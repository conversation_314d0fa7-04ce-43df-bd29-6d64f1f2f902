# AdminController API接口文档

## 接口概述
管理员后台管理系统接口，提供用户管理、图片管理、系统统计等功能。

**基础路径**: `/api/admin`  
**权限要求**: 需要管理员登录（@AdminAuth）

## 认证说明

### 管理员角色
- **超级管理员（role=1）**: 拥有所有权限
- **普通管理员（role=2）**: 拥有基础管理权限

### Token使用
- 登录成功后获得Token
- 请求头中携带：`Token: your-admin-token` 或 `Authorization: Bearer your-admin-token`
- Token有效期：2小时

## 接口列表

### 1. 管理员认证

#### 1.1 管理员登录
- **接口路径**: `POST /api/admin/auth/login`
- **接口描述**: 管理员用户名密码登录
- **权限要求**: 无

**请求参数**:
```json
{
    "username": "admin",
    "password": "123456"
}
```

**成功响应**:
```json
{
    "code": 200,
    "message": "登录成功",
    "data": {
        "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
        "adminId": 1,
        "username": "admin",
        "realName": "系统管理员",
        "role": 1,
        "roleDesc": "超级管理员",
        "expiresIn": 7200
    },
    "timestamp": 1690617600000
}
```

---

### 2. 用户管理

#### 2.1 分页查询用户列表
- **接口路径**: `GET /api/admin/users`
- **接口描述**: 分页查询用户列表，支持搜索和筛选
- **权限要求**: 管理员登录

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 | 默认值 |
|--------|------|------|------|--------|
| page | Integer | 否 | 页码 | 1 |
| size | Integer | 否 | 每页大小 | 10 |
| keyword | String | 否 | 搜索关键词（昵称、手机号、邮箱） | 无 |
| status | Integer | 否 | 用户状态 0:禁用 1:正常 | 无 |

**成功响应**:
```json
{
    "code": 200,
    "message": "操作成功",
    "data": {
        "records": [
            {
                "id": 1,
                "openId": "oABC123DEF456GHI789",
                "nickname": "张三",
                "avatarUrl": "https://wx.qlogo.cn/mmopen/xxx",
                "gender": 1,
                "phone": "13800138000",
                "email": "<EMAIL>",
                "status": 1,
                "statusDesc": "正常",
                "totalPoints": 1000,
                "availablePoints": 800,
                "lockedPoints": 50,
                "consumedPoints": 150,
                "createTime": "2024-07-29 10:30:00",
                "lastLoginTime": "2024-07-29 15:30:00"
            }
        ],
        "total": 100,
        "size": 10,
        "current": 1,
        "pages": 10
    },
    "timestamp": 1690617600000
}
```

#### 2.2 获取用户详情
- **接口路径**: `GET /api/admin/users/{userId}`
- **接口描述**: 获取指定用户的详细信息
- **权限要求**: 管理员登录

#### 2.3 更新用户状态
- **接口路径**: `PUT /api/admin/users/{userId}/status`
- **接口描述**: 启用或禁用用户账号
- **权限要求**: 管理员登录

**请求参数**:
```json
{
    "status": 0,
    "remark": "违规操作，暂时禁用"
}
```

#### 2.4 积分管理操作
- **接口路径**: `POST /api/admin/users/points/manage`
- **接口描述**: 为用户充值或扣除积分
- **权限要求**: 超级管理员

**请求参数**:
```json
{
    "userId": 1,
    "pointsAmount": 100,
    "operationType": 1,
    "remark": "管理员手动充值"
}
```

---

### 3. 图片管理

#### 3.1 分页查询图片列表
- **接口路径**: `GET /api/admin/images`
- **接口描述**: 分页查询所有图片，支持搜索和筛选
- **权限要求**: 管理员登录

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 | 默认值 |
|--------|------|------|------|--------|
| page | Integer | 否 | 页码 | 1 |
| size | Integer | 否 | 每页大小 | 10 |
| keyword | String | 否 | 搜索关键词（文件名、处理类型） | 无 |
| isPublic | Integer | 否 | 是否公开 0:私有 1:公开 | 无 |
| auditStatus | Integer | 否 | 审核状态 0:待审核 1:通过 2:拒绝 | 无 |

**成功响应**:
```json
{
    "code": 200,
    "message": "操作成功",
    "data": {
        "records": [
            {
                "id": 1,
                "taskId": 1,
                "userId": 1,
                "userNickname": "张三",
                "imageUrl": "https://minio.example.com/bucket/image.jpg",
                "thumbnailUrl": "https://minio.example.com/bucket/thumbnail.jpg",
                "originalImageUrl": "https://example.com/original.png",
                "fileName": "processed_image_20240729.jpg",
                "fileSize": 1024000,
                "imageWidth": 1024,
                "imageHeight": 768,
                "imageFormat": "jpg",
                "aspectRatio": "4:3",
                "processType": "beautify",
                "isPublic": 1,
                "publicStatusDesc": "公开",
                "likeCount": 10,
                "viewCount": 100,
                "auditStatus": 1,
                "auditStatusDesc": "审核通过",
                "auditRemark": "内容合规",
                "createTime": "2024-07-29 10:30:00"
            }
        ],
        "total": 500,
        "size": 10,
        "current": 1,
        "pages": 50
    },
    "timestamp": 1690617600000
}
```

#### 3.2 获取图片详情
- **接口路径**: `GET /api/admin/images/{imageId}`
- **接口描述**: 获取指定图片的详细信息
- **权限要求**: 管理员登录

#### 3.3 图片审核
- **接口路径**: `POST /api/admin/images/{imageId}/audit`
- **接口描述**: 审核图片内容，通过或拒绝
- **权限要求**: 管理员登录

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| auditStatus | Integer | 是 | 审核状态 1:通过 2:拒绝 |
| auditRemark | String | 否 | 审核备注 |

**成功响应**:
```json
{
    "code": 200,
    "message": "图片审核通过",
    "data": null,
    "timestamp": 1690617600000
}
```

#### 3.4 删除图片
- **接口路径**: `DELETE /api/admin/images/{imageId}`
- **接口描述**: 删除指定图片
- **权限要求**: 超级管理员

#### 3.5 获取图片统计信息
- **接口路径**: `GET /api/admin/images/stats`
- **接口描述**: 获取图片相关的统计数据
- **权限要求**: 管理员登录

**成功响应**:
```json
{
    "code": 200,
    "message": "操作成功",
    "data": {
        "totalImages": 5000,
        "publicImages": 3000,
        "privateImages": 2000,
        "pendingAuditImages": 50
    },
    "timestamp": 1690617600000
}
```

---

### 4. 系统统计

#### 4.1 获取系统统计信息
- **接口路径**: `GET /api/admin/stats/system`
- **接口描述**: 获取系统全面的统计数据
- **权限要求**: 管理员登录

**成功响应**:
```json
{
    "code": 200,
    "message": "操作成功",
    "data": {
        "userStats": {
            "totalUsers": 1000,
            "todayNewUsers": 10,
            "weekNewUsers": 50,
            "monthNewUsers": 200,
            "activeUsers": 800,
            "normalUsers": 950,
            "disabledUsers": 50
        },
        "imageStats": {
            "totalImages": 5000,
            "todayNewImages": 50,
            "weekNewImages": 300,
            "monthNewImages": 1200,
            "publicImages": 3000,
            "privateImages": 2000,
            "successRate": 95.5
        },
        "pointsStats": {
            "totalPointsIssued": 100000,
            "totalPointsConsumed": 80000,
            "todayPointsIssued": 1000,
            "todayPointsConsumed": 800,
            "avgUserPoints": 150.0
        },
        "systemStatus": {
            "uptime": "7天12小时30分钟",
            "cpuUsage": 25.5,
            "memoryUsage": 60.8,
            "diskUsage": 45.2,
            "databaseStatus": "正常",
            "redisStatus": "正常",
            "minioStatus": "正常"
        }
    },
    "timestamp": 1690617600000
}
```

---

## 使用示例

### JavaScript示例（管理后台）
```javascript
// 管理员登录
fetch('/api/admin/auth/login', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json'
    },
    body: JSON.stringify({
        username: 'admin',
        password: '123456'
    })
})
.then(response => response.json())
.then(data => {
    if (data.code === 200) {
        // 保存Token
        localStorage.setItem('adminToken', data.data.token);
        console.log('登录成功', data.data);
    }
});

// 查询用户列表
fetch('/api/admin/users?page=1&size=10&keyword=张三', {
    method: 'GET',
    headers: {
        'Token': localStorage.getItem('adminToken')
    }
})
.then(response => response.json())
.then(data => {
    if (data.code === 200) {
        console.log('用户列表', data.data);
    }
});

// 禁用用户
fetch('/api/admin/users/123/status', {
    method: 'PUT',
    headers: {
        'Token': localStorage.getItem('adminToken'),
        'Content-Type': 'application/json'
    },
    body: JSON.stringify({
        status: 0,
        remark: '违规操作'
    })
})
.then(response => response.json())
.then(data => {
    if (data.code === 200) {
        console.log('用户已禁用');
    }
});

// 图片审核
fetch('/api/admin/images/456/audit?auditStatus=1&auditRemark=内容合规', {
    method: 'POST',
    headers: {
        'Token': localStorage.getItem('adminToken')
    }
})
.then(response => response.json())
.then(data => {
    if (data.code === 200) {
        console.log('图片审核通过');
    }
});

// 获取系统统计
fetch('/api/admin/stats/system', {
    method: 'GET',
    headers: {
        'Token': localStorage.getItem('adminToken')
    }
})
.then(response => response.json())
.then(data => {
    if (data.code === 200) {
        console.log('系统统计', data.data);
    }
});
```

### cURL示例
```bash
# 管理员登录
curl -X POST "/api/admin/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"123456"}'

# 查询用户列表
curl -X GET "/api/admin/users?page=1&size=10" \
  -H "Token: your-admin-token"

# 禁用用户
curl -X PUT "/api/admin/users/123/status" \
  -H "Token: your-admin-token" \
  -H "Content-Type: application/json" \
  -d '{"status":0,"remark":"违规操作"}'

# 图片审核
curl -X POST "/api/admin/images/456/audit?auditStatus=1&auditRemark=内容合规" \
  -H "Token: your-admin-token"

# 获取系统统计
curl -X GET "/api/admin/stats/system" \
  -H "Token: your-admin-token"
```

---

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 401 | 未登录或Token无效 |
| 403 | 权限不足，需要超级管理员权限 |
| 500 | 用户名或密码错误 |
| 500 | 账号已被禁用 |
| 500 | 用户不存在 |
| 500 | 图片不存在 |
| 500 | 积分不足，无法扣除 |

---

## 注意事项

1. **权限控制**: 
   - 所有接口都需要管理员登录
   - 部分敏感操作需要超级管理员权限
   
2. **Token管理**: 
   - Token有效期2小时
   - 建议定期刷新Token
   
3. **数据安全**: 
   - 密码使用MD5加密存储
   - 敏感操作记录日志
   
4. **操作审计**: 
   - 重要操作都有日志记录
   - 支持操作回溯
   
5. **系统监控**: 
   - 实时监控系统状态
   - 异常情况及时告警

---

**文档版本**: v1.0  
**创建时间**: 2024-07-29  
**更新时间**: 2024-07-29
