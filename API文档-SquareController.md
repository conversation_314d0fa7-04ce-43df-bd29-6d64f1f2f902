# SquareController API接口文档

## 接口概述
图片广场控制器，提供公开图片的浏览、点赞等社交功能。

**基础路径**: `/api/wechat/square`  
**权限要求**: 需要微信用户登录（@WeChatAuth）

## 接口列表

### 1. 分页查询广场图片

#### 接口信息
- **接口路径**: `GET /api/wechat/square/images`
- **接口描述**: 获取公开的图片列表，支持按处理类型和比例筛选
- **权限要求**: 微信用户登录

#### 请求参数
| 参数名 | 类型 | 必填 | 说明 | 默认值 |
|--------|------|------|------|--------|
| page | Integer | 否 | 页码 | 1 |
| size | Integer | 否 | 每页大小 | 10 |
| processType | String | 否 | 处理类型筛选 | 无 |
| aspectRatio | String | 否 | 图片比例筛选 | 无 |

#### 响应结果
**成功响应**:
```json
{
    "code": 200,
    "message": "操作成功",
    "data": {
        "records": [
            {
                "id": 1,
                "imageUrl": "https://minio.example.com/bucket/image.jpg",
                "thumbnailUrl": "https://minio.example.com/bucket/thumbnail.jpg",
                "imageWidth": 1024,
                "imageHeight": 768,
                "aspectRatio": "4:3",
                "processType": "beautify",
                "likeCount": 15,
                "viewCount": 120,
                "userInfo": {
                    "id": 2,
                    "nickname": "张三",
                    "avatarUrl": "https://wx.qlogo.cn/mmopen/xxx"
                },
                "isLiked": false,
                "createTime": "2024-07-29 10:30:00"
            }
        ],
        "total": 50,
        "size": 10,
        "current": 1,
        "pages": 5
    },
    "timestamp": 1690617600000
}
```

**响应参数说明**:
| 参数名 | 类型 | 说明 |
|--------|------|------|
| data.records | Array | 图片记录列表 |
| data.records[].id | Long | 图片ID |
| data.records[].imageUrl | String | 图片URL |
| data.records[].thumbnailUrl | String | 缩略图URL |
| data.records[].imageWidth | Integer | 图片宽度 |
| data.records[].imageHeight | Integer | 图片高度 |
| data.records[].aspectRatio | String | 图片比例 |
| data.records[].processType | String | 处理类型 |
| data.records[].likeCount | Integer | 点赞数 |
| data.records[].viewCount | Integer | 查看数 |
| data.records[].userInfo | Object | 用户信息 |
| data.records[].userInfo.id | Long | 用户ID |
| data.records[].userInfo.nickname | String | 用户昵称 |
| data.records[].userInfo.avatarUrl | String | 用户头像 |
| data.records[].isLiked | Boolean | 当前用户是否已点赞 |
| data.records[].createTime | String | 创建时间 |
| data.total | Long | 总记录数 |
| data.size | Long | 每页大小 |
| data.current | Long | 当前页码 |
| data.pages | Long | 总页数 |

---

### 2. 获取图片详情

#### 接口信息
- **接口路径**: `GET /api/wechat/square/images/{imageId}`
- **接口描述**: 获取指定图片的详细信息，会增加查看数
- **权限要求**: 微信用户登录

#### 请求参数
| 参数名 | 类型 | 位置 | 必填 | 说明 |
|--------|------|------|------|------|
| imageId | Long | Path | 是 | 图片ID |

#### 响应结果
**成功响应**:
```json
{
    "code": 200,
    "message": "操作成功",
    "data": {
        "id": 1,
        "imageUrl": "https://minio.example.com/bucket/image.jpg",
        "thumbnailUrl": "https://minio.example.com/bucket/thumbnail.jpg",
        "imageWidth": 1024,
        "imageHeight": 768,
        "aspectRatio": "4:3",
        "processType": "beautify",
        "likeCount": 15,
        "viewCount": 121,
        "userInfo": {
            "id": 2,
            "nickname": "张三",
            "avatarUrl": "https://wx.qlogo.cn/mmopen/xxx"
        },
        "isLiked": false,
        "createTime": "2024-07-29 10:30:00"
    },
    "timestamp": 1690617600000
}
```

**失败响应**:
```json
{
    "code": 500,
    "message": "图片不存在或未公开",
    "data": null,
    "timestamp": 1690617600000
}
```

#### 业务逻辑
1. 验证图片是否存在且为公开状态
2. 自动增加图片的查看数
3. 查询用户信息和当前用户的点赞状态
4. 返回完整的图片详情

---

### 3. 点赞或取消点赞图片

#### 接口信息
- **接口路径**: `POST /api/wechat/square/images/{imageId}/like`
- **接口描述**: 对指定图片进行点赞或取消点赞操作
- **权限要求**: 微信用户登录

#### 请求参数
**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| imageId | Long | 是 | 图片ID |

**请求体**:
```json
{
    "isLike": true
}
```

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| isLike | Boolean | 是 | 是否点赞 true:点赞 false:取消点赞 |

#### 响应结果
**点赞成功响应**:
```json
{
    "code": 200,
    "message": "点赞成功",
    "data": {
        "imageId": 1,
        "isLiked": true,
        "likeCount": 16
    },
    "timestamp": 1690617600000
}
```

**取消点赞成功响应**:
```json
{
    "code": 200,
    "message": "取消点赞成功",
    "data": {
        "imageId": 1,
        "isLiked": false,
        "likeCount": 15
    },
    "timestamp": 1690617600000
}
```

**失败响应**:
```json
{
    "code": 500,
    "message": "不能给自己的图片点赞",
    "data": null,
    "timestamp": 1690617600000
}
```

**响应参数说明**:
| 参数名 | 类型 | 说明 |
|--------|------|------|
| data.imageId | Long | 图片ID |
| data.isLiked | Boolean | 点赞后的状态 |
| data.likeCount | Integer | 点赞后的总数 |

#### 业务逻辑
1. 验证图片是否存在且为公开状态
2. 验证用户不能给自己的图片点赞
3. 更新或插入点赞记录
4. 更新图片的点赞数统计
5. 自动增加图片的查看数
6. 返回最新的点赞状态和数量

---

## 业务规则说明

### 图片展示规则
1. **公开状态**: 只显示is_public=1的图片
2. **排序规则**: 按创建时间倒序排列
3. **筛选支持**: 支持按处理类型和图片比例筛选
4. **查看统计**: 每次查看详情会自动增加查看数

### 点赞规则
1. **权限限制**: 不能给自己的图片点赞
2. **状态管理**: 支持点赞和取消点赞
3. **数据一致性**: 点赞记录和统计数据保持一致
4. **重复操作**: 重复点赞会覆盖之前的状态

### 用户信息展示
1. **基础信息**: 显示用户昵称和头像
2. **隐私保护**: 不显示用户的敏感信息
3. **实时状态**: 显示当前用户的点赞状态

---

## 使用示例

### JavaScript示例（微信小程序）
```javascript
// 获取广场图片列表
wx.request({
    url: 'https://api.example.com/api/wechat/square/images',
    method: 'GET',
    data: {
        page: 1,
        size: 10,
        processType: 'beautify',
        aspectRatio: '4:3'
    },
    header: {
        'Token': wx.getStorageSync('token')
    },
    success: function(res) {
        if (res.data.code === 200) {
            console.log('广场图片列表', res.data.data);
        }
    }
});

// 获取图片详情
wx.request({
    url: 'https://api.example.com/api/wechat/square/images/123',
    method: 'GET',
    header: {
        'Token': wx.getStorageSync('token')
    },
    success: function(res) {
        if (res.data.code === 200) {
            console.log('图片详情', res.data.data);
        }
    }
});

// 点赞图片
wx.request({
    url: 'https://api.example.com/api/wechat/square/images/123/like',
    method: 'POST',
    header: {
        'Token': wx.getStorageSync('token'),
        'Content-Type': 'application/json'
    },
    data: {
        isLike: true
    },
    success: function(res) {
        if (res.data.code === 200) {
            wx.showToast({
                title: res.data.message,
                icon: 'success'
            });
            console.log('点赞结果', res.data.data);
        }
    }
});

// 取消点赞
wx.request({
    url: 'https://api.example.com/api/wechat/square/images/123/like',
    method: 'POST',
    header: {
        'Token': wx.getStorageSync('token'),
        'Content-Type': 'application/json'
    },
    data: {
        isLike: false
    },
    success: function(res) {
        if (res.data.code === 200) {
            wx.showToast({
                title: res.data.message,
                icon: 'success'
            });
        }
    }
});
```

### cURL示例
```bash
# 获取广场图片列表
curl -X GET "https://api.example.com/api/wechat/square/images?page=1&size=10&processType=beautify" \
  -H "Token: abc123def456ghi789"

# 获取图片详情
curl -X GET "https://api.example.com/api/wechat/square/images/123" \
  -H "Token: abc123def456ghi789"

# 点赞图片
curl -X POST "https://api.example.com/api/wechat/square/images/123/like" \
  -H "Token: abc123def456ghi789" \
  -H "Content-Type: application/json" \
  -d '{"isLike": true}'

# 取消点赞
curl -X POST "https://api.example.com/api/wechat/square/images/123/like" \
  -H "Token: abc123def456ghi789" \
  -H "Content-Type: application/json" \
  -d '{"isLike": false}'
```

---

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 401 | 未登录或Token无效 |
| 500 | 图片不存在或未公开 |
| 500 | 不能给自己的图片点赞 |
| 500 | 点赞操作失败 |
| 500 | 查询广场图片失败 |
| 500 | 获取图片详情失败 |

---

## 注意事项

1. **权限验证**: 所有接口都需要微信用户登录
2. **公开状态**: 只能查看和操作公开状态的图片
3. **点赞限制**: 用户不能给自己的图片点赞
4. **查看统计**: 每次查看详情会自动增加查看数
5. **分页限制**: 每页最多100条记录
6. **实时更新**: 点赞数和查看数实时更新
7. **数据一致性**: 点赞记录和统计数据保持一致

---

**文档版本**: v1.0  
**创建时间**: 2024-07-29  
**更新时间**: 2024-07-29
