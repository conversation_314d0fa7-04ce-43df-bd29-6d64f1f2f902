<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <property name="app.name" value="InspiRealm_system"/>
    <property name="logging.root" value="./logs"/>
    <!-- 控制台 -->
    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
        <encoder charset="UTF-8">
            <pattern>%date [%-5level] [%thread] %logger{80} - %msg[%X{traceId}]%n</pattern>
        </encoder>
    </appender>

    <appender name="ERROR-APPENDER"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${logging.root}/${app.name}/common-error.log</file>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>ERROR</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <FileNamePattern>${logging.root}/${app.name}/common-error.log.%d{yyyy-MM-dd}</FileNamePattern>
            <MaxHistory>30</MaxHistory>
        </rollingPolicy>
        <encoder charset="UTF-8">
            <pattern>%date [%-5level] [%thread] %logger{80} - %msg[%X{traceId}]%n</pattern>
        </encoder>
    </appender>

    <appender name="SYS-WEB-DETAIL-APPENDER" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${logging.root}/${app.name}/common-web-detail.log</file>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>INFO</level>
        </filter>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <FileNamePattern>${logging.root}/${app.name}/common-web-detail.log.%d{yyyy-MM-dd}</FileNamePattern>
            <MaxHistory>10</MaxHistory>
        </rollingPolicy>
        <encoder charset="UTF-8">
            <pattern>%date [%X{loginUserID},%X{loginName},%X{remoteAddr},%X{method},%X{requestURIWithQueryString},%X{userAgent}] %msg[%X{traceId}]%n</pattern>
        </encoder>
    </appender>
    <logger name="org.springframework.jdbc.core.StatementCreatorUtils" level="TRACE" />
    <logger name="SYS-WEB-DETAIL" additivity="false">
        <appender-ref ref="SYS-WEB-DETAIL-APPENDER"/>
        <appender-ref ref="ERROR-APPENDER"/>
        <appender-ref ref="console"/>
    </logger>

    <appender name="WEB-ANALYTICS-APPENDER" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${logging.root}/${app.name}/common-web-analytics.log</file>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>INFO</level>
        </filter>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <FileNamePattern>${logging.root}/${app.name}/common-web-analytics.log.%d{yyyy-MM-dd}</FileNamePattern>
            <MaxHistory>10</MaxHistory>
        </rollingPolicy>
        <encoder charset="UTF-8">
            <pattern>%date %msg[%X{traceId}]%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <logger name="WEB-ANALYTICS" additivity="false">
        <appender-ref ref="WEB-ANALYTICS-APPENDER"/>
        <appender-ref ref="ERROR-APPENDER"/>
        <appender-ref ref="console"/>
    </logger>


    <appender name="DEFAULT-APPENDER" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${logging.root}/${app.name}/common-default.log</file>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>DEBUG</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>NEUTRAL</onMismatch>
        </filter>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>INFO</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>NEUTRAL</onMismatch>
        </filter>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>WARN</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <FileNamePattern>${logging.root}/${app.name}/common-default.log.%d{yyyy-MM-dd}</FileNamePattern>
            <MaxHistory>10</MaxHistory>
        </rollingPolicy>
        <encoder charset="UTF-8">
            <pattern>%date [%-5level] [%thread] %logger{80} - %msg[%X{traceId}]%n</pattern>
        </encoder>
    </appender>

    ​ <logger name="com.jj.dao.mapper" level="debug"/>​



    <root level="INFO">
        <appender-ref ref="DEFAULT-APPENDER"/>
        <appender-ref ref="ERROR-APPENDER"/>
        <appender-ref ref="console"/>
    </root>

</configuration>