# ==========================================
# InspiRealm System - 测试环境配置
# ==========================================

# 应用基础配置
spring.application.name=InspiRealm_system
server.port=9808
server.servlet.context-path=/InspiRealm_system
server.servlet.encoding.charset=UTF-8
server.servlet.encoding.enabled=true
server.servlet.encoding.force=true

# 文件上传配置
spring.servlet.multipart.enabled=true
spring.servlet.multipart.max-file-size=200MB
spring.servlet.multipart.max-request-size=200MB

# ==========================================
# 数据库配置
# ==========================================
spring.datasource.driver-class-name=com.p6spy.engine.spy.P6SpyDriver
spring.datasource.url=****************************************************************************************************************************************************************************************
spring.datasource.username=root
spring.datasource.password=123000aaa...

# MyBatis Plus配置
mybatis-plus.mapper-locations=classpath*:/mapper/**/*.xml
mybatis-plus.type-aliases-package=com.jj.dao.model
mybatis.config-location=classpath:sqlmapconfig.xml

# ==========================================
# Redis缓存配置
# ==========================================
spring.redis.database=2
spring.redis.host=*************
spring.redis.port=6379
spring.redis.password=123000aaa...
spring.redis.timeout=60s
spring.redis.lettuce.pool.max-wait=-1
spring.redis.lettuce.pool.max-active=300
spring.redis.lettuce.pool.max-idle=100
spring.redis.lettuce.pool.min-idle=20

# ==========================================
# 微信小程序配置
# ==========================================
wechat.app-id=wx3be445073804441d
wechat.app-secret=1b6e21546f2384e3f73a04806f46c671

# ==========================================
# 积分系统配置
# ==========================================
points.register-reward=100
points.daily-checkin=10
points.image-process-cost=5

# ==========================================
# AI图片处理配置
# ==========================================
ai.sora-image-api-url=https://yunwu.ai/v1/chat/completions
ai.sora-image-api-key=sk-HsIcesXofJ1XU62bnIG3RHhnXUgJElh425MZsKQlE053bN0v
# AI API超时配置（毫秒）
ai.connect-timeout=6000000
ai.read-timeout=6000000

# ==========================================
# MinIO文件存储配置
# ==========================================
minio.endpoint=https://file.jjxmz.cn
minio.access-key=admin
minio.secret-key=123000aaa...
minio.bucket-name=inspirealm-test
minio.port=443
minio.secure=true

# ==========================================
# 邮件配置
# ==========================================
spring.mail.host=smtp.qq.com
spring.mail.port=587
spring.mail.username=<EMAIL>
spring.mail.password=your_email_password
spring.mail.properties.mail.smtp.auth=true
spring.mail.properties.mail.smtp.starttls.enable=true
spring.mail.properties.mail.smtp.starttls.required=true
spring.mail.default-encoding=UTF-8

# ==========================================
# 应用配置
# ==========================================
app.name=InspiRealm
app.version=1.0.0
app.description=AI视频生成系统

# ==========================================
# Veo3 API配置
# ==========================================
veo3.api.base-url=https://yunwu.ai/v1
veo3.api.key=your_veo3_api_key_here
veo3.api.timeout=30000

# ==========================================
# 其他配置
# ==========================================
jdc.domain=test.inspirealm.com
