package com.jj.config;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.jj.common.constant.CommonConstant;
import com.jj.common.utils.UserInfoUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.stereotype.Component;

import java.util.Date;


@Component
public class MymetaObjectHandler implements MetaObjectHandler {
    @Override
    public void insertFill(MetaObject metaObject) {
        Date date = new Date();
        setFieldValByName("createTime",date,metaObject);
        setFieldValByName("updateTime",date,metaObject);
        setFieldValByName("del", CommonConstant.STATUS_DEL_UN,metaObject);
        if (StringUtils.isNotBlank(UserInfoUtil.getUserId())){
            setFieldValByName("createBy",Long.valueOf(UserInfoUtil.getUserId()),metaObject);
            setFieldValByName("updateBy",Long.valueOf(UserInfoUtil.getUserId()),metaObject);
        }
        setFieldValByName("version", CommonConstant.INIT_VERSION,metaObject);

    }

    @Override
    public void updateFill(MetaObject metaObject) {
        Date date = new Date();
        setFieldValByName("updateTime",date,metaObject);
        if (StringUtils.isNotBlank(UserInfoUtil.getUserId())){
            setFieldValByName("updateBy",Long.valueOf(UserInfoUtil.getUserId()),metaObject);
        }

    }
}
