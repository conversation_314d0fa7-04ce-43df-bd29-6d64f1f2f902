package com.jj.config;

import com.jj.common.constant.CommonConstant;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.ParameterBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.schema.ModelRef;
import springfox.documentation.service.Contact;
import springfox.documentation.service.Parameter;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

import java.util.ArrayList;
import java.util.List;

@Configuration
@EnableSwagger2
@Profile({"dev","test"})
public class Swagger2Config {

    @Bean // swagger2的配置文件，这里可以配置swagger2的一些基本的内容，比如扫描的包等等
    public Docket controllerApi() {
        return new Docket(DocumentationType.SWAGGER_2)
                        .apiInfo(new ApiInfoBuilder()
                        .title("标题：积极小程序后台管理_接口文档")
                        .description("描述：管理")
                        .contact(new Contact("JJ_APPLET", null, null))
                        .version("版本号:1.0").build()).select()
                        .apis(RequestHandlerSelectors.basePackage("com.jj"))
                        .paths(PathSelectors.any()).build()
                        .globalOperationParameters(this.getParameter())
                ;
    }

    private List<Parameter> getParameter(){

        ParameterBuilder parameterBuilder = new ParameterBuilder();

        List<Parameter> pars = new ArrayList<>();
        parameterBuilder.name(CommonConstant.ACCESS_TOKEN).description("token令牌").modelRef(new ModelRef("string"))
                .parameterType("header").required(false).build();
        pars.add(parameterBuilder.build());
        return pars;
    }

}