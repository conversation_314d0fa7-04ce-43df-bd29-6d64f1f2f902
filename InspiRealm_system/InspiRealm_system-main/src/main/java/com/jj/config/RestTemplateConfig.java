package com.jj.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.ClientHttpRequestFactory;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

/**
 * RestTemplate配置类
 * 专门用于处理长时间的API调用，如AI图片生成
 * 
 * <AUTHOR>
 * @date 2024-07-30
 */
@Configuration
public class RestTemplateConfig {

    @Value("${ai.connect-timeout:6000000}")
    private int aiConnectTimeout;

    @Value("${ai.read-timeout:6000000}")
    private int aiReadTimeout;

    /**
     * 创建用于AI API调用的RestTemplate
     * 配置长超时时间以适应AI图片生成的长时间处理
     */
    @Bean("aiRestTemplate")
    public RestTemplate aiRestTemplate() {
        return new RestTemplate(aiClientHttpRequestFactory());
    }

    /**
     * 创建默认的RestTemplate（保持原有的短超时时间）
     */
    @Bean("defaultRestTemplate")
    public RestTemplate defaultRestTemplate() {
        return new RestTemplate(defaultClientHttpRequestFactory());
    }

    /**
     * AI API专用的HTTP请求工厂
     * 使用配置文件中的超时时间，默认：连接超时30秒，读取超时10分钟
     */
    @Bean
    public ClientHttpRequestFactory aiClientHttpRequestFactory() {
        SimpleClientHttpRequestFactory factory = new SimpleClientHttpRequestFactory();

        // 使用配置文件中的连接超时时间
        factory.setConnectTimeout(aiConnectTimeout);

        // 使用配置文件中的读取超时时间
        // AI图片生成通常需要3-5分钟，默认设置10分钟确保足够的处理时间
        factory.setReadTimeout(aiReadTimeout);

        // 添加日志输出，确认超时配置
        System.out.println("=== AI RestTemplate 超时配置 ===");
        System.out.println("连接超时: " + aiConnectTimeout + "ms (" + (aiConnectTimeout/1000) + "秒)");
        System.out.println("读取超时: " + aiReadTimeout + "ms (" + (aiReadTimeout/1000) + "秒)");
        System.out.println("================================");

        return factory;
    }

    /**
     * 默认的HTTP请求工厂
     * 保持较短的超时时间用于一般API调用
     */
    @Bean
    public ClientHttpRequestFactory defaultClientHttpRequestFactory() {
        SimpleClientHttpRequestFactory factory = new SimpleClientHttpRequestFactory();
        
        // 连接超时：10秒
        factory.setConnectTimeout(10 * 1000);
        
        // 读取超时：30秒
        factory.setReadTimeout(30 * 1000);
        
        return factory;
    }
}
