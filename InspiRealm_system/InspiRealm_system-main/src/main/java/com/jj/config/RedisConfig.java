package com.jj.config;

import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.jsontype.impl.LaissezFaireSubTypeValidator;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;

/**
 * <AUTHOR>
 */
@Configuration
public class RedisConfig {

    /**
     * 通用 RedisTemplate 配置（支持存储任意类型）
     */
    @Bean
    public RedisTemplate<String, Object> redisTemplate(RedisConnectionFactory factory) {
        RedisTemplate<String, Object> template = new RedisTemplate<>();
        template.setConnectionFactory(factory);

        // 创建 JSON 序列化器（带类型信息）
        GenericJackson2JsonRedisSerializer serializer = new GenericJackson2JsonRedisSerializer(objectMapper());

        // Key 使用 String 序列化
        template.setKeySerializer(new StringRedisSerializer());
        template.setHashKeySerializer(new StringRedisSerializer());

        // Value 使用 JSON 序列化（自动处理 LocalDateTime）
        template.setValueSerializer(serializer);
        template.setHashValueSerializer(serializer);

        // 特殊数据结构序列化配置（可选）
        template.setStringSerializer(new StringRedisSerializer());          // String 类型
        template.setDefaultSerializer(serializer);                          // 默认序列化器

        template.afterPropertiesSet();
        return template;
    }

    /**
     * 专门处理 String 类型操作的 RedisTemplate（纯字符串场景）
     */
    @Bean
    public StringRedisTemplate stringRedisTemplate(RedisConnectionFactory factory) {
        StringRedisTemplate template = new StringRedisTemplate();
        template.setConnectionFactory(factory);
        return template;
    }

    /**
     * 定制 ObjectMapper（解决 LocalDateTime 等序列化问题）
     */
    private ObjectMapper objectMapper() {
        ObjectMapper mapper = new ObjectMapper();
        // 添加类型信息（反序列化时自动识别）
        mapper.activateDefaultTyping(
                LaissezFaireSubTypeValidator.instance,
                ObjectMapper.DefaultTyping.NON_FINAL,
                JsonTypeInfo.As.PROPERTY
        );
        // 可选配置：解决 LocalDateTime 等 Java8 时间类型序列化
        // mapper.registerModule(new JavaTimeModule());
        // mapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);

        // 忽略未知属性
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        return mapper;
    }


}
