# InspiRealm AI图片美化系统 - 技术开发计划文档

## 1. 项目目录结构规划

### 1.1 当前项目结构
```
InspiRealm_system/
├── InspiRealm_system-main/          # 主启动模块
│   ├── src/main/java/com/jj/
│   │   ├── main/WebApplication.java # 主启动类
│   │   └── config/                  # 配置类
├── InspiRealm_system-common/        # 公共模块
│   ├── src/main/java/com/jj/common/
│   │   ├── response/JsonResult.java # 统一响应格式
│   │   ├── exception/              # 异常处理
│   │   └── utils/                  # 工具类
├── InspiRealm_system-core/          # 核心业务模块
│   ├── src/main/java/com/jj/core/
│   │   ├── annotation/             # 权限注解
│   │   ├── auth/                   # 权限认证
│   │   ├── domain/                 # 领域模型
│   │   └── service/                # 业务服务
├── InspiRealm_system-dao/           # 数据访问模块
│   ├── src/main/java/com/jj/dao/
│   │   ├── mapper/                 # MyBatis Mapper
│   │   └── model/                  # 数据模型
│   └── src/main/resources/
│       └── mapper/                 # MyBatis XML
├── InspiRealm_system-web/           # Web控制模块
│   ├── src/main/java/com/jj/web/
│   │   ├── aspect/                 # AOP切面
│   │   ├── wechat/                 # 微信小程序接口
│   │   └── admin/                  # 管理后台接口
├── InspiRealm_system-shared/        # 共享模块
├── InspiRealm_system-integration/   # 集成模块
└── database.sql                     # 数据库建表语句
```

### 1.2 新增目录结构规划
```
InspiRealm_system-core/src/main/java/com/jj/core/
├── domain/
│   ├── dto/                        # 数据传输对象
│   │   ├── wechat/                # 微信相关DTO
│   │   ├── admin/                 # 管理员相关DTO
│   │   ├── user/                  # 用户相关DTO
│   │   ├── points/                # 积分相关DTO
│   │   └── image/                 # 图片相关DTO
│   ├── vo/                        # 视图对象
│   └── enums/                     # 枚举类
├── service/
│   ├── user/                      # 用户服务
│   ├── points/                    # 积分服务
│   ├── image/                     # 图片服务
│   ├── task/                      # 任务服务
│   └── admin/                     # 管理员服务
└── manager/                       # 外部服务管理
    ├── WeChatManager.java         # 微信API管理
    ├── SoraImageManager.java      # Sora图片API管理
    └── MinioManager.java          # MinIO文件管理

InspiRealm_system-dao/src/main/java/com/jj/dao/
├── model/
│   ├── user/                      # 用户相关实体
│   ├── points/                    # 积分相关实体
│   ├── image/                     # 图片相关实体
│   └── admin/                     # 管理员相关实体
└── mapper/
    ├── UserInfoMapper.java
    ├── UserPointsMapper.java
    ├── ImageProcessTaskMapper.java
    ├── ProcessedImageMapper.java
    └── AdminUserMapper.java

InspiRealm_system-web/src/main/java/com/jj/web/
├── wechat/                        # 微信小程序端接口
│   ├── WeChatAuthController.java
│   ├── UserController.java
│   ├── PointsController.java
│   ├── ImageProcessController.java
│   └── SquareController.java
└── admin/                         # 管理后台接口
    ├── AdminAuthController.java
    ├── AdminUserController.java
    ├── AdminTaskController.java
    └── AdminStatisticsController.java
```

## 2. 代码分层架构设计

### 2.1 分层架构说明
```
┌─────────────────────────────────────┐
│           Web Layer                 │  # Controller层：接口控制
│  - WeChatController                 │
│  - AdminController                  │
│  - 权限验证切面                      │
├─────────────────────────────────────┤
│         Service Layer               │  # Service层：业务逻辑
│  - UserService                      │
│  - PointsService                    │
│  - ImageService                     │
│  - TaskService                      │
├─────────────────────────────────────┤
│         Manager Layer               │  # Manager层：外部服务
│  - WeChatManager                    │
│  - SoraImageManager                 │
│  - MinioManager                     │
├─────────────────────────────────────┤
│           DAO Layer                 │  # DAO层：数据访问
│  - Mapper接口                       │
│  - 实体类                           │
├─────────────────────────────────────┤
│        Infrastructure               │  # 基础设施层
│  - Redis缓存                        │
│  - MySQL数据库                      │
│  - MinIO文件存储                     │
└─────────────────────────────────────┘
```

### 2.2 核心设计原则
1. **单一职责原则**：每个类只负责一个功能
2. **依赖倒置原则**：高层模块不依赖低层模块
3. **开闭原则**：对扩展开放，对修改关闭
4. **接口隔离原则**：使用多个专门的接口
5. **里氏替换原则**：子类可以替换父类

### 2.3 数据流转设计
```
Request → Controller → Service → Manager/DAO → Database/External API
                ↓
Response ← JsonResult ← VO/DTO ← Entity ← Database/External API
```

## 3. 开发时间节点安排

### 3.1 第一周：基础架构搭建（已完成）
- [x] 数据库设计与建表
- [x] 权限控制架构设计
- [x] 项目结构规划

### 3.2 第二周：用户层面接口开发
#### Day 1-2: 用户认证模块
- [ ] WeChatAuthController开发
- [ ] 微信登录API集成
- [ ] 用户信息管理Service

#### Day 3-4: 积分管理模块
- [ ] PointsController开发
- [ ] 积分安全机制实现
- [ ] 积分流水记录

#### Day 5-7: 图片处理模块
- [ ] ImageProcessController开发
- [ ] Sora Image API集成
- [ ] 异步任务处理机制

### 3.3 第三周：广场功能和管理后台
#### Day 1-2: 图片广场功能
- [ ] SquareController开发
- [ ] 图片展示和社交功能
- [ ] MinIO图片代理访问

#### Day 3-5: 管理后台接口
- [ ] AdminAuthController开发
- [ ] AdminUserController开发
- [ ] AdminTaskController开发
- [ ] AdminStatisticsController开发

#### Day 6-7: 系统优化和测试
- [ ] 接口性能优化
- [ ] 异常处理完善
- [ ] 单元测试编写

### 3.4 第四周：文档和部署
#### Day 1-3: API文档完善
- [ ] Swagger文档优化
- [ ] 接口测试用例
- [ ] 部署文档编写

#### Day 4-7: 系统测试和上线
- [ ] 集成测试
- [ ] 性能测试
- [ ] 生产环境部署

## 4. 测试策略制定

### 4.1 单元测试策略
```
测试覆盖率目标：80%以上
测试框架：JUnit 5 + Mockito
测试重点：
- Service层业务逻辑测试
- 权限验证逻辑测试
- 积分安全机制测试
- 外部API调用测试
```

### 4.2 集成测试策略
```
测试工具：Postman + Newman
测试环境：独立测试环境
测试内容：
- 完整业务流程测试
- 接口联调测试
- 数据一致性测试
- 并发场景测试
```

### 4.3 性能测试策略
```
测试工具：JMeter
测试指标：
- 接口响应时间 < 500ms
- 并发用户数 > 1000
- 系统吞吐量 > 100 TPS
- 错误率 < 0.1%
```

## 5. 开发规范要求

### 5.1 代码规范
- 使用阿里巴巴Java开发手册规范
- 类名使用大驼峰命名法
- 方法名和变量名使用小驼峰命名法
- 常量使用全大写下划线分隔
- 包名使用小写字母

### 5.2 注释规范
- 类和接口必须有JavaDoc注释
- 公共方法必须有注释说明
- 复杂业务逻辑必须有行内注释
- 注释内容要准确、简洁、有意义

### 5.3 异常处理规范
- 统一使用BizException处理业务异常
- 不允许捕获Exception后不处理
- 异常信息要对用户友好
- 记录详细的错误日志

### 5.4 数据库操作规范
- 使用MyBatis-Plus进行数据库操作
- 禁止使用SELECT *查询
- 必须使用索引进行查询
- 事务边界要清晰明确

## 6. 技术风险评估

### 6.1 高风险项
1. **Sora Image API稳定性**
   - 风险：第三方API不稳定
   - 应对：实现重试机制和降级策略

2. **积分安全机制**
   - 风险：并发场景下积分计算错误
   - 应对：使用数据库锁和Redis分布式锁

3. **文件存储性能**
   - 风险：大量图片上传下载影响性能
   - 应对：使用CDN加速和异步处理

### 6.2 中风险项
1. **微信API调用限制**
   - 风险：API调用频率限制
   - 应对：实现调用频率控制

2. **数据库性能**
   - 风险：大量数据查询影响性能
   - 应对：合理设计索引和分页查询

## 7. 部署架构设计

### 7.1 开发环境
- 单机部署
- 内存数据库H2
- 本地文件存储

### 7.2 测试环境
- Docker容器部署
- MySQL + Redis
- MinIO文件存储

### 7.3 生产环境
- Kubernetes集群部署
- MySQL主从 + Redis集群
- MinIO集群 + CDN加速

---

**文档版本**：v1.0  
**创建时间**：2024-07-29  
**负责人**：开发团队
