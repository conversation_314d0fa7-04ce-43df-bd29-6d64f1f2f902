package com.jj.core.service.user;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jj.common.exception.BizException;
import com.jj.core.domain.dto.user.UpdateUserInfoReqDTO;
import com.jj.core.domain.dto.user.UserProfileDTO;
import com.jj.core.domain.dto.square.SquareImageDTO;
import com.jj.core.manager.MinioManager;
import com.jj.dao.mapper.UserInfoMapper;
import com.jj.dao.mapper.UserPointsMapper;
import com.jj.dao.mapper.ImageLikeMapper;
import com.jj.dao.mapper.ProcessedImageMapper;
import com.jj.dao.model.points.UserPoints;
import com.jj.dao.model.user.UserInfo;
import com.jj.dao.model.square.ImageLike;
import com.jj.dao.model.image.ProcessedImage;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jj.core.utils.DateFormatUtil;

import java.time.format.DateTimeFormatter;

/**
 * 用户服务
 * 
 * <AUTHOR>
 * @date 2024-07-29
 */
@Service
public class UserService {
    
    private static final Logger logger = LoggerFactory.getLogger(UserService.class);
    
    @Autowired
    private UserInfoMapper userInfoMapper;
    
    @Autowired
    private UserPointsMapper userPointsMapper;

    @Autowired
    private ImageLikeMapper imageLikeMapper;

    @Autowired
    private ProcessedImageMapper processedImageMapper;

    @Autowired
    private ApplicationContext applicationContext;

    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    
    /**
     * 获取用户资料
     * 
     * @param userId 用户ID
     * @return 用户资料
     */
    public UserProfileDTO getUserProfile(Long userId) {
        try {
            logger.info("获取用户资料，userId: {}", userId);
            
            // 查询用户基础信息
            UserInfo userInfo = userInfoMapper.selectById(userId);
            if (userInfo == null) {
                throw new BizException("用户不存在");
            }
            
            // 查询用户积分信息
            UserPoints userPoints = userPointsMapper.selectByUserId(userId);
            
            // 构建用户资料DTO
            UserProfileDTO profileDTO = new UserProfileDTO();
            profileDTO.setId(userInfo.getId());
            profileDTO.setOpenId(userInfo.getOpenId());
            profileDTO.setNickname(userInfo.getNickname());
            profileDTO.setAvatarUrl(userInfo.getAvatarUrl());
            profileDTO.setGender(userInfo.getGender());
            profileDTO.setCountry(userInfo.getCountry());
            profileDTO.setProvince(userInfo.getProvince());
            profileDTO.setCity(userInfo.getCity());
            profileDTO.setPhone(userInfo.getPhone());
            profileDTO.setEmail(userInfo.getEmail());
            profileDTO.setStatus(userInfo.getStatus());
            
            // 设置时间信息
            if (userInfo.getCreateTime() != null) {
                profileDTO.setCreateTime(DateFormatUtil.format(userInfo.getCreateTime()));
            }
            if (userInfo.getLastLoginTime() != null) {
                profileDTO.setLastLoginTime(DateFormatUtil.format(userInfo.getLastLoginTime()));
            }
            
            // 设置积分信息
            if (userPoints != null) {
                UserProfileDTO.PointsInfoDTO pointsInfo = new UserProfileDTO.PointsInfoDTO();
                pointsInfo.setTotalPoints(userPoints.getTotalPoints());
                pointsInfo.setAvailablePoints(userPoints.getAvailablePoints());
                pointsInfo.setLockedPoints(userPoints.getLockedPoints());
                pointsInfo.setConsumedPoints(userPoints.getConsumedPoints());
                profileDTO.setPointsInfo(pointsInfo);
            }
            
            logger.info("获取用户资料成功，userId: {}", userId);
            return profileDTO;
            
        } catch (BizException e) {
            logger.error("获取用户资料失败：{}", e.getMessage());
            throw e;
        } catch (Exception e) {
            logger.error("获取用户资料异常", e);
            throw new BizException("获取用户资料失败");
        }
    }
    
    /**
     * 更新用户信息
     * 
     * @param userId 用户ID
     * @param reqDTO 更新请求
     * @return 更新后的用户资料
     */
    @Transactional(rollbackFor = Exception.class)
    public UserProfileDTO updateUserInfo(Long userId, UpdateUserInfoReqDTO reqDTO) {
        try {
            logger.info("更新用户信息，userId: {}, reqDTO: {}", userId, reqDTO);
            
            // 查询用户信息
            UserInfo userInfo = userInfoMapper.selectById(userId);
            if (userInfo == null) {
                throw new BizException("用户不存在");
            }
            
            // 检查用户状态
            if (userInfo.getStatus() != 1) {
                throw new BizException("用户状态异常，无法更新信息");
            }
            
            // 更新用户信息
            boolean needUpdate = false;
            
            if (StringUtils.isNotBlank(reqDTO.getNickname()) && !"微信用户".equals(reqDTO.getNickname()) && !reqDTO.getNickname().equals(userInfo.getNickname())) {
                userInfo.setNickname(reqDTO.getNickname());
                needUpdate = true;
            }
            
            // 处理头像更新 - 优先处理avatarBase64，其次处理avatarUrl
            String newAvatarUrl = null;

            // 优先处理Base64头像
            if (reqDTO.getAvatarBase64() != null && !reqDTO.getAvatarBase64().trim().isEmpty()) {
                try {
                    logger.info("检测到Base64头像，开始上传到MinIO，userId: {}", userId);
                    MinioManager minioManager = applicationContext.getBean(com.jj.core.manager.MinioManager.class);
                    MinioManager.MinioUploadResult uploadResult = minioManager.uploadImageFromBase64(reqDTO.getAvatarBase64(), userId);

                    if (uploadResult.isSuccess()) {
                        newAvatarUrl = uploadResult.getImageUrl();
                        logger.info("Base64头像上传成功，userId: {}, avatarUrl: {}", userId, newAvatarUrl);
                    } else {
                        throw new BizException("头像上传失败");
                    }
                } catch (Exception e) {
                    logger.error("Base64头像上传失败，userId: {}", userId, e);
                    throw new BizException("头像上传失败: " + e.getMessage());
                }
            }
            // 如果没有Base64头像，处理avatarUrl
            else if (reqDTO.getAvatarUrl() != null && !reqDTO.getAvatarUrl().equals(userInfo.getAvatarUrl())) {
                String avatarUrl = reqDTO.getAvatarUrl();

                // 检查是否为Base64格式的头像
                if (avatarUrl.startsWith("data:image/")) {
                    try {
                        logger.info("检测到Base64头像URL，开始上传到MinIO，userId: {}", userId);
                        MinioManager minioManager = applicationContext.getBean(com.jj.core.manager.MinioManager.class);
                        MinioManager.MinioUploadResult uploadResult = minioManager.uploadImageFromBase64(avatarUrl, userId);

                        if (uploadResult.isSuccess()) {
                            newAvatarUrl = uploadResult.getImageUrl();
                            logger.info("Base64头像URL上传成功，userId: {}, avatarUrl: {}", userId, newAvatarUrl);
                        } else {
                            throw new BizException("头像上传失败");
                        }
                    } catch (Exception e) {
                        logger.error("Base64头像URL上传失败，userId: {}", userId, e);
                        throw new BizException("头像上传失败: " + e.getMessage());
                    }
                } else {
                    // 普通URL直接使用
                    newAvatarUrl = avatarUrl;
                }
            }

            // 如果有新的头像URL且与当前不同，则更新
            if (StringUtils.isNotBlank(newAvatarUrl) && !newAvatarUrl.equals(userInfo.getAvatarUrl())) {
                userInfo.setAvatarUrl(newAvatarUrl);
                needUpdate = true;
            }
            
            if (reqDTO.getGender() != null && !reqDTO.getGender().equals(userInfo.getGender())) {
                userInfo.setGender(reqDTO.getGender());
                needUpdate = true;
            }
            
            if (reqDTO.getPhone() != null && !reqDTO.getPhone().equals(userInfo.getPhone())) {
                // 检查手机号是否已被其他用户使用
                if (isPhoneExists(reqDTO.getPhone(), userId)) {
                    throw new BizException("手机号已被其他用户使用");
                }
                userInfo.setPhone(reqDTO.getPhone());
                needUpdate = true;
            }
            
            if (reqDTO.getEmail() != null && !reqDTO.getEmail().equals(userInfo.getEmail())) {
                // 检查邮箱是否已被其他用户使用
                if (isEmailExists(reqDTO.getEmail(), userId)) {
                    throw new BizException("邮箱已被其他用户使用");
                }
                userInfo.setEmail(reqDTO.getEmail());
                needUpdate = true;
            }
            
            // 如果有变更，执行更新
            if (needUpdate) {
                int updateResult = userInfoMapper.updateById(userInfo);
                if (updateResult <= 0) {
                    throw new BizException("更新用户信息失败");
                }
                logger.info("用户信息更新成功，userId: {}", userId);
            } else {
                logger.info("用户信息无变更，userId: {}", userId);
            }
            
            // 返回更新后的用户资料
            return getUserProfile(userId);
            
        } catch (BizException e) {
            logger.error("更新用户信息失败：{}", e.getMessage());
            throw e;
        } catch (Exception e) {
            logger.error("更新用户信息异常", e);
            throw new BizException("更新用户信息失败");
        }
    }
    
    /**
     * 检查手机号是否已存在
     * 
     * @param phone 手机号
     * @param excludeUserId 排除的用户ID
     * @return 是否存在
     */
    private boolean isPhoneExists(String phone, Long excludeUserId) {
        // 这里可以添加具体的查询逻辑
        // 暂时返回false，实际项目中需要实现
        return false;
    }
    
    /**
     * 检查邮箱是否已存在
     *
     * @param email 邮箱
     * @param excludeUserId 排除的用户ID
     * @return 是否存在
     */
    private boolean isEmailExists(String email, Long excludeUserId) {
        // 这里可以添加具体的查询逻辑
        // 暂时返回false，实际项目中需要实现
        return false;
    }

    /**
     * 获取用户点赞记录
     *
     * @param userId 用户ID
     * @param page 页码
     * @param size 每页大小
     * @return 用户点赞的图片列表
     */
    public IPage<SquareImageDTO> getUserLikes(Long userId, Integer page, Integer size) {
        logger.info("获取用户点赞记录，userId: {}, page: {}, size: {}", userId, page, size);

        try {
            // 分页查询用户点赞的图片
            Page<ProcessedImage> pageParam = new Page<>(page, size);

            // 构建查询条件：查询用户点赞过的公开、审核通过、未删除的图片
            QueryWrapper<ProcessedImage> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("is_public", 1)
                       .eq("audit_status", 1)
                       .eq("del", 0)
                       .inSql("id", String.format("SELECT image_id FROM image_like WHERE user_id = %d AND like_status = 1 AND del = 0", userId))
                       .orderByDesc("create_time");

            IPage<ProcessedImage> pageResult = processedImageMapper.selectPage(pageParam, queryWrapper);

            // 转换为DTO
            IPage<SquareImageDTO> result = pageResult.convert(image -> {
                SquareImageDTO dto = new SquareImageDTO();
                dto.setId(image.getId());
                dto.setImageUrl(image.getImageUrl());
                dto.setThumbnailUrl(image.getThumbnailUrl());
                dto.setAspectRatio(image.getAspectRatio());
                dto.setProcessType(image.getProcessType());
                dto.setViewCount(image.getViewCount());
                dto.setLikeCount(image.getLikeCount());
                dto.setIsLiked(true); // 这里都是用户点赞过的
                dto.setCreateTime(DateFormatUtil.format(image.getCreateTime()));

                // 获取图片作者信息
                UserInfo author = userInfoMapper.selectById(image.getUserId());
                if (author != null) {
                   SquareImageDTO.UserInfoDTO userInfo = new SquareImageDTO.UserInfoDTO();
                    userInfo.setNickname(author.getNickname());
                    userInfo.setAvatarUrl(author.getAvatarUrl());
                    dto.setUserInfo(userInfo);
                }

                return dto;
            });

            logger.info("获取用户点赞记录成功，userId: {}, total: {}", userId, result.getTotal());
            return result;

        } catch (Exception e) {
            logger.error("获取用户点赞记录失败，userId: {}", userId, e);
            throw new BizException("获取点赞记录失败");
        }
    }
}
