package com.jj.core.auth;

import java.io.Serializable;

/**
 * 管理员Token信息
 * 
 * <AUTHOR>
 * @date 2024-07-29
 */
public class AdminTokenInfo implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 管理员ID
     */
    private Long adminId;
    
    /**
     * 用户名
     */
    private String username;

    /**
     * 角色 1:超级管理员 2:普通管理员
     */
    private Integer role;

    /**
     * Token类型
     */
    private String tokenType;
    
    /**
     * 创建时间
     */
    private Long createTime;
    
    public Long getAdminId() {
        return adminId;
    }
    
    public void setAdminId(Long adminId) {
        this.adminId = adminId;
    }
    
    public String getUsername() {
        return username;
    }
    
    public void setUsername(String username) {
        this.username = username;
    }

    public Integer getRole() {
        return role;
    }

    public void setRole(Integer role) {
        this.role = role;
    }

    public String getTokenType() {
        return tokenType;
    }
    
    public void setTokenType(String tokenType) {
        this.tokenType = tokenType;
    }
    
    public Long getCreateTime() {
        return createTime;
    }
    
    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }
    
    @Override
    public String toString() {
        return "AdminTokenInfo{" +
                "adminId=" + adminId +
                ", username='" + username + '\'' +
                ", role=" + role +
                ", tokenType='" + tokenType + '\'' +
                ", createTime=" + createTime +
                '}';
    }
}
