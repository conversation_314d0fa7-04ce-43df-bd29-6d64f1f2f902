package com.jj.core.domain.dto.image;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 任务状态DTO
 * 
 * <AUTHOR>
 * @date 2024-07-29
 */
@ApiModel(description = "任务状态")
public class TaskStatusDTO {
    
    /**
     * 任务编号
     */
    @ApiModelProperty(value = "任务编号", example = "TASK_20240729_001")
    private String taskNo;
    
    /**
     * 任务状态 1:待处理 2:处理中 3:成功 4:失败 5:取消
     */
    @ApiModelProperty(value = "任务状态", example = "3")
    private Integer status;
    
    /**
     * 任务状态描述
     */
    @ApiModelProperty(value = "任务状态描述", example = "处理成功")
    private String statusDesc;
    
    /**
     * 进度百分比
     */
    @ApiModelProperty(value = "进度百分比", example = "100")
    private Integer progress;
    
    /**
     * 错误信息
     */
    @ApiModelProperty(value = "错误信息", example = "")
    private String errorMessage;
    
    /**
     * 处理后图片信息
     */
    @ApiModelProperty(value = "处理后图片信息")
    private ProcessedImageDTO processedImage;
    
    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间", example = "2024-07-29 10:30:00")
    private String startTime;
    
    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间", example = "2024-07-29 10:30:30")
    private String endTime;
    
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间", example = "2024-07-29 10:30:00")
    private String createTime;
    
    public String getTaskNo() {
        return taskNo;
    }
    
    public void setTaskNo(String taskNo) {
        this.taskNo = taskNo;
    }
    
    public Integer getStatus() {
        return status;
    }
    
    public void setStatus(Integer status) {
        this.status = status;
    }
    
    public String getStatusDesc() {
        return statusDesc;
    }
    
    public void setStatusDesc(String statusDesc) {
        this.statusDesc = statusDesc;
    }
    
    public Integer getProgress() {
        return progress;
    }
    
    public void setProgress(Integer progress) {
        this.progress = progress;
    }
    
    public String getErrorMessage() {
        return errorMessage;
    }
    
    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }
    
    public ProcessedImageDTO getProcessedImage() {
        return processedImage;
    }
    
    public void setProcessedImage(ProcessedImageDTO processedImage) {
        this.processedImage = processedImage;
    }
    
    public String getStartTime() {
        return startTime;
    }
    
    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }
    
    public String getEndTime() {
        return endTime;
    }
    
    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }
    
    public String getCreateTime() {
        return createTime;
    }
    
    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }
    
    @Override
    public String toString() {
        return "TaskStatusDTO{" +
                "taskNo='" + taskNo + '\'' +
                ", status=" + status +
                ", statusDesc='" + statusDesc + '\'' +
                ", progress=" + progress +
                ", errorMessage='" + errorMessage + '\'' +
                ", processedImage=" + processedImage +
                ", startTime='" + startTime + '\'' +
                ", endTime='" + endTime + '\'' +
                ", createTime='" + createTime + '\'' +
                '}';
    }
}
