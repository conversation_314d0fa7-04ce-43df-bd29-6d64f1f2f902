package com.jj.core.domain.enums;

/**
 * 积分交易类型枚举
 * 
 * <AUTHOR>
 * @date 2024-07-29
 */
public enum PointsTransactionTypeEnum {
    
    /**
     * 获得积分
     */
    EARN(1, "获得积分"),
    
    /**
     * 消费积分
     */
    CONSUME(2, "消费积分"),
    
    /**
     * 锁定积分
     */
    LOCK(3, "锁定积分"),
    
    /**
     * 解锁积分
     */
    UNLOCK(4, "解锁积分"),
    
    /**
     * 回退积分
     */
    REFUND(5, "回退积分");
    
    private final Integer code;
    private final String desc;
    
    PointsTransactionTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    
    public Integer getCode() {
        return code;
    }
    
    public String getDesc() {
        return desc;
    }
    
    /**
     * 根据代码获取枚举
     * 
     * @param code 代码
     * @return 枚举
     */
    public static PointsTransactionTypeEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (PointsTransactionTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
    
    /**
     * 根据代码获取描述
     * 
     * @param code 代码
     * @return 描述
     */
    public static String getDescByCode(Integer code) {
        PointsTransactionTypeEnum type = getByCode(code);
        return type != null ? type.getDesc() : "未知类型";
    }
}
