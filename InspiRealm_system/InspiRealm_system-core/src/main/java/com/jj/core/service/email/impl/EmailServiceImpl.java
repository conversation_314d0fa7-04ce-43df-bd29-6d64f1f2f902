package com.jj.core.service.email.impl;

import com.jj.core.service.email.EmailService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Service;

import javax.mail.internet.MimeMessage;

/**
 * 邮件发送服务实现类
 *
 * <AUTHOR>
 * @date 2025-01-05
 */
@Service
public class EmailServiceImpl implements EmailService {

    private static final Logger logger = LoggerFactory.getLogger(EmailServiceImpl.class);

    @Autowired
    private JavaMailSender mailSender;

    @Value("${spring.mail.username:<EMAIL>}")
    private String fromEmail;

    @Value("${app.name:InspiRealm}")
    private String appName;

    @Override
    public void sendVerificationCode(String email, String code, Integer type) {
        logger.info("发送验证码邮件，邮箱: {}, 类型: {}", email, type);

        try {
            String subject = getVerificationSubject(type);
            String content = buildVerificationContent(code, type);

            sendHtmlEmail(email, subject, content);

            logger.info("验证码邮件发送成功，邮箱: {}", email);

        } catch (Exception e) {
            logger.error("验证码邮件发送失败，邮箱: {}", email, e);
            throw new RuntimeException("邮件发送失败: " + e.getMessage());
        }
    }

    @Override
    public void sendSimpleEmail(String to, String subject, String content) {
        logger.info("发送普通邮件，收件人: {}, 主题: {}", to, subject);

        try {
            SimpleMailMessage message = new SimpleMailMessage();
            message.setFrom(fromEmail);
            message.setTo(to);
            message.setSubject(subject);
            message.setText(content);

            mailSender.send(message);

            logger.info("普通邮件发送成功，收件人: {}", to);

        } catch (Exception e) {
            logger.error("普通邮件发送失败，收件人: {}", to, e);
            throw new RuntimeException("邮件发送失败: " + e.getMessage());
        }
    }

    @Override
    public void sendHtmlEmail(String to, String subject, String htmlContent) {
        logger.info("发送HTML邮件，收件人: {}, 主题: {}", to, subject);

        try {
            MimeMessage message = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(message, true, "UTF-8");

            helper.setFrom(fromEmail);
            helper.setTo(to);
            helper.setSubject(subject);
            helper.setText(htmlContent, true);

            mailSender.send(message);

            logger.info("HTML邮件发送成功，收件人: {}", to);

        } catch (Exception e) {
            logger.error("HTML邮件发送失败，收件人: {}", to, e);
            throw new RuntimeException("邮件发送失败: " + e.getMessage());
        }
    }

    /**
     * 获取验证码邮件主题
     */
    private String getVerificationSubject(Integer type) {
        switch (type) {
            case 1:
                return appName + " - 注册验证码";
            case 2:
                return appName + " - 密码重置验证码";
            case 3:
                return appName + " - 邮箱变更验证码";
            default:
                return appName + " - 验证码";
        }
    }

    /**
     * 构建验证码邮件内容
     */
    private String buildVerificationContent(String code, Integer type) {
        String purpose = getVerificationPurpose(type);

        return "<!DOCTYPE html>" +
                "<html>" +
                "<head>" +
                "<meta charset='UTF-8'>" +
                "<title>验证码</title>" +
                "</head>" +
                "<body style='font-family: Arial, sans-serif; line-height: 1.6; color: #333;'>" +
                "<div style='max-width: 600px; margin: 0 auto; padding: 20px;'>" +
                "<h2 style='color: #4CAF50; text-align: center;'>" + appName + "</h2>" +
                "<div style='background: #f9f9f9; padding: 20px; border-radius: 5px; margin: 20px 0;'>" +
                "<h3>您好！</h3>" +
                "<p>您正在进行" + purpose + "操作，验证码为：</p>" +
                "<div style='text-align: center; margin: 20px 0;'>" +
                "<span style='font-size: 24px; font-weight: bold; color: #4CAF50; background: #e8f5e8; padding: 10px 20px; border-radius: 5px; letter-spacing: 2px;'>" + code + "</span>" +
                "</div>" +
                "<p style='color: #666;'>验证码有效期为10分钟，请及时使用。</p>" +
                "<p style='color: #666;'>如果这不是您的操作，请忽略此邮件。</p>" +
                "</div>" +
                "<div style='text-align: center; color: #999; font-size: 12px; margin-top: 30px;'>" +
                "<p>此邮件由系统自动发送，请勿回复。</p>" +
                "<p>&copy; 2025 " + appName + ". All rights reserved.</p>" +
                "</div>" +
                "</div>" +
                "</body>" +
                "</html>";
    }

    /**
     * 获取验证目的描述
     */
    private String getVerificationPurpose(Integer type) {
        switch (type) {
            case 1:
                return "账户注册";
            case 2:
                return "密码重置";
            case 3:
                return "邮箱变更";
            default:
                return "身份验证";
        }
    }
}