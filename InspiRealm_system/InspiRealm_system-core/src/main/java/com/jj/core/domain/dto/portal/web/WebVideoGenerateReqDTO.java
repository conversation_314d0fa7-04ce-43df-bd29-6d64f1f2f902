package com.jj.core.domain.dto.portal.web;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * Web端视频生成请求DTO
 *
 * <AUTHOR>
 * @date 2025-01-05
 */
@ApiModel(description = "Web端视频生成请求")
public class WebVideoGenerateReqDTO {

    @ApiModelProperty(value = "生成提示词", required = true, example = "一只可爱的小猫在花园里玩耍")
    @NotBlank(message = "生成提示词不能为空")
    @Size(max = 2000, message = "提示词长度不能超过2000字符")
    private String prompt;

    @ApiModelProperty(value = "模型名称", example = "veo3", notes = "固定为veo3")
    private String model = "veo3";

    @ApiModelProperty(value = "是否增强提示词", example = "true")
    private Boolean enhancePrompt = true;

    @ApiModelProperty(value = "是否启用超分辨率", example = "false")
    private Boolean enableUpsample = false;

    @ApiModelProperty(value = "输入图片Base64数据列表")
    private List<WebImageBase64DTO> inputImages;

    @ApiModelProperty(value = "输入图片URL列表")
    private List<String> imageUrls;

    @ApiModelProperty(value = "视频比例", example = "16:9")
    private String aspectRatio = "16:9";

    @ApiModelProperty(value = "视频时长（秒）", example = "5")
    private Integer duration = 8;

    public String getPrompt() {
        return prompt;
    }

    public void setPrompt(String prompt) {
        this.prompt = prompt;
    }

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model;
    }

    public Boolean getEnhancePrompt() {
        return enhancePrompt;
    }

    public void setEnhancePrompt(Boolean enhancePrompt) {
        this.enhancePrompt = enhancePrompt;
    }

    public Boolean getEnableUpsample() {
        return enableUpsample;
    }

    public void setEnableUpsample(Boolean enableUpsample) {
        this.enableUpsample = enableUpsample;
    }

    public List<WebImageBase64DTO> getInputImages() {
        return inputImages;
    }

    public void setInputImages(List<WebImageBase64DTO> inputImages) {
        this.inputImages = inputImages;
    }

    public List<String> getImageUrls() {
        return imageUrls;
    }

    public void setImageUrls(List<String> imageUrls) {
        this.imageUrls = imageUrls;
    }

    public String getAspectRatio() {
        return aspectRatio;
    }

    public void setAspectRatio(String aspectRatio) {
        this.aspectRatio = aspectRatio;
    }

    public Integer getDuration() {
        return duration;
    }

    public void setDuration(Integer duration) {
        this.duration = duration;
    }

    @Override
    public String toString() {
        return "WebVideoGenerateReqDTO{" +
                "prompt='" + prompt + '\'' +
                ", model='" + model + '\'' +
                ", enhancePrompt=" + enhancePrompt +
                ", enableUpsample=" + enableUpsample +
                ", inputImages=" + inputImages +
                ", aspectRatio='" + aspectRatio + '\'' +
                ", duration=" + duration +
                '}';
    }
}