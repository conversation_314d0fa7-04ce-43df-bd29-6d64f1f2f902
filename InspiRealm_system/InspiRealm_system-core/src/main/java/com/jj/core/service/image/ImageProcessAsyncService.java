package com.jj.core.service.image;

import com.jj.common.exception.BizException;
import com.jj.core.domain.enums.TaskStatusEnum;
import com.jj.core.manager.MinioManager;
import com.jj.core.manager.SoraImageManager;
import com.jj.core.service.points.PointsService;
import com.jj.dao.mapper.ImageProcessTaskMapper;
import com.jj.dao.mapper.ProcessedImageMapper;
import com.jj.dao.model.image.ImageProcessTask;
import com.jj.dao.model.image.ProcessedImage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * 图片处理异步服务
 * 专门处理异步任务，解决@Async在同类调用中无效的问题
 * 
 * <AUTHOR>
 * @date 2024-07-30
 */
@Service
public class ImageProcessAsyncService {

    private static final Logger logger = LoggerFactory.getLogger(ImageProcessAsyncService.class);

    @Autowired
    private ImageProcessTaskMapper imageProcessTaskMapper;

    @Autowired
    private ProcessedImageMapper processedImageMapper;

    @Autowired
    private SoraImageManager soraImageManager;

    @Autowired
    private MinioManager minioManager;

    @Autowired
    private PointsService pointsService;

    /**
     * 异步处理图片
     * 
     * @param taskId 任务ID
     * @param taskNo 任务编号
     */
    @Async
    public void processImageAsync(Long taskId, String taskNo) {
        logger.info("开始异步处理图片，taskId: {}, taskNo: {}", taskId, taskNo);

        try {
            // 更新任务状态为处理中
            imageProcessTaskMapper.updateTaskStatus(taskId, TaskStatusEnum.PROCESSING.getCode(), 10, null);
            imageProcessTaskMapper.updateTaskStartTime(taskId);

            // 查询任务详情（使用QueryWrapper过滤del字段）
            ImageProcessTask task = imageProcessTaskMapper.selectOne(
                new com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<ImageProcessTask>()
                    .eq("id", taskId)
                    .eq("del", 0)
            );
            if (task == null) {
                throw new BizException("任务不存在");
            }

            // 构建Sora Image请求
            SoraImageManager.SoraImageRequest soraRequest = new SoraImageManager.SoraImageRequest();
            soraRequest.setPrompt(task.getPrompt());
            soraRequest.setImageUrl(task.getOriginalImageUrl());
            soraRequest.setImageBase64(task.getOriginalImageBase64());
            soraRequest.setAspectRatio(task.getAspectRatio());
            soraRequest.setProcessType(task.getProcessType());
            soraRequest.setTextContent(task.getTextContent());
            soraRequest.setStyle(task.getStyleType());

            // 更新进度
            imageProcessTaskMapper.updateTaskStatus(taskId, TaskStatusEnum.PROCESSING.getCode(), 30, null);

            // 调用Sora Image API
            SoraImageManager.SoraImageResult soraResult = soraImageManager.processImage(soraRequest);

            // 更新进度
            imageProcessTaskMapper.updateTaskStatus(taskId, TaskStatusEnum.PROCESSING.getCode(), 60, null);

            // 下载并上传图片到MinIO
            MinioManager.MinioUploadResult uploadResult = minioManager.uploadImageFromUrl(soraResult.getImageUrl(), task.getUserId());

            // 更新进度
            imageProcessTaskMapper.updateTaskStatus(taskId, TaskStatusEnum.PROCESSING.getCode(), 80, null);

            // 保存处理后的图片信息
            ProcessedImage processedImage = new ProcessedImage();
            processedImage.setTaskId(taskId);
            processedImage.setUserId(task.getUserId());
            processedImage.setImageUrl(uploadResult.getImageUrl());
            processedImage.setThumbnailUrl(uploadResult.getThumbnailUrl());
            processedImage.setOriginalImageUrl(task.getOriginalImageUrl());
            processedImage.setFileName(uploadResult.getFileName());
            processedImage.setFileSize(uploadResult.getFileSize());
            processedImage.setImageWidth(uploadResult.getImageWidth());
            processedImage.setImageHeight(uploadResult.getImageHeight());
            processedImage.setImageFormat(uploadResult.getImageFormat());

            // 自动计算图片比例
            String calculatedAspectRatio = calculateAspectRatio(uploadResult.getImageWidth(), uploadResult.getImageHeight());
            processedImage.setAspectRatio(calculatedAspectRatio);

            processedImage.setProcessType(task.getProcessType());
            processedImage.setIsPublic(0); // 默认私有
            processedImage.setLikeCount(0);
            processedImage.setViewCount(0);
            // 私有图片无需审核，直接设为审核通过状态
            processedImage.setAuditStatus(1); // 1:审核通过
            processedImage.setAuditRemark("私有图片，无需审核");

            int insertResult = processedImageMapper.insert(processedImage);
            if (insertResult <= 0) {
                throw new BizException("保存处理后图片失败");
            }

            // 确认消费积分
            pointsService.confirmConsumePoints(task.getUserId(), task.getPointsCost(), taskNo);

            // 更新任务状态为成功
            imageProcessTaskMapper.updateTaskStatus(taskId, TaskStatusEnum.SUCCESS.getCode(), 100, null);
            imageProcessTaskMapper.updateTaskEndTime(taskId);

            logger.info("图片处理成功，taskId: {}, taskNo: {}", taskId, taskNo);

        } catch (Exception e) {
            logger.error("图片处理失败，taskId: {}, taskNo: {}", taskId, taskNo, e);

            try {
                // 查询任务详情
                ImageProcessTask task = imageProcessTaskMapper.selectById(taskId);
                if (task != null) {
                    // 回退积分
                    pointsService.refundPoints(task.getUserId(), task.getPointsCost(), taskNo);
                }

                // 更新任务状态为失败
                imageProcessTaskMapper.updateTaskStatus(taskId, TaskStatusEnum.FAILED.getCode(), 0, e.getMessage());
                imageProcessTaskMapper.updateTaskEndTime(taskId);

            } catch (Exception ex) {
                logger.error("处理失败后的清理操作异常", ex);
            }
        }
    }

    /**
     * 计算图片宽高比
     *
     * @param width 图片宽度
     * @param height 图片高度
     * @return 宽高比字符串
     */
    public String calculateAspectRatio(Integer width, Integer height) {
        if (width == null || height == null || width <= 0 || height <= 0) {
            return "unknown";
        }

        // 计算最大公约数
        int gcd = gcd(width, height);
        int ratioWidth = width / gcd;
        int ratioHeight = height / gcd;

        // 常见比例的标准化
        if (ratioWidth == ratioHeight) {
            return "1:1";
        } else if (ratioWidth == 4 && ratioHeight == 3) {
            return "4:3";
        } else if (ratioWidth == 3 && ratioHeight == 4) {
            return "3:4";
        } else if (ratioWidth == 16 && ratioHeight == 9) {
            return "16:9";
        } else if (ratioWidth == 9 && ratioHeight == 16) {
            return "9:16";
        } else if (ratioWidth == 3 && ratioHeight == 2) {
            return "3:2";
        } else if (ratioWidth == 2 && ratioHeight == 3) {
            return "2:3";
        } else {
            // 对于其他比例，如果差异不大，归类到最接近的标准比例
            double ratio = (double) width / height;
            if (Math.abs(ratio - 1.0) < 0.1) {
                return "1:1";
            } else if (Math.abs(ratio - 4.0/3.0) < 0.1) {
                return "4:3";
            } else if (Math.abs(ratio - 3.0/4.0) < 0.1) {
                return "3:4";
            } else if (Math.abs(ratio - 16.0/9.0) < 0.1) {
                return "16:9";
            } else if (Math.abs(ratio - 9.0/16.0) < 0.1) {
                return "9:16";
            } else {
                // 返回简化的比例
                return ratioWidth + ":" + ratioHeight;
            }
        }
    }

    /**
     * 计算最大公约数
     *
     * @param a 数字a
     * @param b 数字b
     * @return 最大公约数
     */
    private int gcd(int a, int b) {
        while (b != 0) {
            int temp = b;
            b = a % b;
            a = temp;
        }
        return a;
    }
}
