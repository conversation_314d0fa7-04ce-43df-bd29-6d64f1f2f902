package com.jj.core.aspect;

import com.jj.core.annotation.WebAuth;
import com.jj.core.enums.AuthStatusEnum;
import com.jj.core.exception.AuthException;
import com.jj.dao.mapper.UserSessionMapper;
import com.jj.dao.model.user.UserSession;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;

/**
 * Web端权限验证切面
 *
 * <AUTHOR>
 * @date 2025-01-05
 */
@Aspect
@Component
public class WebAuthAspect {

    private static final Logger logger = LoggerFactory.getLogger(WebAuthAspect.class);

    @Autowired
    private UserSessionMapper userSessionMapper;

    @Around("@annotation(webAuth)")
    public Object around(ProceedingJoinPoint joinPoint, WebAuth webAuth) throws Throwable {
        logger.debug("Web权限验证开始: {}", webAuth.value());

        try {
            // 获取当前请求
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes == null) {
                logger.error("无法获取请求上下文");
                throw AuthException.tokenMissing();
            }

            HttpServletRequest request = attributes.getRequest();

            // 获取Authorization头
            String authorization = request.getHeader("Authorization");
            if (!StringUtils.hasText(authorization)) {
                logger.warn("请求缺少Authorization头，URI: {}", request.getRequestURI());
                throw AuthException.tokenMissing();
            }

            // 解析Bearer Token
            String accessToken = extractAccessToken(authorization);
            if (!StringUtils.hasText(accessToken)) {
                logger.warn("访问令牌格式错误，authorization: {}", authorization);
                throw AuthException.tokenFormatError();
            }

            // 验证Token并获取会话信息
            UserSession session = validateAccessToken(accessToken);

            // 更新最后活跃时间
            try {
                userSessionMapper.updateLastActiveTime(session.getSessionId());
            } catch (Exception e) {
                logger.warn("更新用户活跃时间失败，sessionId: {}", session.getSessionId(), e);
                // 不影响主流程，继续执行
            }

            // 将用户信息存储到请求属性中
            request.setAttribute("currentUserId", session.getUserId());
            request.setAttribute("currentSessionId", session.getSessionId());

            logger.debug("Web权限验证通过，用户ID: {}, 会话ID: {}", session.getUserId(), session.getSessionId());

            // 执行目标方法
            return joinPoint.proceed();

        } catch (AuthException e) {
            logger.warn("Web权限验证失败: authCode={}, message={}, requireReLogin={}",
                    e.getAuthCode(), e.getMessage(), e.isRequireReLogin());
            throw e;
        } catch (Exception e) {
            logger.error("Web权限验证异常", e);
            throw new AuthException(AuthStatusEnum.AUTH_SERVICE_ERROR, "权限验证服务异常", e);
        }
    }

    /**
     * 从Authorization头中提取访问令牌
     *
     * @param authorization Authorization头的值
     * @return 访问令牌，如果格式错误返回null
     */
    private String extractAccessToken(String authorization) {
        if (authorization.startsWith("Bearer ")) {
            return authorization.substring(7).trim();
        } else {
            // 兼容不带Bearer前缀的情况
            return authorization.trim();
        }
    }

    /**
     * 验证访问令牌并返回会话信息
     *
     * @param accessToken 访问令牌
     * @return 有效的会话信息
     * @throws AuthException 当令牌无效、过期或会话失效时抛出
     */
    private UserSession validateAccessToken(String accessToken) throws AuthException {
        // 查询会话信息
        UserSession session = userSessionMapper.selectByAccessToken(accessToken);
        if (session == null) {
            logger.warn("访问令牌无效，token: {}", accessToken);
            throw AuthException.tokenInvalid();
        }

        // 检查会话状态
        if (session.getStatus() != 1) {
            logger.warn("会话已失效，sessionId: {}, status: {}", session.getSessionId(), session.getStatus());
            throw AuthException.sessionInvalid();
        }

        // 检查是否过期
        if (session.getExpiresAt().before(new Date())) {
            logger.warn("访问令牌已过期，sessionId: {}, expiresAt: {}",
                    session.getSessionId(), session.getExpiresAt());
            throw AuthException.tokenExpired();
        }

        return session;
    }
}