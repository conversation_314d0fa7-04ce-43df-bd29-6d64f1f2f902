package com.jj.core.auth;

import com.jj.core.utils.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.security.SecureRandom;
import java.util.concurrent.TimeUnit;

/**
 * Token管理器
 * 负责Token的生成、验证、刷新和删除
 * 
 * <AUTHOR>
 * @date 2024-07-29
 */
@Component
public class TokenManager {
    
    @Autowired
    private RedisUtil redisUtil;
    
    // Token前缀
    private static final String WECHAT_TOKEN_PREFIX = "wechat:token:";
    private static final String ADMIN_TOKEN_PREFIX = "admin:token:";
    
    // Token过期时间（7天）
    private static final long TOKEN_EXPIRE_TIME = 7 * 24 * 60 * 60;

    // 管理员Token过期时间（2小时）
    public static final long ADMIN_TOKEN_EXPIRE_TIME = 2 * 60 * 60;
    
    // 随机字符集
    private static final String CHARACTERS = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
    private static final SecureRandom RANDOM = new SecureRandom();
    
    /**
     * 生成随机Token
     * 
     * @param length Token长度
     * @return 随机Token
     */
    private String generateRandomToken(int length) {
        StringBuilder token = new StringBuilder(length);
        for (int i = 0; i < length; i++) {
            token.append(CHARACTERS.charAt(RANDOM.nextInt(CHARACTERS.length())));
        }
        return token.toString();
    }
    
    /**
     * 生成微信用户Token
     * 
     * @param userId 用户ID
     * @param openId 微信OpenID
     * @return Token
     */
    public String generateWeChatToken(Long userId, String openId) {
        String token = generateRandomToken(32);
        String key = WECHAT_TOKEN_PREFIX + token;
        
        // 构建用户信息
        WeChatTokenInfo tokenInfo = new WeChatTokenInfo();
        tokenInfo.setUserId(userId);
        tokenInfo.setOpenId(openId);
        tokenInfo.setTokenType("wechat");
        tokenInfo.setCreateTime(System.currentTimeMillis());
        
        // 存储到Redis
        redisUtil.setEx(key, tokenInfo, TOKEN_EXPIRE_TIME, TimeUnit.SECONDS);
        
        return token;
    }
    
    /**
     * 生成管理员Token
     *
     * @param adminId 管理员ID
     * @param username 用户名
     * @param role 管理员角色
     * @return Token
     */
    public String generateAdminToken(Long adminId, String username, Integer role) {
        String token = generateRandomToken(32);
        String key = ADMIN_TOKEN_PREFIX + token;

        // 构建管理员信息
        AdminTokenInfo tokenInfo = new AdminTokenInfo();
        tokenInfo.setAdminId(adminId);
        tokenInfo.setUsername(username);
        tokenInfo.setRole(role);
        tokenInfo.setTokenType("admin");
        tokenInfo.setCreateTime(System.currentTimeMillis());

        // 存储到Redis
        redisUtil.setEx(key, tokenInfo, ADMIN_TOKEN_EXPIRE_TIME, TimeUnit.SECONDS);

        return token;
    }
    
    /**
     * 验证微信用户Token
     * 
     * @param token Token
     * @return 用户信息，验证失败返回null
     */
    public WeChatTokenInfo validateWeChatToken(String token) {
        if (token == null || token.trim().isEmpty()) {
            return null;
        }
        
        String key = WECHAT_TOKEN_PREFIX + token;
        Object obj = redisUtil.get(key);
        
        if (obj instanceof WeChatTokenInfo) {
            // 刷新Token过期时间
            redisUtil.expire(key, TOKEN_EXPIRE_TIME, TimeUnit.SECONDS);
            return (WeChatTokenInfo) obj;
        }
        
        return null;
    }
    
    /**
     * 验证管理员Token
     * 
     * @param token Token
     * @return 管理员信息，验证失败返回null
     */
    public AdminTokenInfo validateAdminToken(String token) {
        if (token == null || token.trim().isEmpty()) {
            return null;
        }
        
        String key = ADMIN_TOKEN_PREFIX + token;
        Object obj = redisUtil.get(key);
        
        if (obj instanceof AdminTokenInfo) {
            // 刷新Token过期时间
            redisUtil.expire(key, ADMIN_TOKEN_EXPIRE_TIME, TimeUnit.SECONDS);
            return (AdminTokenInfo) obj;
        }
        
        return null;
    }
    
    /**
     * 删除微信用户Token
     * 
     * @param token Token
     */
    public void removeWeChatToken(String token) {
        if (token != null && !token.trim().isEmpty()) {
            String key = WECHAT_TOKEN_PREFIX + token;
            redisUtil.del(key);
        }
    }
    
    /**
     * 删除管理员Token
     *
     * @param token Token
     */
    public void removeAdminToken(String token) {
        if (token != null && !token.trim().isEmpty()) {
            String key = ADMIN_TOKEN_PREFIX + token;
            redisUtil.del(key);
        }
    }

    /**
     * 从请求中获取管理员ID
     *
     * @param request HTTP请求
     * @return 管理员ID，获取失败返回null
     */
    public Long getAdminIdFromRequest(javax.servlet.http.HttpServletRequest request) {
        String token = getTokenFromRequest(request);
        if (token == null) {
            return null;
        }

        AdminTokenInfo tokenInfo = validateAdminToken(token);
        return tokenInfo != null ? tokenInfo.getAdminId() : null;
    }

    /**
     * 从请求中获取Token
     *
     * @param request HTTP请求
     * @return Token
     */
    private String getTokenFromRequest(javax.servlet.http.HttpServletRequest request) {
        // 优先从Header中获取
        String token = request.getHeader("Authorization");
        if (token != null && token.startsWith("Bearer ")) {
            return token.substring(7);
        }

        // 从Token Header中获取
        token = request.getHeader("Token");
        if (token != null && !token.trim().isEmpty()) {
            return token;
        }

        // 从参数中获取
        return request.getParameter("token");
    }
}
