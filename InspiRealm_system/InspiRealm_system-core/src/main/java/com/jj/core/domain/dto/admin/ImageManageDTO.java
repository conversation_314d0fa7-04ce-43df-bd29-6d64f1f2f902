package com.jj.core.domain.dto.admin;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 图片管理DTO
 * 
 * <AUTHOR>
 * @date 2024-07-29
 */
@ApiModel(description = "图片管理信息")
public class ImageManageDTO {
    
    /**
     * 图片ID
     */
    @ApiModelProperty(value = "图片ID", example = "1")
    private Long id;
    
    /**
     * 任务ID
     */
    @ApiModelProperty(value = "任务ID", example = "1")
    private Long taskId;
    
    /**
     * 用户ID
     */
    @ApiModelProperty(value = "用户ID", example = "1")
    private Long userId;
    
    /**
     * 用户昵称
     */
    @ApiModelProperty(value = "用户昵称", example = "张三")
    private String userNickname;
    
    /**
     * 图片URL
     */
    @ApiModelProperty(value = "图片URL", example = "https://minio.example.com/bucket/image.jpg")
    private String imageUrl;
    
    /**
     * 缩略图URL
     */
    @ApiModelProperty(value = "缩略图URL", example = "https://minio.example.com/bucket/thumbnail.jpg")
    private String thumbnailUrl;
    
    /**
     * 原图URL
     */
    @ApiModelProperty(value = "原图URL", example = "https://example.com/original.png")
    private String originalImageUrl;
    
    /**
     * 文件名
     */
    @ApiModelProperty(value = "文件名", example = "processed_image_20240729.jpg")
    private String fileName;
    
    /**
     * 文件大小(字节)
     */
    @ApiModelProperty(value = "文件大小(字节)", example = "1024000")
    private Long fileSize;
    
    /**
     * 图片宽度
     */
    @ApiModelProperty(value = "图片宽度", example = "1024")
    private Integer imageWidth;
    
    /**
     * 图片高度
     */
    @ApiModelProperty(value = "图片高度", example = "768")
    private Integer imageHeight;
    
    /**
     * 图片格式
     */
    @ApiModelProperty(value = "图片格式", example = "jpg")
    private String imageFormat;
    
    /**
     * 图片比例
     */
    @ApiModelProperty(value = "图片比例", example = "4:3")
    private String aspectRatio;
    
    /**
     * 处理类型
     */
    @ApiModelProperty(value = "处理类型", example = "beautify")
    private String processType;
    
    /**
     * 是否公开 0:私有 1:公开
     */
    @ApiModelProperty(value = "是否公开 0:私有 1:公开", example = "1")
    private Integer isPublic;
    
    /**
     * 公开状态描述
     */
    @ApiModelProperty(value = "公开状态描述", example = "公开")
    private String publicStatusDesc;
    
    /**
     * 点赞数
     */
    @ApiModelProperty(value = "点赞数", example = "10")
    private Integer likeCount;
    
    /**
     * 查看数
     */
    @ApiModelProperty(value = "查看数", example = "100")
    private Integer viewCount;
    
    /**
     * 审核状态 0:待审核 1:审核通过 2:审核拒绝
     */
    @ApiModelProperty(value = "审核状态", example = "1")
    private Integer auditStatus;
    
    /**
     * 审核状态描述
     */
    @ApiModelProperty(value = "审核状态描述", example = "审核通过")
    private String auditStatusDesc;
    
    /**
     * 审核备注
     */
    @ApiModelProperty(value = "审核备注", example = "内容合规")
    private String auditRemark;
    
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间", example = "2024-07-29 10:30:00")
    private String createTime;
    
    // Getter and Setter methods
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public Long getTaskId() {
        return taskId;
    }
    
    public void setTaskId(Long taskId) {
        this.taskId = taskId;
    }
    
    public Long getUserId() {
        return userId;
    }
    
    public void setUserId(Long userId) {
        this.userId = userId;
    }
    
    public String getUserNickname() {
        return userNickname;
    }
    
    public void setUserNickname(String userNickname) {
        this.userNickname = userNickname;
    }
    
    public String getImageUrl() {
        return imageUrl;
    }
    
    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }
    
    public String getThumbnailUrl() {
        return thumbnailUrl;
    }
    
    public void setThumbnailUrl(String thumbnailUrl) {
        this.thumbnailUrl = thumbnailUrl;
    }
    
    public String getOriginalImageUrl() {
        return originalImageUrl;
    }
    
    public void setOriginalImageUrl(String originalImageUrl) {
        this.originalImageUrl = originalImageUrl;
    }
    
    public String getFileName() {
        return fileName;
    }
    
    public void setFileName(String fileName) {
        this.fileName = fileName;
    }
    
    public Long getFileSize() {
        return fileSize;
    }
    
    public void setFileSize(Long fileSize) {
        this.fileSize = fileSize;
    }
    
    public Integer getImageWidth() {
        return imageWidth;
    }
    
    public void setImageWidth(Integer imageWidth) {
        this.imageWidth = imageWidth;
    }
    
    public Integer getImageHeight() {
        return imageHeight;
    }
    
    public void setImageHeight(Integer imageHeight) {
        this.imageHeight = imageHeight;
    }
    
    public String getImageFormat() {
        return imageFormat;
    }
    
    public void setImageFormat(String imageFormat) {
        this.imageFormat = imageFormat;
    }
    
    public String getAspectRatio() {
        return aspectRatio;
    }
    
    public void setAspectRatio(String aspectRatio) {
        this.aspectRatio = aspectRatio;
    }
    
    public String getProcessType() {
        return processType;
    }
    
    public void setProcessType(String processType) {
        this.processType = processType;
    }
    
    public Integer getIsPublic() {
        return isPublic;
    }
    
    public void setIsPublic(Integer isPublic) {
        this.isPublic = isPublic;
    }
    
    public String getPublicStatusDesc() {
        return publicStatusDesc;
    }
    
    public void setPublicStatusDesc(String publicStatusDesc) {
        this.publicStatusDesc = publicStatusDesc;
    }
    
    public Integer getLikeCount() {
        return likeCount;
    }
    
    public void setLikeCount(Integer likeCount) {
        this.likeCount = likeCount;
    }
    
    public Integer getViewCount() {
        return viewCount;
    }
    
    public void setViewCount(Integer viewCount) {
        this.viewCount = viewCount;
    }
    
    public Integer getAuditStatus() {
        return auditStatus;
    }
    
    public void setAuditStatus(Integer auditStatus) {
        this.auditStatus = auditStatus;
    }
    
    public String getAuditStatusDesc() {
        return auditStatusDesc;
    }
    
    public void setAuditStatusDesc(String auditStatusDesc) {
        this.auditStatusDesc = auditStatusDesc;
    }
    
    public String getAuditRemark() {
        return auditRemark;
    }
    
    public void setAuditRemark(String auditRemark) {
        this.auditRemark = auditRemark;
    }
    
    public String getCreateTime() {
        return createTime;
    }
    
    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }
    
    @Override
    public String toString() {
        return "ImageManageDTO{" +
                "id=" + id +
                ", taskId=" + taskId +
                ", userId=" + userId +
                ", userNickname='" + userNickname + '\'' +
                ", imageUrl='" + imageUrl + '\'' +
                ", thumbnailUrl='" + thumbnailUrl + '\'' +
                ", originalImageUrl='" + originalImageUrl + '\'' +
                ", fileName='" + fileName + '\'' +
                ", fileSize=" + fileSize +
                ", imageWidth=" + imageWidth +
                ", imageHeight=" + imageHeight +
                ", imageFormat='" + imageFormat + '\'' +
                ", aspectRatio='" + aspectRatio + '\'' +
                ", processType='" + processType + '\'' +
                ", isPublic=" + isPublic +
                ", publicStatusDesc='" + publicStatusDesc + '\'' +
                ", likeCount=" + likeCount +
                ", viewCount=" + viewCount +
                ", auditStatus=" + auditStatus +
                ", auditStatusDesc='" + auditStatusDesc + '\'' +
                ", auditRemark='" + auditRemark + '\'' +
                ", createTime='" + createTime + '\'' +
                '}';
    }
}
