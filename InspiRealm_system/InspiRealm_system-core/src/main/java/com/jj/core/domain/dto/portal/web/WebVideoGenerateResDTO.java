package com.jj.core.domain.dto.portal.web;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Web端视频生成响应DTO
 *
 * <AUTHOR>
 * @date 2025-01-05
 */
@ApiModel(description = "Web端视频生成响应")
public class WebVideoGenerateResDTO {

    @ApiModelProperty(value = "任务ID", example = "task_123456789")
    private String taskId;

    @ApiModelProperty(value = "任务编号", example = "VEO3_20250105_001")
    private String taskNo;

    @ApiModelProperty(value = "任务状态", example = "2")
    private Integer status;

    @ApiModelProperty(value = "状态描述", example = "处理中")
    private String statusDesc;

    @ApiModelProperty(value = "预计完成时间（秒）", example = "120")
    private Integer estimatedTime;

    @ApiModelProperty(value = "积分消耗", example = "10")
    private Integer pointsCost;

    @ApiModelProperty(value = "创建时间", example = "2025-01-05 10:30:00")
    private String createTime;

    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public String getTaskNo() {
        return taskNo;
    }

    public void setTaskNo(String taskNo) {
        this.taskNo = taskNo;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getStatusDesc() {
        return statusDesc;
    }

    public void setStatusDesc(String statusDesc) {
        this.statusDesc = statusDesc;
    }

    public Integer getEstimatedTime() {
        return estimatedTime;
    }

    public void setEstimatedTime(Integer estimatedTime) {
        this.estimatedTime = estimatedTime;
    }

    public Integer getPointsCost() {
        return pointsCost;
    }

    public void setPointsCost(Integer pointsCost) {
        this.pointsCost = pointsCost;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    @Override
    public String toString() {
        return "WebVideoGenerateResDTO{" +
                "taskId='" + taskId + '\'' +
                ", taskNo='" + taskNo + '\'' +
                ", status=" + status +
                ", statusDesc='" + statusDesc + '\'' +
                ", estimatedTime=" + estimatedTime +
                ", pointsCost=" + pointsCost +
                ", createTime='" + createTime + '\'' +
                '}';
    }
}