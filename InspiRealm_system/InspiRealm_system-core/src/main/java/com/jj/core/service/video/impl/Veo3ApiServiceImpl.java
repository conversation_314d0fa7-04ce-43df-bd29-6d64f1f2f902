package com.jj.core.service.video.impl;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.jj.core.service.video.Veo3ApiService;
import com.jj.dao.model.video.VideoGenerationTask;
import okhttp3.MediaType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import okhttp3.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;

import java.util.*;

/**
 * Veo3 API调用服务实现类
 *
 * <AUTHOR>
 * @date 2025-01-05
 */
@Service
public class Veo3ApiServiceImpl implements Veo3ApiService {

    private static final Logger logger = LoggerFactory.getLogger(Veo3ApiServiceImpl.class);

    @Value("${veo3.api.base-url:https://yunwu.ai/v1}")
    private String veo3ApiBaseUrl;

    @Value("${veo3.api.key:}")
    private String veo3ApiKey;

    private final OkHttpClient httpClient;
    private final RestTemplate restTemplate; // 保留用于getTaskStatus方法
    private final ObjectMapper objectMapper;

    public Veo3ApiServiceImpl() {
        this.httpClient = new OkHttpClient.Builder()
                .connectTimeout(30, java.util.concurrent.TimeUnit.SECONDS)
                .readTimeout(60, java.util.concurrent.TimeUnit.SECONDS)
                .writeTimeout(60, java.util.concurrent.TimeUnit.SECONDS)
                .build();

        this.restTemplate = new RestTemplate(); // 保留用于getTaskStatus方法

        // 配置ObjectMapper禁用Unicode转义
        this.objectMapper = new ObjectMapper();
        // 禁用Unicode转义，保持URL原始格式
        this.objectMapper.getFactory().disable(com.fasterxml.jackson.core.JsonGenerator.Feature.ESCAPE_NON_ASCII);
    }

    @Override
    public String createVideoTask(VideoGenerationTask task) {
        logger.info("调用Veo3 API创建视频任务，任务编号: {}", task.getTaskNo());

        try {
            // 构建请求URL
            String url = veo3ApiBaseUrl + "/video/create";

            // 构建请求体
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("prompt", task.getPrompt());
            requestBody.put("model", "veo3");
            requestBody.put("enhance_prompt", task.getEnhancePrompt() == 1);
            requestBody.put("enable_upsample", task.getEnableUpsample() == 1);

            // 处理输入图片
            if (StringUtils.hasText(task.getInputImages())) {
                try {
                    JSONArray array = JSONUtil.parseArray(task.getInputImages());
                    List<String> imageUrls = new ArrayList<>();
                    for (Object o : array) {
                        // 直接使用字符串值，不进行JSON序列化
                        imageUrls.add(String.valueOf(o));
                        logger.debug("解析到图片URL: {}", o);
                    }
                    if (!imageUrls.isEmpty()) {
                        requestBody.put("images", imageUrls);
                        logger.info("添加图片URL到请求，数量: {}", imageUrls.size());
                        // 打印第一个URL作为示例（避免日志过长）
                        logger.info("示例URL: {}", imageUrls.get(0));
                    }
                } catch (Exception e) {
                    logger.warn("解析输入图片失败: {}", e.getMessage());
                }
            }

            // 手动序列化请求体，确保URL不被转义
            String requestBodyJson = objectMapper.writeValueAsString(requestBody);
            logger.info("发送到Veo3的请求体: {}", requestBodyJson);
            RequestBody body = RequestBody.create(MediaType.get("application/json"), requestBodyJson);
            // 构建OkHttp请求
            Request.Builder requestBuilder = new Request.Builder()
                    .url(url)
                    .post(body);

            // 添加Authorization头
            if (StringUtils.hasText(veo3ApiKey)) {
                requestBuilder.addHeader("Authorization", "Bearer " + veo3ApiKey);
            }

            Request request = requestBuilder.build();

            // 发送请求
            try (Response response = httpClient.newCall(request).execute()) {
                if (response.isSuccessful()) {
                    String responseBody = response.body().string();
                    JsonNode responseNode = objectMapper.readTree(responseBody);
                    String externalTaskId = responseNode.get("id").asText();

                    logger.info("Veo3任务创建成功，外部任务ID: {}", externalTaskId);
                    return externalTaskId;
                } else {
                    String errorBody = response.body() != null ? response.body().string() : "无响应体";
                    logger.error("Veo3 API调用失败，状态码: {}, 响应: {}", response.code(), errorBody);
                    throw new RuntimeException("Veo3 API调用失败，状态码: " + response.code());
                }
            }

        } catch (Exception e) {
            logger.error("调用Veo3 API创建任务失败", e);
            throw new RuntimeException("Veo3 API调用失败: " + e.getMessage());
        }
    }

    @Override
    public Veo3TaskStatus queryTaskStatus(String externalTaskId) {
        logger.info("查询Veo3任务状态，外部任务ID: {}", externalTaskId);

        try {
            // 构建请求URL
            String url = veo3ApiBaseUrl + "/video/query?id=" + externalTaskId;

            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            if (StringUtils.hasText(veo3ApiKey)) {
                headers.set("Authorization", "Bearer " + veo3ApiKey);
            }

            HttpEntity<Void> entity = new HttpEntity<>(headers);

            // 发送请求
            ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.GET, entity, String.class);

            if (response.getStatusCode() == HttpStatus.OK) {
                JsonNode responseNode = objectMapper.readTree(response.getBody());

                Veo3TaskStatus status = new Veo3TaskStatus();
                status.setId(responseNode.get("id").asText());
                status.setStatus(responseNode.get("status").asText());

                if (responseNode.has("status_update_time")) {
                    status.setStatusUpdateTime(responseNode.get("status_update_time").asLong());
                }

                // 检查是否有详细信息
                if (responseNode.has("detail")) {
                    JsonNode detailNode = responseNode.get("detail");
                    if (detailNode.has("video_url")) {
                        status.setVideoUrl(detailNode.get("video_url").asText());
                    }
                    if (detailNode.has("error")) {
                        status.setErrorMessage(detailNode.get("error").asText());
                    }
                }

                logger.info("Veo3任务状态查询成功，状态: {}", status.getStatus());
                return status;
            } else {
                throw new RuntimeException("Veo3 API查询失败，状态码: " + response.getStatusCode());
            }

        } catch (Exception e) {
            logger.error("查询Veo3任务状态失败", e);
            throw new RuntimeException("Veo3 API查询失败: " + e.getMessage());
        }
    }
}