package com.jj.core.domain.dto.portal.web;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Web端用户信息响应DTO
 *
 * <AUTHOR>
 * @date 2025-01-05
 */
@ApiModel(description = "Web端用户信息响应")
public class WebUserProfileResDTO {

    @ApiModelProperty(value = "用户ID", example = "1001")
    private Long id;

    @ApiModelProperty(value = "用户名", example = "testuser")
    private String username;

    @ApiModelProperty(value = "邮箱地址", example = "<EMAIL>")
    private String email;

    @ApiModelProperty(value = "昵称", example = "测试用户")
    private String nickname;

    @ApiModelProperty(value = "头像URL", example = "https://example.com/avatar.jpg")
    private String avatarUrl;

    @ApiModelProperty(value = "手机号", example = "13800138000")
    private String phone;

    @ApiModelProperty(value = "性别", example = "1")
    private Integer gender;

    @ApiModelProperty(value = "生日", example = "1990-01-01")
    private String birthday;

    @ApiModelProperty(value = "国家", example = "中国")
    private String country;

    @ApiModelProperty(value = "省份", example = "北京市")
    private String province;

    @ApiModelProperty(value = "城市", example = "北京市")
    private String city;

    @ApiModelProperty(value = "用户类型", example = "1")
    private Integer userType;

    @ApiModelProperty(value = "邮箱验证状态", example = "true")
    private Boolean emailVerified;

    @ApiModelProperty(value = "注册时间", example = "2025-01-05 10:30:00")
    private String createTime;

    // Getters and Setters
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }
    public String getUsername() { return username; }
    public void setUsername(String username) { this.username = username; }
    public String getEmail() { return email; }
    public void setEmail(String email) { this.email = email; }
    public String getNickname() { return nickname; }
    public void setNickname(String nickname) { this.nickname = nickname; }
    public String getAvatarUrl() { return avatarUrl; }
    public void setAvatarUrl(String avatarUrl) { this.avatarUrl = avatarUrl; }
    public String getPhone() { return phone; }
    public void setPhone(String phone) { this.phone = phone; }
    public Integer getGender() { return gender; }
    public void setGender(Integer gender) { this.gender = gender; }
    public String getBirthday() { return birthday; }
    public void setBirthday(String birthday) { this.birthday = birthday; }
    public String getCountry() { return country; }
    public void setCountry(String country) { this.country = country; }
    public String getProvince() { return province; }
    public void setProvince(String province) { this.province = province; }
    public String getCity() { return city; }
    public void setCity(String city) { this.city = city; }
    public Integer getUserType() { return userType; }
    public void setUserType(Integer userType) { this.userType = userType; }
    public Boolean getEmailVerified() { return emailVerified; }
    public void setEmailVerified(Boolean emailVerified) { this.emailVerified = emailVerified; }
    public String getCreateTime() { return createTime; }
    public void setCreateTime(String createTime) { this.createTime = createTime; }
}