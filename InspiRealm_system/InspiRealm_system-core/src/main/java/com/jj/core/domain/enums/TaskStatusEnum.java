package com.jj.core.domain.enums;

/**
 * 任务状态枚举
 * 
 * <AUTHOR>
 * @date 2024-07-29
 */
public enum TaskStatusEnum {
    
    /**
     * 待处理
     */
    PENDING(1, "待处理"),
    
    /**
     * 处理中
     */
    PROCESSING(2, "处理中"),
    
    /**
     * 成功
     */
    SUCCESS(3, "处理成功"),
    
    /**
     * 失败
     */
    FAILED(4, "处理失败"),
    
    /**
     * 取消
     */
    CANCELLED(5, "已取消");
    
    private final Integer code;
    private final String desc;
    
    TaskStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    
    public Integer getCode() {
        return code;
    }
    
    public String getDesc() {
        return desc;
    }
    
    /**
     * 根据代码获取枚举
     * 
     * @param code 代码
     * @return 枚举
     */
    public static TaskStatusEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (TaskStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }
    
    /**
     * 根据代码获取描述
     * 
     * @param code 代码
     * @return 描述
     */
    public static String getDescByCode(Integer code) {
        TaskStatusEnum status = getByCode(code);
        return status != null ? status.getDesc() : "未知状态";
    }
}
