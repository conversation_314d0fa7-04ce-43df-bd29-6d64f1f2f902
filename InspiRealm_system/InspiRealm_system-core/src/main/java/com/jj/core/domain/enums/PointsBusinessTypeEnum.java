package com.jj.core.domain.enums;

/**
 * 积分业务类型枚举
 * 
 * <AUTHOR>
 * @date 2024-07-29
 */
public enum PointsBusinessTypeEnum {
    
    /**
     * 用户注册
     */
    USER_REGISTER("USER_REGISTER", "用户注册"),
    
    /**
     * 每日签到
     */
    DAILY_CHECKIN("DAILY_CHECKIN", "每日签到"),
    
    /**
     * 图片处理
     */
    IMAGE_PROCESS("IMAGE_PROCESS", "图片处理"),
    
    /**
     * 管理员充值
     */
    ADMIN_RECHARGE("ADMIN_RECHARGE", "管理员充值"),
    
    /**
     * 管理员扣除
     */
    ADMIN_DEDUCT("ADMIN_DEDUCT", "管理员扣除"),
    
    /**
     * 系统奖励
     */
    SYSTEM_REWARD("SYSTEM_REWARD", "系统奖励"),
    
    /**
     * 系统回退
     */
    SYSTEM_REFUND("SYSTEM_REFUND", "系统回退");
    
    private final String code;
    private final String desc;
    
    PointsBusinessTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getDesc() {
        return desc;
    }
    
    /**
     * 根据代码获取枚举
     * 
     * @param code 代码
     * @return 枚举
     */
    public static PointsBusinessTypeEnum getByCode(String code) {
        if (code == null) {
            return null;
        }
        for (PointsBusinessTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
    
    /**
     * 根据代码获取描述
     * 
     * @param code 代码
     * @return 描述
     */
    public static String getDescByCode(String code) {
        PointsBusinessTypeEnum type = getByCode(code);
        return type != null ? type.getDesc() : "未知业务";
    }
}
