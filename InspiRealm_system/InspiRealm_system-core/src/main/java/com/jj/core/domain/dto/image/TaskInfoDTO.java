package com.jj.core.domain.dto.image;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 任务信息DTO（用于任务列表）
 * 
 * <AUTHOR>
 * @date 2024-07-30
 */
@ApiModel(description = "任务信息")
public class TaskInfoDTO {
    
    /**
     * 任务编号
     */
    @ApiModelProperty(value = "任务编号", example = "TASK_20240729_001")
    private String taskNo;
    
    /**
     * 原图片URL
     */
    @ApiModelProperty(value = "原图片URL", example = "https://example.com/original.jpg")
    private String originalImageUrl;
    
    /**
     * 结果图片URL
     */
    @ApiModelProperty(value = "结果图片URL", example = "https://example.com/result.jpg")
    private String resultImageUrl;
    
    /**
     * 处理类型
     */
    @ApiModelProperty(value = "处理类型", example = "beautify")
    private String processType;
    
    /**
     * 任务状态 1:待处理 2:处理中 3:成功 4:失败 5:取消
     */
    @ApiModelProperty(value = "任务状态", example = "3")
    private Integer status;
    
    /**
     * 任务状态描述
     */
    @ApiModelProperty(value = "任务状态描述", example = "处理成功")
    private String statusDesc;
    
    /**
     * 进度百分比
     */
    @ApiModelProperty(value = "进度百分比", example = "100")
    private Integer progress;
    
    /**
     * 错误信息
     */
    @ApiModelProperty(value = "错误信息", example = "")
    private String errorMessage;
    
    /**
     * 提交时间
     */
    @ApiModelProperty(value = "提交时间", example = "2024-07-29 10:30:00")
    private String submitTime;
    
    /**
     * 完成时间
     */
    @ApiModelProperty(value = "完成时间", example = "2024-07-29 10:30:30")
    private String completeTime;
    
    /**
     * 处理后图片信息（仅当任务成功时有值）
     */
    @ApiModelProperty(value = "处理后图片信息")
    private ProcessedImageDTO processedImage;
    
    public String getTaskNo() {
        return taskNo;
    }
    
    public void setTaskNo(String taskNo) {
        this.taskNo = taskNo;
    }
    
    public String getOriginalImageUrl() {
        return originalImageUrl;
    }
    
    public void setOriginalImageUrl(String originalImageUrl) {
        this.originalImageUrl = originalImageUrl;
    }
    
    public String getResultImageUrl() {
        return resultImageUrl;
    }
    
    public void setResultImageUrl(String resultImageUrl) {
        this.resultImageUrl = resultImageUrl;
    }
    
    public String getProcessType() {
        return processType;
    }
    
    public void setProcessType(String processType) {
        this.processType = processType;
    }
    
    public Integer getStatus() {
        return status;
    }
    
    public void setStatus(Integer status) {
        this.status = status;
    }
    
    public String getStatusDesc() {
        return statusDesc;
    }
    
    public void setStatusDesc(String statusDesc) {
        this.statusDesc = statusDesc;
    }
    
    public Integer getProgress() {
        return progress;
    }
    
    public void setProgress(Integer progress) {
        this.progress = progress;
    }
    
    public String getErrorMessage() {
        return errorMessage;
    }
    
    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }
    
    public String getSubmitTime() {
        return submitTime;
    }
    
    public void setSubmitTime(String submitTime) {
        this.submitTime = submitTime;
    }
    
    public String getCompleteTime() {
        return completeTime;
    }
    
    public void setCompleteTime(String completeTime) {
        this.completeTime = completeTime;
    }
    
    public ProcessedImageDTO getProcessedImage() {
        return processedImage;
    }
    
    public void setProcessedImage(ProcessedImageDTO processedImage) {
        this.processedImage = processedImage;
    }
    
    @Override
    public String toString() {
        return "TaskInfoDTO{" +
                "taskNo='" + taskNo + '\'' +
                ", originalImageUrl='" + originalImageUrl + '\'' +
                ", resultImageUrl='" + resultImageUrl + '\'' +
                ", processType='" + processType + '\'' +
                ", status=" + status +
                ", statusDesc='" + statusDesc + '\'' +
                ", progress=" + progress +
                ", errorMessage='" + errorMessage + '\'' +
                ", submitTime='" + submitTime + '\'' +
                ", completeTime='" + completeTime + '\'' +
                ", processedImage=" + processedImage +
                '}';
    }
}
