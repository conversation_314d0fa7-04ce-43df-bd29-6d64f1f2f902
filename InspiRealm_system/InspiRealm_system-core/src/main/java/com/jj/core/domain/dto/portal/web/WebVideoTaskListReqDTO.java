package com.jj.core.domain.dto.portal.web;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * Web端视频任务列表请求DTO
 *
 * <AUTHOR>
 * @date 2025-01-05
 */
@ApiModel(description = "Web端视频任务列表请求")
public class WebVideoTaskListReqDTO {

    @ApiModelProperty(value = "页码", required = true, example = "1")
    @NotNull(message = "页码不能为空")
    @Min(value = 1, message = "页码必须大于0")
    private Integer page;

    @ApiModelProperty(value = "每页大小", required = true, example = "10")
    @NotNull(message = "每页大小不能为空")
    @Min(value = 1, message = "每页大小必须大于0")
    @Max(value = 100, message = "每页大小不能超过100")
    private Integer pageSize;

    @ApiModelProperty(value = "任务状态", example = "3", notes = "1:待处理 2:处理中 3:成功 4:失败，不传则查询所有")
    private Integer status;

    public Integer getPage() {
        return page;
    }

    public void setPage(Integer page) {
        this.page = page;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    @Override
    public String toString() {
        return "WebVideoTaskListReqDTO{" +
                "page=" + page +
                ", pageSize=" + pageSize +
                ", status=" + status +
                '}';
    }
}