package com.jj.core.service.admin;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jj.common.exception.BizException;
import com.jj.core.domain.dto.admin.PointsManageReqDTO;
import com.jj.core.domain.dto.admin.UserManageDTO;
import com.jj.core.domain.dto.admin.UserStatusUpdateReqDTO;
import com.jj.core.domain.enums.PointsBusinessTypeEnum;
import com.jj.core.service.points.PointsService;
import com.jj.dao.mapper.UserInfoMapper;
import com.jj.dao.mapper.UserPointsMapper;
import com.jj.dao.model.points.UserPoints;
import com.jj.dao.model.user.UserInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jj.core.utils.DateFormatUtil;

import java.time.format.DateTimeFormatter;

/**
 * 管理员用户管理服务
 * 
 * <AUTHOR>
 * @date 2024-07-29
 */
@Service
public class AdminUserService {
    
    private static final Logger logger = LoggerFactory.getLogger(AdminUserService.class);
    
    @Autowired
    private UserInfoMapper userInfoMapper;
    
    @Autowired
    private UserPointsMapper userPointsMapper;
    
    @Autowired
    private PointsService pointsService;
    
    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    
    /**
     * 分页查询用户列表
     * 
     * @param page 页码
     * @param size 每页大小
     * @param keyword 搜索关键词
     * @param status 用户状态
     * @return 用户分页数据
     */
    public IPage<UserManageDTO> getUserList(Integer page, Integer size, String keyword, Integer status) {
        try {
            logger.info("分页查询用户列表，page: {}, size: {}, keyword: {}, status: {}", page, size, keyword, status);
            
            Page<UserInfo> pageParam = new Page<>(page, size);
            QueryWrapper<UserInfo> queryWrapper = new QueryWrapper<>();
            
            // 搜索条件
            if (keyword != null && !keyword.trim().isEmpty()) {
                queryWrapper.and(wrapper -> wrapper
                        .like("nickname", keyword)
                        .or()
                        .like("phone", keyword)
                        .or()
                        .like("email", keyword)
                );
            }
            
            // 状态筛选
            if (status != null) {
                queryWrapper.eq("status", status);
            }
            
            queryWrapper.orderByDesc("create_time");
            
            IPage<UserInfo> userPage = userInfoMapper.selectPage(pageParam, queryWrapper);
            
            // 转换为DTO
            IPage<UserManageDTO> dtoPage = userPage.convert(this::convertToUserManageDTO);
            
            logger.info("分页查询用户列表成功，total: {}", dtoPage.getTotal());
            return dtoPage;
            
        } catch (Exception e) {
            logger.error("分页查询用户列表异常", e);
            throw new BizException("查询用户列表失败");
        }
    }
    
    /**
     * 获取用户详情
     * 
     * @param userId 用户ID
     * @return 用户详情
     */
    public UserManageDTO getUserDetail(Long userId) {
        try {
            logger.info("获取用户详情，userId: {}", userId);
            
            UserInfo userInfo = userInfoMapper.selectById(userId);
            if (userInfo == null) {
                throw new BizException("用户不存在");
            }
            
            UserManageDTO userDTO = convertToUserManageDTO(userInfo);
            
            logger.info("获取用户详情成功，userId: {}", userId);
            return userDTO;
            
        } catch (BizException e) {
            logger.error("获取用户详情失败：{}", e.getMessage());
            throw e;
        } catch (Exception e) {
            logger.error("获取用户详情异常", e);
            throw new BizException("获取用户详情失败");
        }
    }
    
    /**
     * 更新用户状态
     * 
     * @param userId 用户ID
     * @param reqDTO 状态更新请求
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateUserStatus(Long userId, UserStatusUpdateReqDTO reqDTO) {
        try {
            logger.info("更新用户状态，userId: {}, status: {}, remark: {}", userId, reqDTO.getStatus(), reqDTO.getRemark());
            
            UserInfo userInfo = userInfoMapper.selectById(userId);
            if (userInfo == null) {
                throw new BizException("用户不存在");
            }
            
            if (userInfo.getStatus().equals(reqDTO.getStatus())) {
                throw new BizException("用户状态未发生变化");
            }
            
            // 更新用户状态
            userInfo.setStatus(reqDTO.getStatus());
            int updateResult = userInfoMapper.updateById(userInfo);
            if (updateResult <= 0) {
                throw new BizException("更新用户状态失败");
            }
            
            logger.info("更新用户状态成功，userId: {}, status: {}", userId, reqDTO.getStatus());
            
        } catch (BizException e) {
            logger.error("更新用户状态失败：{}", e.getMessage());
            throw e;
        } catch (Exception e) {
            logger.error("更新用户状态异常", e);
            throw new BizException("更新用户状态失败");
        }
    }
    
    /**
     * 积分管理操作
     * 
     * @param reqDTO 积分管理请求
     */
    @Transactional(rollbackFor = Exception.class)
    public void manageUserPoints(PointsManageReqDTO reqDTO) {
        try {
            logger.info("积分管理操作，userId: {}, amount: {}, type: {}, remark: {}", 
                    reqDTO.getUserId(), reqDTO.getPointsAmount(), reqDTO.getOperationType(), reqDTO.getRemark());
            
            // 验证用户是否存在
            UserInfo userInfo = userInfoMapper.selectById(reqDTO.getUserId());
            if (userInfo == null) {
                throw new BizException("用户不存在");
            }
            
            String businessNo = "ADMIN_" + System.currentTimeMillis();
            String remark = reqDTO.getRemark() != null ? reqDTO.getRemark() : "管理员操作";
            
            if (reqDTO.getOperationType() == 1) {
                // 充值积分
                pointsService.addPoints(reqDTO.getUserId(), reqDTO.getPointsAmount(), 
                        PointsBusinessTypeEnum.ADMIN_RECHARGE, businessNo, remark);
                logger.info("积分充值成功，userId: {}, amount: {}", reqDTO.getUserId(), reqDTO.getPointsAmount());
            } else if (reqDTO.getOperationType() == 2) {
                // 扣除积分
                boolean success = pointsService.consumePoints(reqDTO.getUserId(), reqDTO.getPointsAmount(), 
                        PointsBusinessTypeEnum.ADMIN_DEDUCT, businessNo, remark);
                if (!success) {
                    throw new BizException("用户积分不足，无法扣除");
                }
                logger.info("积分扣除成功，userId: {}, amount: {}", reqDTO.getUserId(), reqDTO.getPointsAmount());
            } else {
                throw new BizException("无效的操作类型");
            }
            
        } catch (BizException e) {
            logger.error("积分管理操作失败：{}", e.getMessage());
            throw e;
        } catch (Exception e) {
            logger.error("积分管理操作异常", e);
            throw new BizException("积分管理操作失败");
        }
    }
    
    /**
     * 转换为用户管理DTO
     * 
     * @param userInfo 用户信息
     * @return DTO
     */
    private UserManageDTO convertToUserManageDTO(UserInfo userInfo) {
        UserManageDTO dto = new UserManageDTO();
        dto.setId(userInfo.getId());
        dto.setOpenId(userInfo.getOpenId());
        dto.setNickname(userInfo.getNickname());
        dto.setAvatarUrl(userInfo.getAvatarUrl());
        dto.setGender(userInfo.getGender());
        dto.setPhone(userInfo.getPhone());
        dto.setEmail(userInfo.getEmail());
        dto.setStatus(userInfo.getStatus());
        dto.setStatusDesc(userInfo.getStatus() == 1 ? "正常" : "禁用");
        
        if (userInfo.getCreateTime() != null) {
            dto.setCreateTime(DateFormatUtil.format(userInfo.getCreateTime()));
        }
        if (userInfo.getLastLoginTime() != null) {
            dto.setLastLoginTime(DateFormatUtil.format(userInfo.getLastLoginTime()));
        }
        
        // 查询用户积分信息
        UserPoints userPoints = userPointsMapper.selectByUserId(userInfo.getId());
        if (userPoints != null) {
            dto.setTotalPoints(userPoints.getTotalPoints());
            dto.setAvailablePoints(userPoints.getAvailablePoints());
            dto.setLockedPoints(userPoints.getLockedPoints());
            dto.setConsumedPoints(userPoints.getConsumedPoints());
        } else {
            dto.setTotalPoints(0);
            dto.setAvailablePoints(0);
            dto.setLockedPoints(0);
            dto.setConsumedPoints(0);
        }
        
        return dto;
    }
}
