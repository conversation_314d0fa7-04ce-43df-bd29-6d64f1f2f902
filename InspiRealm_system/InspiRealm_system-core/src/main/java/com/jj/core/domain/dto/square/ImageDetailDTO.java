package com.jj.core.domain.dto.square;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 图片详情DTO
 * 
 * <AUTHOR>
 * @date 2024/12/19
 */
@ApiModel(description = "图片详情信息")
public class ImageDetailDTO {
    
    @ApiModelProperty(value = "图片ID")
    private Long id;
    
    @ApiModelProperty(value = "原始图片URL（参考图）")
    private String originalImageUrl;
    
    @ApiModelProperty(value = "处理后图片URL（结果图）")
    private String imageUrl;
    
    @ApiModelProperty(value = "缩略图URL")
    private String thumbnailUrl;
    
    @ApiModelProperty(value = "图片宽度")
    private Integer imageWidth;
    
    @ApiModelProperty(value = "图片高度")
    private Integer imageHeight;
    
    @ApiModelProperty(value = "图片比例")
    private String aspectRatio;
    
    @ApiModelProperty(value = "处理类型")
    private String processType;
    
    @ApiModelProperty(value = "提示词")
    private String prompt;
    
    @ApiModelProperty(value = "负面提示词")
    private String negativePrompt;
    
    @ApiModelProperty(value = "查看数")
    private Integer viewCount;
    
    @ApiModelProperty(value = "点赞数")
    private Integer likeCount;

    @ApiModelProperty(value = "是否已点赞")
    private Boolean isLiked;
    
    @ApiModelProperty(value = "是否公开")
    private Boolean isPublic;
    
    @ApiModelProperty(value = "审核状态：0-待审核，1-审核通过，2-审核拒绝")
    private Integer auditStatus;
    
    @ApiModelProperty(value = "审核备注")
    private String auditRemark;
    
    @ApiModelProperty(value = "是否删除")
    private Boolean isDeleted;
    
    @ApiModelProperty(value = "创建时间")
    private String createTime;
    
    @ApiModelProperty(value = "作者信息")
    private UserInfoDTO userInfo;

    /**
     * 用户信息
     */
    @ApiModel(description = "用户信息")
    public static class UserInfoDTO {
        
        @ApiModelProperty(value = "用户昵称")
        private String nickname;
        
        @ApiModelProperty(value = "用户头像")
        private String avatarUrl;
        
        public String getNickname() {
            return nickname;
        }
        
        public void setNickname(String nickname) {
            this.nickname = nickname;
        }
        
        public String getAvatarUrl() {
            return avatarUrl;
        }
        
        public void setAvatarUrl(String avatarUrl) {
            this.avatarUrl = avatarUrl;
        }
    }
    
    // Getters and Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getOriginalImageUrl() {
        return originalImageUrl;
    }
    
    public void setOriginalImageUrl(String originalImageUrl) {
        this.originalImageUrl = originalImageUrl;
    }
    
    public String getImageUrl() {
        return imageUrl;
    }
    
    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }
    
    public String getThumbnailUrl() {
        return thumbnailUrl;
    }
    
    public void setThumbnailUrl(String thumbnailUrl) {
        this.thumbnailUrl = thumbnailUrl;
    }
    
    public Integer getImageWidth() {
        return imageWidth;
    }
    
    public void setImageWidth(Integer imageWidth) {
        this.imageWidth = imageWidth;
    }
    
    public Integer getImageHeight() {
        return imageHeight;
    }
    
    public void setImageHeight(Integer imageHeight) {
        this.imageHeight = imageHeight;
    }
    
    public String getAspectRatio() {
        return aspectRatio;
    }
    
    public void setAspectRatio(String aspectRatio) {
        this.aspectRatio = aspectRatio;
    }
    
    public String getProcessType() {
        return processType;
    }
    
    public void setProcessType(String processType) {
        this.processType = processType;
    }
    
    public String getPrompt() {
        return prompt;
    }
    
    public void setPrompt(String prompt) {
        this.prompt = prompt;
    }
    
    public String getNegativePrompt() {
        return negativePrompt;
    }
    
    public void setNegativePrompt(String negativePrompt) {
        this.negativePrompt = negativePrompt;
    }
    
    public Integer getViewCount() {
        return viewCount;
    }
    
    public void setViewCount(Integer viewCount) {
        this.viewCount = viewCount;
    }
    
    public Integer getLikeCount() {
        return likeCount;
    }

    public void setLikeCount(Integer likeCount) {
        this.likeCount = likeCount;
    }

    public Boolean getIsLiked() {
        return isLiked;
    }
    
    public void setIsLiked(Boolean isLiked) {
        this.isLiked = isLiked;
    }
    
    public Boolean getIsPublic() {
        return isPublic;
    }
    
    public void setIsPublic(Boolean isPublic) {
        this.isPublic = isPublic;
    }
    
    public Integer getAuditStatus() {
        return auditStatus;
    }
    
    public void setAuditStatus(Integer auditStatus) {
        this.auditStatus = auditStatus;
    }
    
    public String getAuditRemark() {
        return auditRemark;
    }
    
    public void setAuditRemark(String auditRemark) {
        this.auditRemark = auditRemark;
    }
    
    public Boolean getIsDeleted() {
        return isDeleted;
    }
    
    public void setIsDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
    }
    
    public String getCreateTime() {
        return createTime;
    }
    
    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }
    
    public UserInfoDTO getUserInfo() {
        return userInfo;
    }

    public void setUserInfo(UserInfoDTO userInfo) {
        this.userInfo = userInfo;
    }
}
