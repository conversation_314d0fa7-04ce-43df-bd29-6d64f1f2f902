package com.jj.core.domain.dto.portal.web;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * Web端图片上传响应DTO
 *
 * <AUTHOR>
 * @date 2025-01-05
 */
@ApiModel(description = "Web端图片上传响应")
public class WebImageUploadResDTO {

    @ApiModelProperty(value = "图片URL列表")
    private List<String> imageUrls;

    @ApiModelProperty(value = "上传成功的图片数量", example = "3")
    private Integer count;

    public List<String> getImageUrls() {
        return imageUrls;
    }

    public void setImageUrls(List<String> imageUrls) {
        this.imageUrls = imageUrls;
    }

    public Integer getCount() {
        return count;
    }

    public void setCount(Integer count) {
        this.count = count;
    }

    @Override
    public String toString() {
        return "WebImageUploadResDTO{" +
                "imageUrls=" + imageUrls +
                ", count=" + count +
                '}';
    }
}