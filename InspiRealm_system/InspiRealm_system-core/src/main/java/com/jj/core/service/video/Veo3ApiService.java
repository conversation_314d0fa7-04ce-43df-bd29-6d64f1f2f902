package com.jj.core.service.video;

import com.jj.dao.model.video.VideoGenerationTask;

/**
 * Veo3 API调用服务接口
 *
 * <AUTHOR>
 * @date 2025-01-05
 */
public interface Veo3ApiService {

    /**
     * 创建视频生成任务
     *
     * @param task 视频生成任务
     * @return 外部任务ID
     */
    String createVideoTask(VideoGenerationTask task);

    /**
     * 查询视频生成任务状态
     *
     * @param externalTaskId 外部任务ID
     * @return 任务状态信息
     */
    Veo3TaskStatus queryTaskStatus(String externalTaskId);

    /**
     * Veo3任务状态信息
     */
    class Veo3TaskStatus {
        private String id;
        private String status;
        private String videoUrl;
        private String errorMessage;
        private Long statusUpdateTime;

        // Getters and Setters
        public String getId() { return id; }
        public void setId(String id) { this.id = id; }
        public String getStatus() { return status; }
        public void setStatus(String status) { this.status = status; }
        public String getVideoUrl() { return videoUrl; }
        public void setVideoUrl(String videoUrl) { this.videoUrl = videoUrl; }
        public String getErrorMessage() { return errorMessage; }
        public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }
        public Long getStatusUpdateTime() { return statusUpdateTime; }
        public void setStatusUpdateTime(Long statusUpdateTime) { this.statusUpdateTime = statusUpdateTime; }
    }
}