package com.jj.core.service.image;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jj.common.exception.BizException;
import com.jj.core.domain.dto.image.*;
import com.jj.core.domain.enums.PointsBusinessTypeEnum;
import com.jj.core.domain.enums.TaskStatusEnum;
import com.jj.core.manager.MinioManager;
import com.jj.core.manager.SoraImageManager;
import com.jj.core.service.points.PointsService;
import com.jj.dao.mapper.ImageProcessTaskMapper;
import com.jj.dao.mapper.ProcessedImageMapper;
import com.jj.dao.model.image.ImageProcessTask;
import com.jj.dao.model.image.ProcessedImage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.*;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import com.jj.core.utils.DateFormatUtil;

/**
 * 图片处理服务
 *
 * <AUTHOR>
 * @date 2024-07-29
 */
@Service
public class ImageProcessService {

    private static final Logger logger = LoggerFactory.getLogger(ImageProcessService.class);

    @Autowired
    private ImageProcessTaskMapper imageProcessTaskMapper;

    @Autowired
    private ProcessedImageMapper processedImageMapper;

    @Autowired
    private PointsService pointsService;

    @Autowired
    private ImageProcessAsyncService imageProcessAsyncService;

    @Autowired
    private MinioManager minioManager;

    @Autowired
    @Qualifier("aiRestTemplate")
    private RestTemplate restTemplate;

    /**
     * 图片处理消耗积分
     */
    @Value("${points.image-process-cost:5}")
    private Integer imageProcessCost;

    @Value("${ai.sora-image-api-url:}")
    private String soraImageApiUrl;

    @Value("${ai.sora-image-api-key:}")
    private String soraImageApiKey;

    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    /**
     * 创建图片处理任务
     *
     * @param userId 用户ID
     * @param reqDTO 处理请求
     * @return 处理响应
     */
    @Transactional(rollbackFor = Exception.class)
    public ImageProcessResDTO createProcessTask(Long userId, ImageProcessReqDTO reqDTO) {
        try {
            logger.info("创建图片处理任务，userId: {}, reqDTO: {}", userId, reqDTO);

            // 验证请求参数
            validateProcessRequest(reqDTO);

            // 处理Base64图片上传到MinIO
            String processedImageUrl = null;
            if (reqDTO.getImageBase64() != null && !reqDTO.getImageBase64().trim().isEmpty()) {
                try {
                    logger.info("开始将Base64图片上传到MinIO，userId: {}", userId);
                   MinioManager.MinioUploadResult uploadResult = minioManager.uploadImageFromBase64(reqDTO.getImageBase64(), userId);
                    if (uploadResult.isSuccess()) {
                        processedImageUrl = uploadResult.getImageUrl();
                        // 将Base64转换为URL，后续处理使用URL
                        reqDTO.setImageUrl(processedImageUrl);
                        reqDTO.setImageBase64(null); // 清空Base64数据
                        String aspectRatio = imageProcessAsyncService.calculateAspectRatio(uploadResult.getImageWidth(), uploadResult.getImageHeight());
                        reqDTO.setAspectRatio(aspectRatio);
                        logger.info("Base64图片上传成功，userId: {}, imageUrl: {}", userId, processedImageUrl);
                    } else {
                        throw new BizException("图片上传失败");
                    }
                } catch (Exception e) {
                    logger.error("Base64图片上传失败，userId: {}", userId, e);
                    throw new BizException("图片上传失败: " + e.getMessage());
                }
            }

            // 锁定积分
            boolean lockSuccess = pointsService.lockPoints(userId, imageProcessCost, null);
            if (!lockSuccess) {
                throw new BizException("积分不足，无法处理图片");
            }

            // 生成任务编号
            String taskNo = generateTaskNo();

            // 创建任务记录
            ImageProcessTask task = new ImageProcessTask();
            task.setTaskNo(taskNo);
            task.setUserId(userId);
            task.setPrompt(reqDTO.getPrompt());
            task.setOriginalImageUrl(reqDTO.getImageUrl());
            task.setOriginalImageBase64(reqDTO.getImageBase64());
            task.setAspectRatio(reqDTO.getAspectRatio());
            task.setProcessType(reqDTO.getProcessType());
            task.setTextContent(reqDTO.getTextContent());
            task.setStyleType(reqDTO.getStyle());
            task.setPointsCost(imageProcessCost);
            task.setStatus(TaskStatusEnum.PENDING.getCode());
            task.setProgress(0);

            int insertResult = imageProcessTaskMapper.insert(task);
            if (insertResult <= 0) {
                throw new BizException("创建任务失败");
            }

            // 异步处理任务
            imageProcessAsyncService.processImageAsync(task.getId(), taskNo);

            // 构建响应
            ImageProcessResDTO resDTO = new ImageProcessResDTO();
            resDTO.setTaskNo(taskNo);
            resDTO.setPointsCost(imageProcessCost);
            resDTO.setEstimatedTime(30); // 预计30秒
            resDTO.setStatus(TaskStatusEnum.PENDING.getCode());
            resDTO.setStatusDesc(TaskStatusEnum.PENDING.getDesc());
            resDTO.setCreateTime(DateFormatUtil.format(new Date()));

            logger.info("图片处理任务创建成功，taskNo: {}, userId: {}", taskNo, userId);
            return resDTO;

        } catch (BizException e) {
            logger.error("创建图片处理任务失败：{}", e.getMessage());
            throw e;
        } catch (Exception e) {
            logger.error("创建图片处理任务异常", e);
            throw new BizException("创建任务失败，请重试");
        }
    }

    /**
     * 查询任务状态
     *
     * @param taskNo 任务编号
     * @param userId 用户ID
     * @return 任务状态
     */
    public TaskStatusDTO getTaskStatus(String taskNo, Long userId) {
        try {
            logger.info("查询任务状态，taskNo: {}, userId: {}", taskNo, userId);

            // 查询任务
            ImageProcessTask task = imageProcessTaskMapper.selectByTaskNo(taskNo);
            if (task == null) {
                throw new BizException("任务不存在");
            }

            // 验证权限
            if (!task.getUserId().equals(userId)) {
                throw new BizException("无权限查看该任务");
            }

            // 构建状态DTO
            TaskStatusDTO statusDTO = new TaskStatusDTO();
            statusDTO.setTaskNo(task.getTaskNo());
            statusDTO.setStatus(task.getStatus());
            statusDTO.setStatusDesc(TaskStatusEnum.getDescByCode(task.getStatus()));
            statusDTO.setProgress(task.getProgress());
            statusDTO.setErrorMessage(task.getErrorMessage());

            if (task.getStartTime() != null) {
                statusDTO.setStartTime(DateFormatUtil.format(task.getStartTime()));
            }
            if (task.getEndTime() != null) {
                statusDTO.setEndTime(DateFormatUtil.format(task.getEndTime()));
            }
            if (task.getCreateTime() != null) {
                statusDTO.setCreateTime(DateFormatUtil.format(task.getCreateTime()));
            }

            // 如果任务成功，查询处理后的图片
            if (TaskStatusEnum.SUCCESS.getCode().equals(task.getStatus())) {
                ProcessedImage processedImage = processedImageMapper.selectByTaskId(task.getId());
                if (processedImage != null) {
                    ProcessedImageDTO imageDTO = convertToProcessedImageDTO(processedImage);
                    statusDTO.setProcessedImage(imageDTO);
                }
            }

            logger.info("查询任务状态成功，taskNo: {}, status: {}", taskNo, task.getStatus());
            return statusDTO;

        } catch (BizException e) {
            logger.error("查询任务状态失败：{}", e.getMessage());
            throw e;
        } catch (Exception e) {
            logger.error("查询任务状态异常", e);
            throw new BizException("查询任务状态失败");
        }
    }

    /**
     * 分页查询用户图片
     *
     * @param userId 用户ID
     * @param page 页码
     * @param size 每页大小
     * @param isPublic 是否公开
     * @return 图片分页数据
     */
    public IPage<ProcessedImageDTO> getUserImages(Long userId, Integer page, Integer size, Integer isPublic) {
        try {
            logger.info("分页查询用户图片，userId: {}, page: {}, size: {}, isPublic: {}", userId, page, size, isPublic);

            Page<ProcessedImage> pageParam = new Page<>(page, size);
            IPage<ProcessedImage> imagePage = processedImageMapper.selectUserImages(pageParam, userId, isPublic);

            // 转换为DTO
            IPage<ProcessedImageDTO> dtoPage = imagePage.convert(this::convertToProcessedImageDTO);

            logger.info("分页查询用户图片成功，userId: {}, total: {}", userId, dtoPage.getTotal());
            return dtoPage;

        } catch (Exception e) {
            logger.error("分页查询用户图片异常", e);
            throw new BizException("查询图片失败");
        }
    }

    /**
     * 设置图片公开状态
     *
     * @param imageId 图片ID
     * @param isPublic 是否公开
     * @param userId 用户ID
     */
    @Transactional(rollbackFor = Exception.class)
    public void setImagePublicStatus(Long imageId, Integer isPublic, Long userId) {
        try {
            logger.info("设置图片公开状态，imageId: {}, isPublic: {}, userId: {}", imageId, isPublic, userId);

            // 查询图片信息（使用QueryWrapper过滤del字段）
            ProcessedImage image = processedImageMapper.selectOne(
                new com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<ProcessedImage>()
                    .eq("id", imageId)
                    .eq("del", 0)
            );
            if (image == null || !image.getUserId().equals(userId)) {
                throw new BizException("图片不存在或无权限");
            }

            // 更新公开状态和审核状态
            image.setIsPublic(isPublic);

            if (isPublic == 1) {
                // 申请公开时，设置为待审核状态
//                image.setAuditStatus(0);
//                image.setAuditRemark("用户申请公开，待管理员审核");
                image.setAuditStatus(1);
                image.setAuditRemark("审核通过");
            } else {
                // 设为私有时，无需审核
                image.setAuditStatus(1);
                image.setAuditRemark("私有图片，无需审核");
            }

            int updateResult = processedImageMapper.updateById(image);
            if (updateResult <= 0) {
                throw new BizException("设置失败");
            }

            logger.info("设置图片公开状态成功，imageId: {}, isPublic: {}, auditStatus: {}",
                       imageId, isPublic, image.getAuditStatus());

        } catch (BizException e) {
            logger.error("设置图片公开状态失败：{}", e.getMessage());
            throw e;
        } catch (Exception e) {
            logger.error("设置图片公开状态异常", e);
            throw new BizException("设置失败，请重试");
        }
    }

    /**
     * 分页查询用户任务
     *
     * @param userId 用户ID
     * @param page 页码
     * @param size 每页大小
     * @param status 任务状态
     * @param processType 处理类型
     * @return 任务分页数据
     */
    public IPage<TaskInfoDTO> getUserTasks(Long userId, Integer page, Integer size, Integer status, String processType) {
        try {
            logger.info("分页查询用户任务，userId: {}, page: {}, size: {}, status: {}, processType: {}", userId, page, size, status, processType);

            Page<ImageProcessTask> pageParam = new Page<>(page, size);
            IPage<ImageProcessTask> taskPage = imageProcessTaskMapper.selectUserTasks(pageParam, userId, status, processType);

            // 转换为DTO
            IPage<TaskInfoDTO> dtoPage = taskPage.convert(this::convertToTaskInfoDTO);

            logger.info("分页查询用户任务成功，userId: {}, total: {}", userId, dtoPage.getTotal());
            return dtoPage;

        } catch (Exception e) {
            logger.error("分页查询用户任务异常，userId: {}", userId, e);
            throw new BizException("查询失败，请重试");
        }
    }

    /**
     * 重新处理任务
     *
     * @param taskNo 原任务编号
     * @param userId 用户ID
     * @return 新任务信息
     */
    @Transactional(rollbackFor = Exception.class)
    public ImageProcessResDTO retryTask(String taskNo, Long userId) {
        try {
            logger.info("重新处理任务，taskNo: {}, userId: {}", taskNo, userId);

            // 查询原任务
            ImageProcessTask originalTask = imageProcessTaskMapper.selectByTaskNo(taskNo);
            if (originalTask == null) {
                throw new BizException("原任务不存在");
            }

            // 验证任务所有者
            if (!originalTask.getUserId().equals(userId)) {
                throw new BizException("无权限重新处理此任务");
            }

            // 只有失败的任务才能重新处理
            if (!TaskStatusEnum.FAILED.getCode().equals(originalTask.getStatus())) {
                throw new BizException("只有失败的任务才能重新处理");
            }

            // 检查用户积分是否足够
            Integer pointsCost = originalTask.getPointsCost();
            if (pointsCost == null || pointsCost <= 0) {
                pointsCost = imageProcessCost; // 使用默认积分消耗
            }

            if (!pointsService.hasEnoughPoints(userId, pointsCost)) {
                throw new BizException("积分不足，无法重新处理");
            }

            // 锁定积分
            boolean lockSuccess = pointsService.lockPoints(userId, pointsCost, "RETRY_" + taskNo);
            if (!lockSuccess) {
                throw new BizException("积分不足，无法重新处理");
            }

            // 生成新任务编号
            String newTaskNo = generateTaskNo();

            // 创建新任务记录，复制原任务的所有参数
            ImageProcessTask newTask = new ImageProcessTask();
            newTask.setTaskNo(newTaskNo);
            newTask.setUserId(userId);
            newTask.setPrompt(originalTask.getPrompt());
            newTask.setOriginalImageUrl(originalTask.getOriginalImageUrl());
            newTask.setOriginalImageBase64(originalTask.getOriginalImageBase64());
            newTask.setAspectRatio(originalTask.getAspectRatio());
            newTask.setProcessType(originalTask.getProcessType());
            newTask.setTextContent(originalTask.getTextContent());
            newTask.setStyleType(originalTask.getStyleType());
            newTask.setPointsCost(pointsCost);
            newTask.setStatus(TaskStatusEnum.PENDING.getCode());
            newTask.setProgress(0);

            int insertResult = imageProcessTaskMapper.insert(newTask);
            if (insertResult <= 0) {
                // 插入失败，回退积分
                pointsService.refundPoints(userId, pointsCost, "RETRY_" + taskNo);
                throw new BizException("创建新任务失败");
            }

            // 异步处理新任务
            imageProcessAsyncService.processImageAsync(newTask.getId(), newTaskNo);

            // 构建响应
            ImageProcessResDTO resDTO = new ImageProcessResDTO();
            resDTO.setTaskNo(newTaskNo);
            resDTO.setPointsCost(pointsCost);
            resDTO.setEstimatedTime(30); // 预计30秒
            resDTO.setStatus(TaskStatusEnum.PENDING.getCode());
            resDTO.setStatusDesc(TaskStatusEnum.PENDING.getDesc());
            resDTO.setCreateTime(DateFormatUtil.format(new Date()));

            logger.info("重新处理任务成功，originalTaskNo: {}, newTaskNo: {}, userId: {}",
                taskNo, newTaskNo, userId);
            return resDTO;

        } catch (BizException e) {
            logger.error("重新处理任务失败：{}", e.getMessage());
            throw e;
        } catch (Exception e) {
            logger.error("重新处理任务异常", e);
            throw new BizException("重新处理任务失败，请重试");
        }
    }

    /**
     * 删除用户图片
     *
     * @param imageId 图片ID
     * @param userId 用户ID
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteUserImage(Long imageId, Long userId) {
        try {
            logger.info("删除用户图片，imageId: {}, userId: {}", imageId, userId);

            // 查询图片
            ProcessedImage image = processedImageMapper.selectById(imageId);
            if (image == null) {
                throw new BizException("图片不存在");
            }

            // 验证图片所有者
            if (!image.getUserId().equals(userId)) {
                throw new BizException("无权限删除此图片");
            }

            // 1. 先删除MinIO文件
            deleteImageFiles(image);

            // 2. 删除图片记录（逻辑删除）
            int deleteResult = processedImageMapper.deleteById(imageId);
            if (deleteResult <= 0) {
                throw new BizException("删除图片失败");
            }

            logger.info("删除用户图片成功，imageId: {}, userId: {}", imageId, userId);

        } catch (BizException e) {
            logger.error("删除用户图片失败：{}", e.getMessage());
            throw e;
        } catch (Exception e) {
            logger.error("删除用户图片异常", e);
            throw new BizException("删除图片失败，请重试");
        }
    }

    /**
     * 删除任务
     *
     * 删除规则：
     * 1. 只能删除自己的任务
     * 2. 处理中的任务不能删除
     * 3. 成功/失败的任务都可以删除
     * 4. 删除时不处理积分退还（成功任务积分已消费，失败任务积分已退还）
     *
     * @param taskNo 任务编号
     * @param userId 用户ID
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteTask(String taskNo, Long userId) {
        try {
            logger.info("删除任务，taskNo: {}, userId: {}", taskNo, userId);

            // 查询任务
            ImageProcessTask task = imageProcessTaskMapper.selectByTaskNo(taskNo);
            if (task == null) {
                throw new BizException("任务不存在");
            }
            // 验证权限
            if (!task.getUserId().equals(userId)) {
                throw new BizException("无权限删除该任务");
            }

            // 检查任务状态，处理中的任务不能删除
            if (TaskStatusEnum.PROCESSING.getCode().equals(task.getStatus())) {
                throw new BizException("处理中的任务不能删除");
            }

            // 如果任务成功完成，需要同时删除相关的处理后图片记录
            if (TaskStatusEnum.SUCCESS.getCode().equals(task.getStatus())) {
                // 先查询相关图片，删除MinIO文件
                ProcessedImage processedImage = processedImageMapper.selectByTaskId(task.getId());
                if (processedImage != null) {
                    deleteImageFiles(processedImage);
                }

                // 删除处理后图片记录
                processedImageMapper.deleteByTaskId(task.getId());
                logger.info("已删除相关的处理后图片记录，taskId: {}", task.getId());
            }

            // 删除任务记录
            int deleteResult = imageProcessTaskMapper.deleteByTaskNo(taskNo);
            if (deleteResult <= 0) {
                throw new BizException("删除任务失败");
            }

            // 注意：不需要处理积分退还
            // - 成功的任务：积分已正常消费，删除不退还
            // - 失败的任务：系统已自动退还积分
            // - 处理中的任务：不允许删除

            logger.info("删除任务成功，taskNo: {}, userId: {}", taskNo, userId);

        } catch (BizException e) {
            logger.error("删除任务失败：{}", e.getMessage());
            throw e;
        } catch (Exception e) {
            logger.error("删除任务异常", e);
            throw new BizException("删除任务失败，请重试");
        }
    }

    /**
     * 润色提示词
     *
     * @param originalPrompt 原始提示词
     * @param imageBase64 图片Base64（可选）
     * @return 润色后的提示词
     */
    @Transactional(rollbackFor = Exception.class)
    public String polishPrompt(String originalPrompt, String imageBase64) {
        try {
            boolean hasImage = imageBase64 != null && !imageBase64.trim().isEmpty();
            logger.info("开始润色提示词，原始内容: {}, 是否包含图片: {}", originalPrompt, hasImage);

            // 调用OpenAI API进行润色
            String polishedPrompt = callOpenAIForPolish(originalPrompt, imageBase64);

            // 清理AI返回的内容
            String cleanedPrompt = cleanAIResponse(polishedPrompt);

            logger.info("提示词润色完成，原始: {}, 润色后: {}", originalPrompt, cleanedPrompt);
            return cleanedPrompt;

        } catch (Exception e) {
            logger.error("润色提示词失败，原始内容: {}", originalPrompt, e);
            throw new BizException("润色失败，请稍后重试");
        }
    }

    /**
     * 调用OpenAI API进行润色
     */
    private String callOpenAIForPolish(String originalPrompt, String imageBase64) {
        try {
            boolean hasImage = imageBase64 != null && !imageBase64.trim().isEmpty();

            // 构建请求体
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("model", "o4-mini");
            requestBody.put("max_tokens", 1000);
            requestBody.put("temperature", 0.7);

            List<Map<String, Object>> messages = new ArrayList<>();

            // 添加系统消息
            Map<String, Object> systemMessage = new HashMap<>();
            systemMessage.put("role", "system");
            systemMessage.put("content", "You are a helpful assistant that optimizes image generation prompts. Please provide detailed, accurate, and safe prompts in English. Avoid any inappropriate content.");
            messages.add(systemMessage);

            // 构建用户消息
            Map<String, Object> userMessage = new HashMap<>();
            userMessage.put("role", "user");

            if (hasImage) {
                // 有图片时，使用多模态格式
                List<Map<String, Object>> content = new ArrayList<>();

                // 添加文本内容
                Map<String, Object> textContent = new HashMap<>();
                textContent.put("type", "text");
                textContent.put("text", "Please analyze this image and optimize the following prompt based on what you see. Create a detailed English prompt for image generation: " + originalPrompt);
                content.add(textContent);

                // 添加图片内容
                Map<String, Object> imageContent = new HashMap<>();
                imageContent.put("type", "image_url");
                Map<String, Object> imageUrl = new HashMap<>();
                imageUrl.put("url", imageBase64);
                imageContent.put("image_url", imageUrl);
                content.add(imageContent);

                userMessage.put("content", content);
            } else {
                // 没有图片时，使用纯文本格式
                userMessage.put("content", "Please optimize the following image generation prompt to be more detailed, accurate, and safe. Return only the optimized English prompt without any explanations: " + originalPrompt);
            }

            messages.add(userMessage);
            requestBody.put("messages", messages);

            // 构建请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.setBearerAuth(soraImageApiKey);

            HttpEntity<Map<String, Object>> httpEntity = new HttpEntity<>(requestBody, headers);

            // 调用API
            ResponseEntity<String> response = restTemplate.postForEntity(soraImageApiUrl, httpEntity, String.class);

            if (response.getStatusCode().is2xxSuccessful()) {
                return parseOpenAIResponse(response.getBody());
            } else {
                throw new BizException("AI服务调用失败");
            }

        } catch (Exception e) {
            logger.error("调用OpenAI API失败", e);
            throw new BizException("AI服务暂时不可用，请稍后重试");
        }
    }

    /**
     * 解析OpenAI响应
     */
    private String parseOpenAIResponse(String responseBody) {
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            JsonNode rootNode = objectMapper.readTree(responseBody);

            JsonNode choicesNode = rootNode.get("choices");
            if (choicesNode != null && choicesNode.isArray() && choicesNode.size() > 0) {
                JsonNode firstChoice = choicesNode.get(0);
                JsonNode messageNode = firstChoice.get("message");
                if (messageNode != null) {
                    JsonNode contentNode = messageNode.get("content");
                    if (contentNode != null) {
                        return contentNode.asText();
                    }
                }
            }

            throw new BizException("AI响应格式异常");

        } catch (Exception e) {
            logger.error("解析AI响应失败", e);
            throw new BizException("AI响应解析失败");
        }
    }

    /**
     * 清理AI返回的内容，去除代码块等格式标记
     */
    private String cleanAIResponse(String aiResponse) {
        if (aiResponse == null || aiResponse.trim().isEmpty()) {
            throw new BizException("AI返回内容为空");
        }

        String cleaned = aiResponse.trim();

        // 去除代码块标记
        cleaned = cleaned.replaceAll("```[\\s\\S]*?```", "");
        cleaned = cleaned.replaceAll("```", "");

        // 去除markdown格式
        cleaned = cleaned.replaceAll("\\*\\*(.*?)\\*\\*", "$1"); // 粗体
        cleaned = cleaned.replaceAll("\\*(.*?)\\*", "$1"); // 斜体
        cleaned = cleaned.replaceAll("__(.*?)__", "$1"); // 下划线
        cleaned = cleaned.replaceAll("~~(.*?)~~", "$1"); // 删除线

        // 去除引号
        cleaned = cleaned.replaceAll("^[\"'`]|[\"'`]$", "");

        // 去除多余的换行和空格
        cleaned = cleaned.replaceAll("\\n+", " ");
        cleaned = cleaned.replaceAll("\\s+", " ");

        // 去除常见的AI回复前缀
        cleaned = cleaned.replaceAll("^(优化后的提示词：?|润色后：?|修改后：?|建议：?)", "");

        cleaned = cleaned.trim();

        if (cleaned.isEmpty()) {
            throw new BizException("润色后内容为空");
        }

        // 限制长度
        if (cleaned.length() > 800) {
            cleaned = cleaned.substring(0, 800) + "...";
        }

        return cleaned;
    }

    /**
     * 删除图片相关的MinIO文件
     *
     * @param image 图片信息
     */
    private void deleteImageFiles(ProcessedImage image) {
        if (image == null) {
            return;
        }

        logger.info("开始删除图片文件，imageId: {}", image.getId());

        try {
            // 删除主图片
            deleteMinioFileByUrl(image.getImageUrl(), "主图片");

            // 删除缩略图
            deleteMinioFileByUrl(image.getThumbnailUrl(), "缩略图");

            // 删除原图
            deleteMinioFileByUrl(image.getOriginalImageUrl(), "原图");

            logger.info("图片文件删除完成，imageId: {}", image.getId());

        } catch (Exception e) {
            logger.error("删除图片文件时发生异常，imageId: {}", image.getId(), e);
            // 不抛出异常，避免影响数据库删除操作
        }
    }

    /**
     * 通过URL删除MinIO文件
     *
     * @param url 文件URL
     * @param fileType 文件类型描述
     */
    private void deleteMinioFileByUrl(String url, String fileType) {
        if (url == null || url.trim().isEmpty()) {
            logger.debug("{}URL为空，跳过删除", fileType);
            return;
        }

        try {
            boolean deleted = minioManager.deleteFileByUrl(url);
            if (deleted) {
                logger.debug("{}删除成功，url: {}", fileType, url);
            } else {
                logger.debug("{}删除跳过（非MinIO地址或提取失败），url: {}", fileType, url);
            }
        } catch (Exception e) {
            logger.error("{}删除失败，url: {}", fileType, url, e);
            // 不抛出异常，避免影响主流程
        }
    }

    /**
     * 验证处理请求
     *
     * @param reqDTO 请求DTO
     */
    private void validateProcessRequest(ImageProcessReqDTO reqDTO) {
        if (reqDTO.getPrompt() == null || reqDTO.getPrompt().trim().isEmpty()) {
            throw new BizException("处理要求描述不能为空");
        }

        // 检查是否为文字生成模式（通过processType判断）
        boolean isTextGeneration = isTextGenerationMode(reqDTO.getProcessType());

        if (!isTextGeneration) {
            // 图片处理模式：必须提供图片
            if (reqDTO.getImageUrl() == null && reqDTO.getImageBase64() == null) {
                throw new BizException("图片处理模式必须提供图片URL或Base64数据");
            }
        }

        if (reqDTO.getImageUrl() != null && reqDTO.getImageBase64() != null) {
            throw new BizException("图片URL和Base64数据只能提供其中一种");
        }
    }

    /**
     * 判断是否为文字生成模式
     *
     * @param processType 处理类型
     * @return 是否为文字生成模式
     */
    private boolean isTextGenerationMode(String processType) {
        if (processType == null) {
            return false;
        }

        // 文字生成模式的处理类型
        return processType.equals("realistic") ||
               processType.equals("artistic") ||
               processType.equals("anime") ||
               processType.equals("fantasy");
    }

    /**
     * 生成任务编号
     *
     * @return 任务编号
     */
    private String generateTaskNo() {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        String uuid = UUID.randomUUID().toString().replace("-", "").substring(0, 8).toUpperCase();
        return "TASK_" + timestamp + "_" + uuid;
    }

    /**
     * 转换为TaskInfoDTO
     *
     * @param task 任务实体
     * @return DTO
     */
    private TaskInfoDTO convertToTaskInfoDTO(ImageProcessTask task) {
        TaskInfoDTO dto = new TaskInfoDTO();
        dto.setTaskNo(task.getTaskNo());
        dto.setOriginalImageUrl(task.getOriginalImageUrl());
        dto.setProcessType(task.getProcessType());
        dto.setStatus(task.getStatus());
        dto.setStatusDesc(getStatusDesc(task.getStatus()));
        dto.setProgress(task.getProgress());
        dto.setErrorMessage(task.getErrorMessage());

        if (task.getCreateTime() != null) {
            dto.setSubmitTime(DateFormatUtil.format(task.getCreateTime()));
        }
        if (task.getEndTime() != null) {
            dto.setCompleteTime(DateFormatUtil.format(task.getEndTime()));
        }

        // 如果任务已完成，查询处理后的图片信息
        if (task.getStatus() == TaskStatusEnum.SUCCESS.getCode()) {
            ProcessedImage processedImage = processedImageMapper.selectByTaskId(task.getId());
            if (processedImage != null) {
                dto.setResultImageUrl(processedImage.getImageUrl());
                ProcessedImageDTO imageDTO = convertToProcessedImageDTO(processedImage);
                dto.setProcessedImage(imageDTO);
            }
        }

        return dto;
    }

    /**
     * 获取状态描述
     *
     * @param status 状态码
     * @return 状态描述
     */
    private String getStatusDesc(Integer status) {
        if (status == null) return "未知";

        switch (status) {
            case 1: return "待处理";
            case 2: return "处理中";
            case 3: return "处理成功";
            case 4: return "处理失败";
            case 5: return "已取消";
            default: return "未知状态";
        }
    }

    /**
     * 转换为ProcessedImageDTO
     *
     * @param processedImage 处理后图片实体
     * @return DTO
     */
    private ProcessedImageDTO convertToProcessedImageDTO(ProcessedImage processedImage) {
        ProcessedImageDTO dto = new ProcessedImageDTO();
        dto.setId(processedImage.getId());
        dto.setTaskId(processedImage.getTaskId());
        dto.setImageUrl(processedImage.getImageUrl());
        dto.setThumbnailUrl(processedImage.getThumbnailUrl());
        dto.setOriginalImageUrl(processedImage.getOriginalImageUrl());
        dto.setFileName(processedImage.getFileName());
        dto.setFileSize(processedImage.getFileSize());
        dto.setImageWidth(processedImage.getImageWidth());
        dto.setImageHeight(processedImage.getImageHeight());
        dto.setImageFormat(processedImage.getImageFormat());
        dto.setAspectRatio(processedImage.getAspectRatio());
        dto.setProcessType(processedImage.getProcessType());
        dto.setIsPublic(processedImage.getIsPublic());
        dto.setLikeCount(processedImage.getLikeCount());
        dto.setViewCount(processedImage.getViewCount());
        if (processedImage.getCreateTime() != null) {
            dto.setCreateTime(DateFormatUtil.format(processedImage.getCreateTime()));
        }
        return dto;
    }
}
