package com.jj.core.domain.dto.points;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 积分流水DTO
 * 
 * <AUTHOR>
 * @date 2024-07-29
 */
@ApiModel(description = "积分流水信息")
public class PointsTransactionDTO {
    
    /**
     * 流水ID
     */
    @ApiModelProperty(value = "流水ID", example = "1")
    private Long id;
    
    /**
     * 交易类型 1:获得 2:消费 3:锁定 4:解锁 5:回退
     */
    @ApiModelProperty(value = "交易类型 1:获得 2:消费 3:锁定 4:解锁 5:回退", example = "1")
    private Integer transactionType;
    
    /**
     * 交易类型描述
     */
    @ApiModelProperty(value = "交易类型描述", example = "获得积分")
    private String transactionTypeDesc;
    
    /**
     * 积分数量
     */
    @ApiModelProperty(value = "积分数量", example = "10")
    private Integer pointsAmount;
    
    /**
     * 变更前积分
     */
    @ApiModelProperty(value = "变更前积分", example = "90")
    private Integer beforePoints;
    
    /**
     * 变更后积分
     */
    @ApiModelProperty(value = "变更后积分", example = "100")
    private Integer afterPoints;
    
    /**
     * 业务类型
     */
    @ApiModelProperty(value = "业务类型", example = "DAILY_CHECKIN")
    private String businessType;
    
    /**
     * 业务类型描述
     */
    @ApiModelProperty(value = "业务类型描述", example = "每日签到")
    private String businessTypeDesc;
    
    /**
     * 描述
     */
    @ApiModelProperty(value = "描述", example = "每日签到奖励")
    private String description;
    
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间", example = "2024-07-29 10:30:00")
    private String createTime;
    
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public Integer getTransactionType() {
        return transactionType;
    }
    
    public void setTransactionType(Integer transactionType) {
        this.transactionType = transactionType;
    }
    
    public String getTransactionTypeDesc() {
        return transactionTypeDesc;
    }
    
    public void setTransactionTypeDesc(String transactionTypeDesc) {
        this.transactionTypeDesc = transactionTypeDesc;
    }
    
    public Integer getPointsAmount() {
        return pointsAmount;
    }
    
    public void setPointsAmount(Integer pointsAmount) {
        this.pointsAmount = pointsAmount;
    }
    
    public Integer getBeforePoints() {
        return beforePoints;
    }
    
    public void setBeforePoints(Integer beforePoints) {
        this.beforePoints = beforePoints;
    }
    
    public Integer getAfterPoints() {
        return afterPoints;
    }
    
    public void setAfterPoints(Integer afterPoints) {
        this.afterPoints = afterPoints;
    }
    
    public String getBusinessType() {
        return businessType;
    }
    
    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }
    
    public String getBusinessTypeDesc() {
        return businessTypeDesc;
    }
    
    public void setBusinessTypeDesc(String businessTypeDesc) {
        this.businessTypeDesc = businessTypeDesc;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public String getCreateTime() {
        return createTime;
    }
    
    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }
    
    @Override
    public String toString() {
        return "PointsTransactionDTO{" +
                "id=" + id +
                ", transactionType=" + transactionType +
                ", transactionTypeDesc='" + transactionTypeDesc + '\'' +
                ", pointsAmount=" + pointsAmount +
                ", beforePoints=" + beforePoints +
                ", afterPoints=" + afterPoints +
                ", businessType='" + businessType + '\'' +
                ", businessTypeDesc='" + businessTypeDesc + '\'' +
                ", description='" + description + '\'' +
                ", createTime='" + createTime + '\'' +
                '}';
    }
}
