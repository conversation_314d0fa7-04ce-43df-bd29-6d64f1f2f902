package com.jj.core.service.video;

import com.jj.core.domain.dto.portal.web.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * Web端视频生成服务接口
 *
 * <AUTHOR>
 * @date 2025-01-05
 */
public interface WebVideoService {

    /**
     * 创建视频生成任务
     *
     * @param reqDTO 视频生成请求
     * @return 任务创建响应
     */
    WebVideoGenerateResDTO generateVideo(WebVideoGenerateReqDTO reqDTO);

    /**
     * 查询视频生成任务状态
     *
     * @param taskId 任务ID
     * @return 任务状态
     */
    WebVideoTaskStatusResDTO queryTaskStatus(String taskId);

    /**
     * 获取用户视频任务列表
     *
     * @param reqDTO 查询请求
     * @return 任务列表
     */
    WebVideoTaskListResDTO getTaskList(WebVideoTaskListReqDTO reqDTO);

    /**
     * 删除视频任务（仅限已完成或失败的任务）
     *
     * @param taskId 任务ID
     */
    void deleteTask(String taskId);

    /**
     * 上传图片用于视频生成
     *
     * @param files 图片文件
     * @return 上传结果
     */
    WebImageUploadResDTO uploadImages(List<MultipartFile> files);
}