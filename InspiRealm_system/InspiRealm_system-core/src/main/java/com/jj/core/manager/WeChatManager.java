package com.jj.core.manager;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.jj.common.exception.BizException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

/**
 * 微信API管理器
 * 负责与微信API的交互
 * 
 * <AUTHOR>
 * @date 2024-07-29
 */
@Component
public class WeChatManager {
    
    private static final Logger logger = LoggerFactory.getLogger(WeChatManager.class);
    
    @Value("${wechat.app-id:}")
    private String appId;
    
    @Value("${wechat.app-secret:}")
    private String appSecret;
    
    private final RestTemplate restTemplate = new RestTemplate();
    private final ObjectMapper objectMapper = new ObjectMapper();
    
    /**
     * 微信登录凭证校验URL
     */
    private static final String JSCODE2SESSION_URL = "https://api.weixin.qq.com/sns/jscode2session?appid={appid}&secret={secret}&js_code={js_code}&grant_type=authorization_code";
    
    /**
     * 通过code获取微信用户的openid和session_key
     * 
     * @param code 微信授权码
     * @return 微信会话信息
     */
    public WeChatSessionInfo getSessionInfo(String code) {
        try {
            logger.info("开始获取微信会话信息，code: {}", code);
            
            // 调用微信API
            String response = restTemplate.getForObject(JSCODE2SESSION_URL, String.class, appId, appSecret, code);
            logger.info("微信API响应: {}", response);
            
            // 解析响应
            JsonNode jsonNode = objectMapper.readTree(response);
            
            // 检查是否有错误
            if (jsonNode.has("errcode")) {
                int errcode = jsonNode.get("errcode").asInt();
                String errmsg = jsonNode.get("errmsg").asText();
                logger.error("微信API调用失败，errcode: {}, errmsg: {}", errcode, errmsg);
                throw new BizException("微信登录失败：" + errmsg);
            }
            
            // 构建会话信息
            WeChatSessionInfo sessionInfo = new WeChatSessionInfo();
            sessionInfo.setOpenId(jsonNode.get("openid").asText());
            sessionInfo.setSessionKey(jsonNode.get("session_key").asText());
            
            if (jsonNode.has("unionid")) {
                sessionInfo.setUnionId(jsonNode.get("unionid").asText());
            }
            
            logger.info("获取微信会话信息成功，openId: {}", sessionInfo.getOpenId());
            return sessionInfo;
            
        } catch (Exception e) {
            logger.error("获取微信会话信息失败", e);
            throw new BizException("微信登录失败，请重试");
        }
    }
    
    /**
     * 微信会话信息
     */
    public static class WeChatSessionInfo {
        
        /**
         * 用户唯一标识
         */
        private String openId;
        
        /**
         * 会话密钥
         */
        private String sessionKey;
        
        /**
         * 用户在开放平台的唯一标识符
         */
        private String unionId;
        
        public String getOpenId() {
            return openId;
        }
        
        public void setOpenId(String openId) {
            this.openId = openId;
        }
        
        public String getSessionKey() {
            return sessionKey;
        }
        
        public void setSessionKey(String sessionKey) {
            this.sessionKey = sessionKey;
        }
        
        public String getUnionId() {
            return unionId;
        }
        
        public void setUnionId(String unionId) {
            this.unionId = unionId;
        }
        
        @Override
        public String toString() {
            return "WeChatSessionInfo{" +
                    "openId='" + openId + '\'' +
                    ", sessionKey='" + sessionKey + '\'' +
                    ", unionId='" + unionId + '\'' +
                    '}';
        }
    }
}
