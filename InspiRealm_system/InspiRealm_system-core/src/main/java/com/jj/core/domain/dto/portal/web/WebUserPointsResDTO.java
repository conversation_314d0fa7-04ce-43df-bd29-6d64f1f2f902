package com.jj.core.domain.dto.portal.web;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Web端用户积分响应DTO
 *
 * <AUTHOR>
 * @date 2025-01-05
 */
@ApiModel(description = "Web端用户积分响应")
public class WebUserPointsResDTO {

    @ApiModelProperty(value = "用户ID", example = "1001")
    private Long userId;

    @ApiModelProperty(value = "总积分", example = "1000")
    private Integer totalPoints;

    @ApiModelProperty(value = "可用积分", example = "850")
    private Integer availablePoints;

    @ApiModelProperty(value = "锁定积分", example = "50")
    private Integer lockedPoints;

    @ApiModelProperty(value = "已消费积分", example = "100")
    private Integer consumedPoints;

    // Getters and Setters
    public Long getUserId() { return userId; }
    public void setUserId(Long userId) { this.userId = userId; }
    public Integer getTotalPoints() { return totalPoints; }
    public void setTotalPoints(Integer totalPoints) { this.totalPoints = totalPoints; }
    public Integer getAvailablePoints() { return availablePoints; }
    public void setAvailablePoints(Integer availablePoints) { this.availablePoints = availablePoints; }
    public Integer getLockedPoints() { return lockedPoints; }
    public void setLockedPoints(Integer lockedPoints) { this.lockedPoints = lockedPoints; }
    public Integer getConsumedPoints() { return consumedPoints; }
    public void setConsumedPoints(Integer consumedPoints) { this.consumedPoints = consumedPoints; }
}