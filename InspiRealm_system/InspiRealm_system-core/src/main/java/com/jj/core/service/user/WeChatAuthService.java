package com.jj.core.service.user;

import com.jj.common.exception.BizException;
import com.jj.core.auth.TokenManager;
import com.jj.core.domain.dto.wechat.WeChatLoginReqDTO;
import com.jj.core.domain.dto.wechat.WeChatLoginResDTO;
import com.jj.core.domain.dto.wechat.WeChatUserInfoDTO;
import com.jj.core.manager.WeChatManager;
import com.jj.dao.mapper.UserInfoMapper;
import com.jj.dao.mapper.UserPointsMapper;
import com.jj.dao.model.points.UserPoints;
import com.jj.dao.model.user.UserInfo;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

/**
 * 微信认证服务
 * 
 * <AUTHOR>
 * @date 2024-07-29
 */
@Service
public class WeChatAuthService {
    
    private static final Logger logger = LoggerFactory.getLogger(WeChatAuthService.class);
    
    @Autowired
    private WeChatManager weChatManager;
    
    @Autowired
    private TokenManager tokenManager;
    
    @Autowired
    private UserInfoMapper userInfoMapper;
    
    @Autowired
    private UserPointsMapper userPointsMapper;
    
    /**
     * 新用户注册奖励积分
     */
    @Value("${points.register-reward:100}")
    private Integer registerRewardPoints;
    
    /**
     * 微信登录
     * 
     * @param reqDTO 登录请求
     * @return 登录响应
     */
    @Transactional(rollbackFor = Exception.class)
    public WeChatLoginResDTO login(WeChatLoginReqDTO reqDTO) {
        try {
            logger.info("开始处理微信登录，code: {}", reqDTO.getCode());
            
            // 1. 通过code获取微信会话信息
            WeChatManager.WeChatSessionInfo sessionInfo = weChatManager.getSessionInfo(reqDTO.getCode());
            String openId = sessionInfo.getOpenId();
            String unionId = sessionInfo.getUnionId();
            
            // 2. 查询用户是否已存在
            UserInfo existUser = userInfoMapper.selectByOpenId(openId);
            boolean isNewUser = (existUser == null);
            
            UserInfo userInfo;
            if (isNewUser) {
                // 3. 新用户注册
                userInfo = createNewUser(openId, unionId, reqDTO.getUserInfo());
                logger.info("新用户注册成功，userId: {}, openId: {}", userInfo.getId(), openId);
            } else {
                // 4. 老用户更新信息
                userInfo = updateExistingUser(existUser, reqDTO.getUserInfo());
                logger.info("老用户登录成功，userId: {}, openId: {}", userInfo.getId(), openId);
            }
            
            // 5. 生成Token
            String token = tokenManager.generateWeChatToken(userInfo.getId(), openId);
            
            // 6. 获取用户积分信息
            UserPoints userPoints = userPointsMapper.selectByUserId(userInfo.getId());
            
            // 7. 构建响应
            WeChatLoginResDTO resDTO = new WeChatLoginResDTO();
            resDTO.setToken(token);
            resDTO.setIsNewUser(isNewUser);
            
            WeChatLoginResDTO.UserInfoDTO userInfoDTO = new WeChatLoginResDTO.UserInfoDTO();
            userInfoDTO.setId(userInfo.getId());
            userInfoDTO.setNickname(userInfo.getNickname());
            userInfoDTO.setAvatarUrl(userInfo.getAvatarUrl());
            userInfoDTO.setAvailablePoints(userPoints != null ? userPoints.getAvailablePoints() : 0);
            resDTO.setUserInfo(userInfoDTO);
            
            logger.info("微信登录处理完成，userId: {}, isNewUser: {}", userInfo.getId(), isNewUser);
            return resDTO;
            
        } catch (BizException e) {
            logger.error("微信登录失败：{}", e.getMessage());
            throw e;
        } catch (Exception e) {
            logger.error("微信登录处理异常", e);
            throw new BizException("登录失败，请重试");
        }
    }
    
    /**
     * 创建新用户
     * 
     * @param openId 微信OpenID
     * @param unionId 微信UnionID
     * @param userInfoDTO 用户信息
     * @return 用户信息
     */
    private UserInfo createNewUser(String openId, String unionId, WeChatUserInfoDTO userInfoDTO) {
        // 创建用户基础信息
        UserInfo userInfo = new UserInfo();
        userInfo.setOpenId(openId);
        userInfo.setUnionId(unionId);
        userInfo.setStatus(1); // 正常状态
        userInfo.setLastLoginTime(new Date());
        
        // 设置用户信息
        if (userInfoDTO != null) {
            userInfo.setNickname(userInfoDTO.getNickName());
            userInfo.setAvatarUrl(userInfoDTO.getAvatarUrl());
            userInfo.setGender(userInfoDTO.getGender());
            userInfo.setCountry(userInfoDTO.getCountry());
            userInfo.setProvince(userInfoDTO.getProvince());
            userInfo.setCity(userInfoDTO.getCity());
        }
        
        // 插入用户信息
        int insertResult = userInfoMapper.insert(userInfo);
        if (insertResult <= 0) {
            throw new BizException("用户注册失败");
        }
        
        // 初始化用户积分
        int pointsResult = userPointsMapper.insertUserPoints(userInfo.getId(), registerRewardPoints);
        if (pointsResult <= 0) {
            throw new BizException("用户积分初始化失败");
        }
        
        return userInfo;
    }
    
    /**
     * 更新已存在用户信息
     * 
     * @param existUser 已存在的用户
     * @param userInfoDTO 用户信息
     * @return 用户信息
     */
    private UserInfo updateExistingUser(UserInfo existUser, WeChatUserInfoDTO userInfoDTO) {
        boolean needUpdate = false;
        
        // 更新用户信息
        if (userInfoDTO != null) {
            if (StringUtils.isNotBlank(userInfoDTO.getNickName()) && !"微信用户".equals(userInfoDTO.getNickName()) && !userInfoDTO.getNickName().equals(existUser.getNickname())) {
                existUser.setNickname(userInfoDTO.getNickName());
                needUpdate = true;
            }
            if (StringUtils.isNotBlank(userInfoDTO.getAvatarUrl())) {
                existUser.setAvatarUrl(userInfoDTO.getAvatarUrl());
                needUpdate = true;
            }
            if (userInfoDTO.getGender() != null && !userInfoDTO.getGender().equals(existUser.getGender())) {
                existUser.setGender(userInfoDTO.getGender());
                needUpdate = true;
            }
            if (userInfoDTO.getCountry() != null && !userInfoDTO.getCountry().equals(existUser.getCountry())) {
                existUser.setCountry(userInfoDTO.getCountry());
                needUpdate = true;
            }
            if (userInfoDTO.getProvince() != null && !userInfoDTO.getProvince().equals(existUser.getProvince())) {
                existUser.setProvince(userInfoDTO.getProvince());
                needUpdate = true;
            }
            if (userInfoDTO.getCity() != null && !userInfoDTO.getCity().equals(existUser.getCity())) {
                existUser.setCity(userInfoDTO.getCity());
                needUpdate = true;
            }
        }
        
        // 更新最后登录时间
        userInfoMapper.updateLastLoginTime(existUser.getId());
        
        // 如果有信息变更，更新用户信息
        if (needUpdate) {
            userInfoMapper.updateById(existUser);
        }
        
        return existUser;
    }
}
