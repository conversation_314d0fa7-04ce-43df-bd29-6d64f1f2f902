package com.jj.core.domain.dto.portal.web;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * Web端积分历史响应DTO
 *
 * <AUTHOR>
 * @date 2025-01-05
 */
@ApiModel(description = "Web端积分历史响应")
public class WebPointsHistoryResDTO {

    @ApiModelProperty(value = "总记录数", example = "100")
    private Long total;

    @ApiModelProperty(value = "当前页码", example = "1")
    private Integer page;

    @ApiModelProperty(value = "每页大小", example = "10")
    private Integer pageSize;

    @ApiModelProperty(value = "积分记录列表")
    private List<PointsRecord> records;

    // Getters and Setters
    public Long getTotal() { return total; }
    public void setTotal(Long total) { this.total = total; }
    public Integer getPage() { return page; }
    public void setPage(Integer page) { this.page = page; }
    public Integer getPageSize() { return pageSize; }
    public void setPageSize(Integer pageSize) { this.pageSize = pageSize; }
    public List<PointsRecord> getRecords() { return records; }
    public void setRecords(List<PointsRecord> records) { this.records = records; }

    @ApiModel(description = "积分记录")
    public static class PointsRecord {
        @ApiModelProperty(value = "记录ID", example = "1")
        private Long id;
        @ApiModelProperty(value = "操作类型", example = "1")
        private Integer type;
        @ApiModelProperty(value = "积分变化", example = "10")
        private Integer points;
        @ApiModelProperty(value = "操作描述", example = "视频生成消费")
        private String description;
        @ApiModelProperty(value = "创建时间", example = "2025-01-05 10:30:00")
        private String createTime;

        // Getters and Setters
        public Long getId() { return id; }
        public void setId(Long id) { this.id = id; }
        public Integer getType() { return type; }
        public void setType(Integer type) { this.type = type; }
        public Integer getPoints() { return points; }
        public void setPoints(Integer points) { this.points = points; }
        public String getDescription() { return description; }
        public void setDescription(String description) { this.description = description; }
        public String getCreateTime() { return createTime; }
        public void setCreateTime(String createTime) { this.createTime = createTime; }
    }
}