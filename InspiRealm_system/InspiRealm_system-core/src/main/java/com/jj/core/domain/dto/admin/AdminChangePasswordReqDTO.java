package com.jj.core.domain.dto.admin;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * 管理员修改密码请求DTO
 * 
 * <AUTHOR>
 * @date 2024-07-29
 */
@Data
@ApiModel(description = "管理员修改密码请求")
public class AdminChangePasswordReqDTO {
    
    /**
     * 原密码
     */
    @ApiModelProperty(value = "原密码", required = true, example = "123456")
    @NotBlank(message = "原密码不能为空")
    private String oldPassword;
    
    /**
     * 新密码
     */
    @ApiModelProperty(value = "新密码", required = true, example = "newpassword123")
    @NotBlank(message = "新密码不能为空")
    @Size(min = 6, max = 20, message = "新密码长度必须在6-20位之间")
    private String newPassword;
}
