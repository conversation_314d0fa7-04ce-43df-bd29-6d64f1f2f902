package com.jj.core.utils;

import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * Date格式化工具类
 * 
 * <AUTHOR>
 * @date 2024-07-29
 */
public class DateFormatUtil {
    
    private static final String DEFAULT_PATTERN = "yyyy-MM-dd HH:mm:ss";
    private static final String DATE_PATTERN = "yyyy-MM-dd";
    private static final String TIME_PATTERN = "HH:mm:ss";
    
    /**
     * 格式化Date为字符串（默认格式）
     * 
     * @param date 日期
     * @return 格式化后的字符串
     */
    public static String format(Date date) {
        if (date == null) {
            return "";
        }
        return new SimpleDateFormat(DEFAULT_PATTERN).format(date);
    }
    
    /**
     * 格式化Date为字符串（指定格式）
     * 
     * @param date 日期
     * @param pattern 格式模式
     * @return 格式化后的字符串
     */
    public static String format(Date date, String pattern) {
        if (date == null) {
            return "";
        }
        return new SimpleDateFormat(pattern).format(date);
    }
    
    /**
     * 格式化Date为日期字符串（yyyy-MM-dd）
     * 
     * @param date 日期
     * @return 格式化后的字符串
     */
    public static String formatDate(Date date) {
        return format(date, DATE_PATTERN);
    }
    
    /**
     * 格式化Date为时间字符串（HH:mm:ss）
     * 
     * @param date 日期
     * @return 格式化后的字符串
     */
    public static String formatTime(Date date) {
        return format(date, TIME_PATTERN);
    }
}
