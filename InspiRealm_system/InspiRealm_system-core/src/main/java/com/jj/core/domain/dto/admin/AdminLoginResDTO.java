package com.jj.core.domain.dto.admin;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 管理员登录响应DTO
 * 
 * <AUTHOR>
 * @date 2024-07-29
 */
@ApiModel(description = "管理员登录响应")
public class AdminLoginResDTO {
    
    /**
     * 访问Token
     */
    @ApiModelProperty(value = "访问Token", example = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...")
    private String token;
    
    /**
     * 管理员ID
     */
    @ApiModelProperty(value = "管理员ID", example = "1")
    private Long adminId;
    
    /**
     * 用户名
     */
    @ApiModelProperty(value = "用户名", example = "admin")
    private String username;
    
    /**
     * 真实姓名
     */
    @ApiModelProperty(value = "真实姓名", example = "系统管理员")
    private String realName;
    
    /**
     * 角色 1:超级管理员 2:普通管理员
     */
    @ApiModelProperty(value = "角色", example = "1")
    private Integer role;
    
    /**
     * 角色描述
     */
    @ApiModelProperty(value = "角色描述", example = "超级管理员")
    private String roleDesc;
    
    /**
     * Token过期时间（秒）
     */
    @ApiModelProperty(value = "Token过期时间（秒）", example = "7200")
    private Long expiresIn;
    
    public String getToken() {
        return token;
    }
    
    public void setToken(String token) {
        this.token = token;
    }
    
    public Long getAdminId() {
        return adminId;
    }
    
    public void setAdminId(Long adminId) {
        this.adminId = adminId;
    }
    
    public String getUsername() {
        return username;
    }
    
    public void setUsername(String username) {
        this.username = username;
    }
    
    public String getRealName() {
        return realName;
    }
    
    public void setRealName(String realName) {
        this.realName = realName;
    }
    
    public Integer getRole() {
        return role;
    }
    
    public void setRole(Integer role) {
        this.role = role;
    }
    
    public String getRoleDesc() {
        return roleDesc;
    }
    
    public void setRoleDesc(String roleDesc) {
        this.roleDesc = roleDesc;
    }
    
    public Long getExpiresIn() {
        return expiresIn;
    }
    
    public void setExpiresIn(Long expiresIn) {
        this.expiresIn = expiresIn;
    }
    
    @Override
    public String toString() {
        return "AdminLoginResDTO{" +
                "token='" + (token != null ? "***" : null) + '\'' +
                ", adminId=" + adminId +
                ", username='" + username + '\'' +
                ", realName='" + realName + '\'' +
                ", role=" + role +
                ", roleDesc='" + roleDesc + '\'' +
                ", expiresIn=" + expiresIn +
                '}';
    }
}
