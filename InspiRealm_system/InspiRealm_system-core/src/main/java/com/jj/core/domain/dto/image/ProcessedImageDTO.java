package com.jj.core.domain.dto.image;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 处理后图片DTO
 * 
 * <AUTHOR>
 * @date 2024-07-29
 */
@ApiModel(description = "处理后图片")
public class ProcessedImageDTO {
    
    /**
     * 图片ID
     */
    @ApiModelProperty(value = "图片ID", example = "1")
    private Long id;
    
    /**
     * 任务ID
     */
    @ApiModelProperty(value = "任务ID", example = "1")
    private Long taskId;
    
    /**
     * 图片URL
     */
    @ApiModelProperty(value = "图片URL", example = "https://minio.example.com/bucket/image.jpg")
    private String imageUrl;
    
    /**
     * 缩略图URL
     */
    @ApiModelProperty(value = "缩略图URL", example = "https://minio.example.com/bucket/thumbnail.jpg")
    private String thumbnailUrl;
    
    /**
     * 原图URL
     */
    @ApiModelProperty(value = "原图URL", example = "https://minio.example.com/bucket/original.jpg")
    private String originalImageUrl;
    
    /**
     * 文件名
     */
    @ApiModelProperty(value = "文件名", example = "processed_image_20240729.jpg")
    private String fileName;
    
    /**
     * 文件大小(字节)
     */
    @ApiModelProperty(value = "文件大小(字节)", example = "1024000")
    private Long fileSize;
    
    /**
     * 图片宽度
     */
    @ApiModelProperty(value = "图片宽度", example = "1024")
    private Integer imageWidth;
    
    /**
     * 图片高度
     */
    @ApiModelProperty(value = "图片高度", example = "768")
    private Integer imageHeight;
    
    /**
     * 图片格式
     */
    @ApiModelProperty(value = "图片格式", example = "jpg")
    private String imageFormat;
    
    /**
     * 图片比例
     */
    @ApiModelProperty(value = "图片比例", example = "4:3")
    private String aspectRatio;
    
    /**
     * 处理类型
     */
    @ApiModelProperty(value = "处理类型", example = "beautify")
    private String processType;
    
    /**
     * 是否公开 0:私有 1:公开
     */
    @ApiModelProperty(value = "是否公开 0:私有 1:公开", example = "0")
    private Integer isPublic;
    
    /**
     * 点赞数
     */
    @ApiModelProperty(value = "点赞数", example = "10")
    private Integer likeCount;
    
    /**
     * 查看数
     */
    @ApiModelProperty(value = "查看数", example = "100")
    private Integer viewCount;
    
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间", example = "2024-07-29 10:30:00")
    private String createTime;
    
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public Long getTaskId() {
        return taskId;
    }
    
    public void setTaskId(Long taskId) {
        this.taskId = taskId;
    }
    
    public String getImageUrl() {
        return imageUrl;
    }
    
    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }
    
    public String getThumbnailUrl() {
        return thumbnailUrl;
    }
    
    public void setThumbnailUrl(String thumbnailUrl) {
        this.thumbnailUrl = thumbnailUrl;
    }
    
    public String getOriginalImageUrl() {
        return originalImageUrl;
    }
    
    public void setOriginalImageUrl(String originalImageUrl) {
        this.originalImageUrl = originalImageUrl;
    }
    
    public String getFileName() {
        return fileName;
    }
    
    public void setFileName(String fileName) {
        this.fileName = fileName;
    }
    
    public Long getFileSize() {
        return fileSize;
    }
    
    public void setFileSize(Long fileSize) {
        this.fileSize = fileSize;
    }
    
    public Integer getImageWidth() {
        return imageWidth;
    }
    
    public void setImageWidth(Integer imageWidth) {
        this.imageWidth = imageWidth;
    }
    
    public Integer getImageHeight() {
        return imageHeight;
    }
    
    public void setImageHeight(Integer imageHeight) {
        this.imageHeight = imageHeight;
    }
    
    public String getImageFormat() {
        return imageFormat;
    }
    
    public void setImageFormat(String imageFormat) {
        this.imageFormat = imageFormat;
    }
    
    public String getAspectRatio() {
        return aspectRatio;
    }
    
    public void setAspectRatio(String aspectRatio) {
        this.aspectRatio = aspectRatio;
    }
    
    public String getProcessType() {
        return processType;
    }
    
    public void setProcessType(String processType) {
        this.processType = processType;
    }
    
    public Integer getIsPublic() {
        return isPublic;
    }
    
    public void setIsPublic(Integer isPublic) {
        this.isPublic = isPublic;
    }
    
    public Integer getLikeCount() {
        return likeCount;
    }
    
    public void setLikeCount(Integer likeCount) {
        this.likeCount = likeCount;
    }
    
    public Integer getViewCount() {
        return viewCount;
    }
    
    public void setViewCount(Integer viewCount) {
        this.viewCount = viewCount;
    }
    
    public String getCreateTime() {
        return createTime;
    }
    
    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }
    
    @Override
    public String toString() {
        return "ProcessedImageDTO{" +
                "id=" + id +
                ", taskId=" + taskId +
                ", imageUrl='" + imageUrl + '\'' +
                ", thumbnailUrl='" + thumbnailUrl + '\'' +
                ", originalImageUrl='" + originalImageUrl + '\'' +
                ", fileName='" + fileName + '\'' +
                ", fileSize=" + fileSize +
                ", imageWidth=" + imageWidth +
                ", imageHeight=" + imageHeight +
                ", imageFormat='" + imageFormat + '\'' +
                ", aspectRatio='" + aspectRatio + '\'' +
                ", processType='" + processType + '\'' +
                ", isPublic=" + isPublic +
                ", likeCount=" + likeCount +
                ", viewCount=" + viewCount +
                ", createTime='" + createTime + '\'' +
                '}';
    }
}
