package com.jj.core.service.startup;

import com.jj.core.domain.enums.TaskStatusEnum;
import com.jj.core.service.points.PointsService;
import com.jj.dao.mapper.ImageProcessTaskMapper;
import com.jj.dao.model.image.ImageProcessTask;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 启动时任务清理服务
 * 项目启动时检查并清理处理中的任务，回退积分
 * 
 * <AUTHOR>
 * @date 2024-07-31
 */
@Service
public class StartupTaskCleanupService implements ApplicationRunner {

    private static final Logger logger = LoggerFactory.getLogger(StartupTaskCleanupService.class);

    @Autowired
    private ImageProcessTaskMapper imageProcessTaskMapper;

    @Autowired
    private PointsService pointsService;

    /**
     * 项目启动后执行
     */
    @Override
    public void run(ApplicationArguments args) throws Exception {
        logger.info("=== 开始启动时任务清理 ===");
        
        try {
            cleanupProcessingTasks();
            logger.info("=== 启动时任务清理完成 ===");
        } catch (Exception e) {
            logger.error("启动时任务清理失败", e);
            // 不抛出异常，避免影响项目启动
        }
    }

    /**
     * 清理处理中的任务
     */
    @Transactional(rollbackFor = Exception.class)
    public void cleanupProcessingTasks() {
        logger.info("开始清理处理中的任务");

        // 查询所有处理中的任务（状态为 1:待处理 或 2:处理中）
        List<ImageProcessTask> processingTasks = imageProcessTaskMapper.selectProcessingTasks();
        
        if (processingTasks == null || processingTasks.isEmpty()) {
            logger.info("没有发现处理中的任务");
            return;
        }

        logger.info("发现 {} 个处理中的任务，开始清理", processingTasks.size());

        int successCount = 0;
        int failCount = 0;

        for (ImageProcessTask task : processingTasks) {
            try {
                cleanupSingleTask(task);
                successCount++;
                logger.info("任务清理成功，taskId: {}, taskNo: {}, userId: {}", 
                    task.getId(), task.getTaskNo(), task.getUserId());
            } catch (Exception e) {
                failCount++;
                logger.error("任务清理失败，taskId: {}, taskNo: {}, userId: {}", 
                    task.getId(), task.getTaskNo(), task.getUserId(), e);
            }
        }

        logger.info("任务清理完成，成功: {}, 失败: {}", successCount, failCount);
    }

    /**
     * 清理单个任务
     */
    private void cleanupSingleTask(ImageProcessTask task) {
        logger.info("开始清理任务，taskId: {}, taskNo: {}, status: {}, userId: {}", 
            task.getId(), task.getTaskNo(), task.getStatus(), task.getUserId());

        try {
            // 1. 回退积分
            if (task.getPointsCost() != null && task.getPointsCost() > 0) {
                pointsService.refundPoints(task.getUserId(), task.getPointsCost(), task.getTaskNo());
                logger.info("积分回退成功，userId: {}, points: {}, taskNo: {}", 
                    task.getUserId(), task.getPointsCost(), task.getTaskNo());
            }

            // 2. 更新任务状态为失败
            String errorMessage = "系统重启时自动清理，任务被标记为失败";
            imageProcessTaskMapper.updateTaskStatus(
                task.getId(), 
                TaskStatusEnum.FAILED.getCode(), 
                0, 
                errorMessage
            );

            // 3. 更新任务结束时间
            imageProcessTaskMapper.updateTaskEndTime(task.getId());

            logger.info("任务状态更新成功，taskId: {}, 新状态: {}", task.getId(), TaskStatusEnum.FAILED.getCode());

        } catch (Exception e) {
            logger.error("清理任务时发生异常，taskId: {}, taskNo: {}", task.getId(), task.getTaskNo(), e);
            throw e;
        }
    }
}
