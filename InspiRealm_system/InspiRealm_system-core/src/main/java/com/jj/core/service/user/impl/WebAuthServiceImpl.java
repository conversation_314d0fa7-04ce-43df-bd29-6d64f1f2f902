package com.jj.core.service.user.impl;

import com.jj.core.domain.dto.portal.web.*;
import com.jj.core.service.user.WebAuthService;
import com.jj.core.service.email.EmailService;
import com.jj.dao.mapper.WebUserMapper;
import com.jj.dao.mapper.UserSessionMapper;
import com.jj.dao.mapper.EmailVerificationMapper;
import com.jj.dao.mapper.WebUserPointsMapper;
import com.jj.dao.model.user.WebUser;
import com.jj.dao.model.user.UserSession;
import com.jj.dao.model.user.EmailVerification;
import com.jj.dao.model.points.WebUserPoints;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.Random;
import java.util.UUID;

/**
 * Web端用户认证服务实现类
 *
 * <AUTHOR>
 * @date 2025-01-05
 */
@Service
@Transactional
public class WebAuthServiceImpl implements WebAuthService {

    private static final Logger logger = LoggerFactory.getLogger(WebAuthServiceImpl.class);

    @Autowired
    private WebUserMapper webUserMapper;

    @Autowired
    private UserSessionMapper userSessionMapper;

    @Autowired
    private EmailVerificationMapper emailVerificationMapper;

    @Autowired
    private WebUserPointsMapper webUserPointsMapper;

    @Autowired
    private EmailService emailService;

    @Override
    public WebRegisterResDTO register(WebRegisterReqDTO reqDTO) {
        logger.info("开始处理用户注册，邮箱: {}, 用户名: {}", reqDTO.getEmail(), reqDTO.getUsername());

        try {
            // 1. 验证密码一致性
            if (!reqDTO.getPassword().equals(reqDTO.getConfirmPassword())) {
                throw new RuntimeException("两次输入的密码不一致");
            }

            // 2. 验证邮箱是否已存在
            if (webUserMapper.existsByEmail(reqDTO.getEmail())) {
                throw new RuntimeException("该邮箱已被注册");
            }

            // 3. 验证用户名是否已存在
            if (webUserMapper.existsByUsername(reqDTO.getUsername())) {
                throw new RuntimeException("该用户名已被使用");
            }

            // 4. 验证邮箱验证码
            if (!verifyEmailCode(reqDTO.getEmail(), reqDTO.getVerificationCode(), 1)) {
                throw new RuntimeException("验证码错误或已过期");
            }

            // 5. 创建用户
            WebUser webUser = new WebUser();
            webUser.setUsername(reqDTO.getUsername());
            webUser.setEmail(reqDTO.getEmail());
            webUser.setPasswordHash(hashPassword(reqDTO.getPassword()));
            webUser.setNickname(reqDTO.getUsername()); // 默认昵称为用户名
            webUser.setEmailVerified(1); // 已验证
            webUser.setUserType(1); // 普通用户
            webUser.setStatus(1); // 正常状态
            webUser.setLoginCount(0);
            webUser.setVersion(1);

            int insertResult = webUserMapper.insert(webUser);
            if (insertResult <= 0) {
                throw new RuntimeException("用户创建失败");
            }

            // 6. 初始化用户积分
            WebUserPoints userPoints = new WebUserPoints();
            userPoints.setUserId(webUser.getId());
            userPoints.setTotalPoints(100); // 注册赠送100积分
            userPoints.setAvailablePoints(100);
            userPoints.setLockedPoints(0);
            userPoints.setConsumedPoints(0);
            userPoints.setVersion(1);

            int pointsResult = webUserPointsMapper.insert(userPoints);
            if (pointsResult <= 0) {
                throw new RuntimeException("用户积分初始化失败");
            }

            // 7. 标记验证码为已使用
            EmailVerification verification = emailVerificationMapper.selectByEmailAndCodeAndType(
                reqDTO.getEmail(), reqDTO.getVerificationCode(), 1);
            if (verification != null) {
                emailVerificationMapper.markAsUsed(verification.getId());
            }

            // 构造响应
            WebRegisterResDTO resDTO = new WebRegisterResDTO();
            resDTO.setUserId(webUser.getId());
            resDTO.setUsername(webUser.getUsername());
            resDTO.setEmail(webUser.getEmail());
            resDTO.setCreateTime(formatDate(webUser.getCreateTime()));

            logger.info("用户注册成功，用户ID: {}", resDTO.getUserId());
            return resDTO;

        } catch (Exception e) {
            logger.error("用户注册失败", e);
            throw new RuntimeException("注册失败: " + e.getMessage());
        }
    }

    @Override
    public WebLoginResDTO login(WebLoginReqDTO reqDTO) {
        logger.info("开始处理用户登录，邮箱: {}", reqDTO.getEmail());

        try {
            // 1. 查询用户信息
            WebUser webUser = webUserMapper.selectByEmail(reqDTO.getEmail());
            if (webUser == null) {
                throw new RuntimeException("用户不存在");
            }

            // 2. 验证密码
            if (!verifyPassword(reqDTO.getPassword(), webUser.getPasswordHash())) {
                throw new RuntimeException("密码错误");
            }

            // 3. 检查用户状态
            if (webUser.getStatus() != 1) {
                throw new RuntimeException("账户已被禁用或待验证");
            }

            // 4. 生成会话信息
            String sessionId = UUID.randomUUID().toString();
            String accessToken = generateAccessToken(webUser.getId(), sessionId);
            String refreshToken = generateRefreshToken(webUser.getId(), sessionId);

            // 5. 创建用户会话
            UserSession session = new UserSession();
            session.setUserId(webUser.getId());
            session.setSessionId(sessionId);
            session.setAccessToken(accessToken);
            session.setRefreshToken(refreshToken);
            session.setDeviceType("web");
            session.setDeviceInfo("Web Browser");
            session.setIpAddress("127.0.0.1");
            session.setUserAgent("Mozilla/5.0");
            session.setLoginTime(new Date());
            session.setLastActiveTime(new Date());

            // 设置过期时间
            long expiresInSeconds = reqDTO.getRememberMe() ? 30 * 24 * 3600L : 2 * 3600L;
            Date expiresAt = new Date(System.currentTimeMillis() + expiresInSeconds * 1000);
            session.setExpiresAt(expiresAt);
            session.setStatus(1);

            userSessionMapper.insert(session);
            webUserMapper.updateLastLoginInfo(webUser.getId(), "127.0.0.1");

            // 构造响应
            WebLoginResDTO resDTO = new WebLoginResDTO();
            WebUserInfoDTO userInfo = new WebUserInfoDTO();
            userInfo.setId(webUser.getId());
            userInfo.setUsername(webUser.getUsername());
            userInfo.setEmail(webUser.getEmail());
            userInfo.setNickname(webUser.getNickname());
            userInfo.setUserType(webUser.getUserType());
            userInfo.setEmailVerified(webUser.getEmailVerified() == 1);

            resDTO.setUserInfo(userInfo);
            resDTO.setAccessToken(accessToken);
            resDTO.setRefreshToken(refreshToken);
            resDTO.setExpiresIn(expiresInSeconds);

            logger.info("用户登录成功，用户ID: {}", userInfo.getId());
            return resDTO;

        } catch (Exception e) {
            logger.error("用户登录失败", e);
            throw new RuntimeException("登录失败: " + e.getMessage());
        }
    }

    @Override
    public WebRefreshTokenResDTO refreshToken(WebRefreshTokenReqDTO reqDTO) {
        logger.info("开始处理令牌刷新");

        try {
            UserSession session = userSessionMapper.selectByRefreshToken(reqDTO.getRefreshToken());
            if (session == null || session.getStatus() != 1) {
                throw new RuntimeException("刷新令牌无效");
            }

            if (session.getExpiresAt().before(new Date())) {
                throw new RuntimeException("刷新令牌已过期");
            }

            String newAccessToken = generateAccessToken(session.getUserId(), session.getSessionId());
            session.setAccessToken(newAccessToken);
            session.setLastActiveTime(new Date());
            userSessionMapper.updateById(session);

            WebRefreshTokenResDTO resDTO = new WebRefreshTokenResDTO();
            resDTO.setAccessToken(newAccessToken);
            resDTO.setExpiresIn(2 * 3600L);

            logger.info("令牌刷新成功");
            return resDTO;

        } catch (Exception e) {
            logger.error("令牌刷新失败", e);
            throw new RuntimeException("令牌刷新失败: " + e.getMessage());
        }
    }

    @Override
    public void logout() {
        logger.info("用户登出成功");
    }

    @Override
    public void sendVerificationCode(WebSendVerificationCodeReqDTO reqDTO) {
        logger.info("开始发送验证码，邮箱: {}, 类型: {}", reqDTO.getEmail(), reqDTO.getType());

        try {
            int recentCount = emailVerificationMapper.countRecentVerifications(
                reqDTO.getEmail(), reqDTO.getType(), 5);
            if (recentCount > 0) {
                throw new RuntimeException("验证码发送过于频繁，请5分钟后再试");
            }

            String code = generateVerificationCode();

            EmailVerification verification = new EmailVerification();
            verification.setEmail(reqDTO.getEmail());
            verification.setVerificationCode(code);
            verification.setVerificationToken(UUID.randomUUID().toString());
            verification.setVerificationType(reqDTO.getType());
            verification.setStatus(0);
            verification.setAttempts(0);
            verification.setExpiresAt(new Date(System.currentTimeMillis() + 10 * 60 * 1000));

            emailVerificationMapper.insert(verification);

            // 发送验证码邮件
            emailService.sendVerificationCode(reqDTO.getEmail(), code, reqDTO.getType());

            logger.info("验证码已生成并发送到邮箱: {}", reqDTO.getEmail());

        } catch (Exception e) {
            logger.error("验证码发送失败", e);
            throw new RuntimeException("验证码发送失败: " + e.getMessage());
        }
    }

    @Override
    public void resetPassword(WebResetPasswordReqDTO reqDTO) {
        logger.info("开始处理密码重置，邮箱: {}", reqDTO.getEmail());

        try {
            if (!verifyEmailCode(reqDTO.getEmail(), reqDTO.getVerificationCode(), 2)) {
                throw new RuntimeException("验证码错误或已过期");
            }

            WebUser webUser = webUserMapper.selectByEmail(reqDTO.getEmail());
            if (webUser == null) {
                throw new RuntimeException("用户不存在");
            }

            webUser.setPasswordHash(hashPassword(reqDTO.getNewPassword()));
            webUserMapper.updateById(webUser);
            userSessionMapper.deleteByUserId(webUser.getId());

            EmailVerification verification = emailVerificationMapper.selectByEmailAndCodeAndType(
                reqDTO.getEmail(), reqDTO.getVerificationCode(), 2);
            if (verification != null) {
                emailVerificationMapper.markAsUsed(verification.getId());
            }

            logger.info("密码重置成功");

        } catch (Exception e) {
            logger.error("密码重置失败", e);
            throw new RuntimeException("密码重置失败: " + e.getMessage());
        }
    }

    /**
     * 验证邮箱验证码
     */
    private boolean verifyEmailCode(String email, String code, Integer type) {
        try {
            EmailVerification verification = emailVerificationMapper.selectByEmailAndCodeAndType(email, code, type);
            if (verification == null) {
                return false;
            }

            if (verification.getExpiresAt().before(new Date())) {
                return false;
            }

            if (verification.getStatus() == 1) {
                return false;
            }

            if (verification.getAttempts() >= 3) {
                return false;
            }

            return true;

        } catch (Exception e) {
            logger.error("验证码验证失败", e);
            return false;
        }
    }

    /**
     * 生成6位数字验证码
     */
    private String generateVerificationCode() {
        Random random = new Random();
        return String.format("%06d", random.nextInt(1000000));
    }

    /**
     * 密码哈希
     */
    private String hashPassword(String password) {
        try {
            MessageDigest md = MessageDigest.getInstance("SHA-256");
            byte[] hash = md.digest(password.getBytes(StandardCharsets.UTF_8));
            StringBuilder hexString = new StringBuilder();
            for (byte b : hash) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            return hexString.toString();
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("密码加密失败", e);
        }
    }

    /**
     * 验证密码
     */
    private boolean verifyPassword(String password, String hashedPassword) {
        return hashPassword(password).equals(hashedPassword);
    }

    /**
     * 生成访问令牌
     */
    private String generateAccessToken(Long userId, String sessionId) {
        return "access_" + userId + "_" + sessionId + "_" + System.currentTimeMillis();
    }

    /**
     * 生成刷新令牌
     */
    private String generateRefreshToken(Long userId, String sessionId) {
        return "refresh_" + userId + "_" + sessionId + "_" + System.currentTimeMillis();
    }

    /**
     * 格式化日期
     */
    private String formatDate(Date date) {
        if (date == null) {
            return null;
        }
        LocalDateTime localDateTime = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
        return localDateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    }
}