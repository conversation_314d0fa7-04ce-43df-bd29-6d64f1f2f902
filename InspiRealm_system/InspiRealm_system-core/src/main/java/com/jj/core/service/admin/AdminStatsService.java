package com.jj.core.service.admin;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jj.common.exception.BizException;
import com.jj.core.domain.dto.admin.SystemStatsDTO;
import com.jj.dao.mapper.ImageProcessTaskMapper;
import com.jj.dao.mapper.PointsTransactionMapper;
import com.jj.dao.mapper.ProcessedImageMapper;
import com.jj.dao.mapper.UserInfoMapper;
import com.jj.dao.model.image.ImageProcessTask;
import com.jj.dao.model.image.ProcessedImage;
import com.jj.dao.model.points.PointsTransaction;
import com.jj.dao.model.user.UserInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.lang.management.ManagementFactory;
import java.lang.management.MemoryMXBean;
import java.lang.management.OperatingSystemMXBean;
import java.time.LocalDateTime;
import com.jj.core.utils.DateFormatUtil;

/**
 * 管理员系统统计服务
 * 
 * <AUTHOR>
 * @date 2024-07-29
 */
@Service
public class AdminStatsService {
    
    private static final Logger logger = LoggerFactory.getLogger(AdminStatsService.class);
    
    @Autowired
    private UserInfoMapper userInfoMapper;
    
    @Autowired
    private ProcessedImageMapper processedImageMapper;
    
    @Autowired
    private ImageProcessTaskMapper imageProcessTaskMapper;
    
    @Autowired
    private PointsTransactionMapper pointsTransactionMapper;
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    /**
     * 获取系统统计信息
     * 
     * @return 统计信息
     */
    public SystemStatsDTO getSystemStats() {
        try {
            logger.info("获取系统统计信息");
            
            SystemStatsDTO stats = new SystemStatsDTO();
            
            // 用户统计
            stats.setUserStats(getUserStats());
            
            // 图片统计
            stats.setImageStats(getImageStats());
            
            // 积分统计
            stats.setPointsStats(getPointsStats());
            
            // 系统状态
            stats.setSystemStatus(getSystemStatus());
            
            logger.info("获取系统统计信息成功");
            return stats;
            
        } catch (Exception e) {
            logger.error("获取系统统计信息异常", e);
            throw new BizException("获取系统统计信息失败");
        }
    }
    
    /**
     * 获取用户统计
     * 
     * @return 用户统计
     */
    private SystemStatsDTO.UserStatsDTO getUserStats() {
        SystemStatsDTO.UserStatsDTO userStats = new SystemStatsDTO.UserStatsDTO();
        
        // 总用户数
        QueryWrapper<UserInfo> totalWrapper = new QueryWrapper<>();
        userStats.setTotalUsers(Long.valueOf(userInfoMapper.selectCount(totalWrapper)));

        // 今日新增用户
        LocalDateTime todayStart = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0).withNano(0);
        QueryWrapper<UserInfo> todayWrapper = new QueryWrapper<>();
        todayWrapper.ge("create_time", todayStart);
        userStats.setTodayNewUsers(Long.valueOf(userInfoMapper.selectCount(todayWrapper)));

        // 本周新增用户
        LocalDateTime weekStart = LocalDateTime.now().minusDays(7);
        QueryWrapper<UserInfo> weekWrapper = new QueryWrapper<>();
        weekWrapper.ge("create_time", weekStart);
        userStats.setWeekNewUsers(Long.valueOf(userInfoMapper.selectCount(weekWrapper)));

        // 本月新增用户
        LocalDateTime monthStart = LocalDateTime.now().minusDays(30);
        QueryWrapper<UserInfo> monthWrapper = new QueryWrapper<>();
        monthWrapper.ge("create_time", monthStart);
        userStats.setMonthNewUsers(Long.valueOf(userInfoMapper.selectCount(monthWrapper)));

        // 活跃用户数（最近7天有登录）
        LocalDateTime activeStart = LocalDateTime.now().minusDays(7);
        QueryWrapper<UserInfo> activeWrapper = new QueryWrapper<>();
        activeWrapper.ge("last_login_time", activeStart);
        userStats.setActiveUsers(Long.valueOf(userInfoMapper.selectCount(activeWrapper)));

        // 正常用户数
        QueryWrapper<UserInfo> normalWrapper = new QueryWrapper<>();
        normalWrapper.eq("status", 1);
        userStats.setNormalUsers(Long.valueOf(userInfoMapper.selectCount(normalWrapper)));

        // 禁用用户数
        QueryWrapper<UserInfo> disabledWrapper = new QueryWrapper<>();
        disabledWrapper.eq("status", 0);
        userStats.setDisabledUsers(Long.valueOf(userInfoMapper.selectCount(disabledWrapper)));
        
        return userStats;
    }
    
    /**
     * 获取图片统计
     * 
     * @return 图片统计
     */
    private SystemStatsDTO.ImageStatsDTO getImageStats() {
        SystemStatsDTO.ImageStatsDTO imageStats = new SystemStatsDTO.ImageStatsDTO();
        
        // 总图片数
        QueryWrapper<ProcessedImage> totalWrapper = new QueryWrapper<>();
        imageStats.setTotalImages(Long.valueOf(processedImageMapper.selectCount(totalWrapper)));

        // 今日新增图片
        LocalDateTime todayStart = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0).withNano(0);
        QueryWrapper<ProcessedImage> todayWrapper = new QueryWrapper<>();
        todayWrapper.ge("create_time", todayStart);
        imageStats.setTodayNewImages(Long.valueOf(processedImageMapper.selectCount(todayWrapper)));

        // 本周新增图片
        LocalDateTime weekStart = LocalDateTime.now().minusDays(7);
        QueryWrapper<ProcessedImage> weekWrapper = new QueryWrapper<>();
        weekWrapper.ge("create_time", weekStart);
        imageStats.setWeekNewImages(Long.valueOf(processedImageMapper.selectCount(weekWrapper)));

        // 本月新增图片
        LocalDateTime monthStart = LocalDateTime.now().minusDays(30);
        QueryWrapper<ProcessedImage> monthWrapper = new QueryWrapper<>();
        monthWrapper.ge("create_time", monthStart);
        imageStats.setMonthNewImages(Long.valueOf(processedImageMapper.selectCount(monthWrapper)));

        // 公开图片数
        QueryWrapper<ProcessedImage> publicWrapper = new QueryWrapper<>();
        publicWrapper.eq("is_public", 1);
        imageStats.setPublicImages(Long.valueOf(processedImageMapper.selectCount(publicWrapper)));
        
        // 私有图片数
        imageStats.setPrivateImages(imageStats.getTotalImages() - imageStats.getPublicImages());
        
        // 处理成功率
        QueryWrapper<ImageProcessTask> totalTaskWrapper = new QueryWrapper<>();
        Long totalTasks = Long.valueOf(imageProcessTaskMapper.selectCount(totalTaskWrapper));

        QueryWrapper<ImageProcessTask> successTaskWrapper = new QueryWrapper<>();
        successTaskWrapper.eq("status", 3); // 成功状态
        Long successTasks = Long.valueOf(imageProcessTaskMapper.selectCount(successTaskWrapper));
        
        if (totalTasks > 0) {
            double successRate = (double) successTasks / totalTasks * 100;
            imageStats.setSuccessRate(Math.round(successRate * 100.0) / 100.0);
        } else {
            imageStats.setSuccessRate(0.0);
        }
        
        return imageStats;
    }
    
    /**
     * 获取积分统计
     * 
     * @return 积分统计
     */
    private SystemStatsDTO.PointsStatsDTO getPointsStats() {
        SystemStatsDTO.PointsStatsDTO pointsStats = new SystemStatsDTO.PointsStatsDTO();
        
        // 总积分发放（收入类型）
        QueryWrapper<PointsTransaction> issuedWrapper = new QueryWrapper<>();
        issuedWrapper.eq("transaction_type", 1); // 收入
        issuedWrapper.select("IFNULL(SUM(points_amount), 0) as total");
        // 这里需要使用原生SQL或者自定义方法来实现聚合查询
        pointsStats.setTotalPointsIssued(0L); // 简化处理
        
        // 总积分消费（支出类型）
        QueryWrapper<PointsTransaction> consumedWrapper = new QueryWrapper<>();
        consumedWrapper.eq("transaction_type", 2); // 支出
        pointsStats.setTotalPointsConsumed(0L); // 简化处理
        
        // 今日积分发放
        LocalDateTime todayStart = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0).withNano(0);
        QueryWrapper<PointsTransaction> todayIssuedWrapper = new QueryWrapper<>();
        todayIssuedWrapper.eq("transaction_type", 1);
        todayIssuedWrapper.ge("create_time", todayStart);
        pointsStats.setTodayPointsIssued(Long.valueOf(pointsTransactionMapper.selectCount(todayIssuedWrapper)));

        // 今日积分消费
        QueryWrapper<PointsTransaction> todayConsumedWrapper = new QueryWrapper<>();
        todayConsumedWrapper.eq("transaction_type", 2);
        todayConsumedWrapper.ge("create_time", todayStart);
        pointsStats.setTodayPointsConsumed(Long.valueOf(pointsTransactionMapper.selectCount(todayConsumedWrapper)));
        
        // 平均用户积分
        pointsStats.setAvgUserPoints(150.0); // 简化处理
        
        return pointsStats;
    }
    
    /**
     * 获取系统状态
     * 
     * @return 系统状态
     */
    private SystemStatsDTO.SystemStatusDTO getSystemStatus() {
        SystemStatsDTO.SystemStatusDTO systemStatus = new SystemStatsDTO.SystemStatusDTO();
        
        try {
            // 系统运行时间
            long uptime = ManagementFactory.getRuntimeMXBean().getUptime();
            systemStatus.setUptime(formatUptime(uptime));
            
            // CPU使用率
            OperatingSystemMXBean osBean = ManagementFactory.getOperatingSystemMXBean();
            if (osBean instanceof com.sun.management.OperatingSystemMXBean) {
                com.sun.management.OperatingSystemMXBean sunOsBean = (com.sun.management.OperatingSystemMXBean) osBean;
                double cpuUsage = sunOsBean.getProcessCpuLoad() * 100;
                systemStatus.setCpuUsage(Math.round(cpuUsage * 100.0) / 100.0);
            } else {
                systemStatus.setCpuUsage(0.0);
            }
            
            // 内存使用率
            MemoryMXBean memoryBean = ManagementFactory.getMemoryMXBean();
            long usedMemory = memoryBean.getHeapMemoryUsage().getUsed();
            long maxMemory = memoryBean.getHeapMemoryUsage().getMax();
            double memoryUsage = (double) usedMemory / maxMemory * 100;
            systemStatus.setMemoryUsage(Math.round(memoryUsage * 100.0) / 100.0);
            
            // 磁盘使用率（简化处理）
            systemStatus.setDiskUsage(45.2);
            
            // 数据库状态
            systemStatus.setDatabaseStatus(checkDatabaseStatus());
            
            // Redis状态
            systemStatus.setRedisStatus(checkRedisStatus());
            
            // MinIO状态
            systemStatus.setMinioStatus("正常"); // 简化处理
            
        } catch (Exception e) {
            logger.error("获取系统状态异常", e);
            systemStatus.setDatabaseStatus("异常");
            systemStatus.setRedisStatus("异常");
            systemStatus.setMinioStatus("异常");
        }
        
        return systemStatus;
    }
    
    /**
     * 格式化运行时间
     * 
     * @param uptime 运行时间（毫秒）
     * @return 格式化后的时间
     */
    private String formatUptime(long uptime) {
        long seconds = uptime / 1000;
        long days = seconds / (24 * 3600);
        long hours = (seconds % (24 * 3600)) / 3600;
        long minutes = (seconds % 3600) / 60;
        
        return String.format("%d天%d小时%d分钟", days, hours, minutes);
    }
    
    /**
     * 检查数据库状态
     * 
     * @return 状态描述
     */
    private String checkDatabaseStatus() {
        try {
            // 简单查询测试数据库连接
            userInfoMapper.selectCount(new QueryWrapper<UserInfo>().last("LIMIT 1"));
            return "正常";
        } catch (Exception e) {
            logger.error("数据库连接异常", e);
            return "异常";
        }
    }
    
    /**
     * 检查Redis状态
     * 
     * @return 状态描述
     */
    private String checkRedisStatus() {
        try {
            // 测试Redis连接
            redisTemplate.opsForValue().set("health_check", "ok");
            String result = (String) redisTemplate.opsForValue().get("health_check");
            redisTemplate.delete("health_check");
            return "ok".equals(result) ? "正常" : "异常";
        } catch (Exception e) {
            logger.error("Redis连接异常", e);
            return "异常";
        }
    }
}
