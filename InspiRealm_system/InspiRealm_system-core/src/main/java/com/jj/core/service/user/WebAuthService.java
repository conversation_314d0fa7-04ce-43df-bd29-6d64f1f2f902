package com.jj.core.service.user;

import com.jj.core.domain.dto.portal.web.*;

/**
 * Web端用户认证服务接口
 *
 * <AUTHOR>
 * @date 2025-01-05
 */
public interface WebAuthService {

    /**
     * 用户注册
     *
     * @param reqDTO 注册请求
     * @return 注册响应
     */
    WebRegisterResDTO register(WebRegisterReqDTO reqDTO);

    /**
     * 用户登录
     *
     * @param reqDTO 登录请求
     * @return 登录响应
     */
    WebLoginResDTO login(WebLoginReqDTO reqDTO);

    /**
     * 刷新访问令牌
     *
     * @param reqDTO 刷新令牌请求
     * @return 新的访问令牌
     */
    WebRefreshTokenResDTO refreshToken(WebRefreshTokenReqDTO reqDTO);

    /**
     * 用户登出
     */
    void logout();

    /**
     * 发送邮箱验证码
     *
     * @param reqDTO 发送验证码请求
     */
    void sendVerificationCode(WebSendVerificationCodeReqDTO reqDTO);

    /**
     * 重置密码
     *
     * @param reqDTO 重置密码请求
     */
    void resetPassword(WebResetPasswordReqDTO reqDTO);
}