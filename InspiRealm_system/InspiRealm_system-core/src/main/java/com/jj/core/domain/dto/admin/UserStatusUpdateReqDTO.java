package com.jj.core.domain.dto.admin;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * 用户状态更新请求DTO
 * 
 * <AUTHOR>
 * @date 2024-07-29
 */
@ApiModel(description = "用户状态更新请求")
public class UserStatusUpdateReqDTO {
    
    /**
     * 状态值 0:禁用 1:正常
     */
    @ApiModelProperty(value = "状态值 0:禁用 1:正常", required = true, example = "1")
    @NotNull(message = "状态值不能为空")
    @Min(value = 0, message = "状态值必须为0或1")
    @Max(value = 1, message = "状态值必须为0或1")
    private Integer status;
    
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注", example = "违规操作，暂时禁用")
    @Size(max = 200, message = "备注不能超过200个字符")
    private String remark;
    
    public Integer getStatus() {
        return status;
    }
    
    public void setStatus(Integer status) {
        this.status = status;
    }
    
    public String getRemark() {
        return remark;
    }
    
    public void setRemark(String remark) {
        this.remark = remark;
    }
    
    @Override
    public String toString() {
        return "UserStatusUpdateReqDTO{" +
                "status=" + status +
                ", remark='" + remark + '\'' +
                '}';
    }
}
