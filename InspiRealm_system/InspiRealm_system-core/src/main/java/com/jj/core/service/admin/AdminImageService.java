package com.jj.core.service.admin;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jj.common.exception.BizException;
import com.jj.core.domain.dto.admin.ImageManageDTO;
import com.jj.core.manager.MinioManager;
import com.jj.dao.mapper.ProcessedImageMapper;
import com.jj.dao.mapper.UserInfoMapper;
import com.jj.dao.model.image.ProcessedImage;
import com.jj.dao.model.user.UserInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jj.core.utils.DateFormatUtil;

import java.time.format.DateTimeFormatter;

/**
 * 管理员图片管理服务
 * 
 * <AUTHOR>
 * @date 2024-07-29
 */
@Service
public class AdminImageService {
    
    private static final Logger logger = LoggerFactory.getLogger(AdminImageService.class);
    
    @Autowired
    private ProcessedImageMapper processedImageMapper;

    @Autowired
    private UserInfoMapper userInfoMapper;

    @Autowired
    private MinioManager minioManager;
    
    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    
    /**
     * 分页查询所有图片
     * 
     * @param page 页码
     * @param size 每页大小
     * @param keyword 搜索关键词
     * @param isPublic 是否公开
     * @param auditStatus 审核状态
     * @return 图片分页数据
     */
    public IPage<ImageManageDTO> getImageList(Integer page, Integer size, String keyword, Integer isPublic, Integer auditStatus) {
        try {
            logger.info("分页查询图片列表，page: {}, size: {}, keyword: {}, isPublic: {}, auditStatus: {}", 
                    page, size, keyword, isPublic, auditStatus);
            
            Page<ProcessedImage> pageParam = new Page<>(page, size);
            QueryWrapper<ProcessedImage> queryWrapper = new QueryWrapper<>();
            
            // 搜索条件
            if (keyword != null && !keyword.trim().isEmpty()) {
                queryWrapper.and(wrapper -> wrapper
                        .like("file_name", keyword)
                        .or()
                        .like("process_type", keyword)
                );
            }
            
            // 公开状态筛选
            if (isPublic != null) {
                queryWrapper.eq("is_public", isPublic);
            }
            
            // 审核状态筛选
            if (auditStatus != null) {
                queryWrapper.eq("audit_status", auditStatus);
            }
            
            queryWrapper.orderByDesc("create_time");
            
            IPage<ProcessedImage> imagePage = processedImageMapper.selectPage(pageParam, queryWrapper);
            
            // 转换为DTO
            IPage<ImageManageDTO> dtoPage = imagePage.convert(this::convertToImageManageDTO);
            
            logger.info("分页查询图片列表成功，total: {}", dtoPage.getTotal());
            return dtoPage;
            
        } catch (Exception e) {
            logger.error("分页查询图片列表异常", e);
            throw new BizException("查询图片列表失败");
        }
    }
    
    /**
     * 获取图片详情
     * 
     * @param imageId 图片ID
     * @return 图片详情
     */
    public ImageManageDTO getImageDetail(Long imageId) {
        try {
            logger.info("获取图片详情，imageId: {}", imageId);
            
            // 查询图片信息（使用QueryWrapper过滤del字段）
            ProcessedImage image = processedImageMapper.selectOne(
                new QueryWrapper<ProcessedImage>()
                    .eq("id", imageId)
                    .eq("del", 0)
            );
            if (image == null) {
                throw new BizException("图片不存在");
            }
            
            ImageManageDTO imageDTO = convertToImageManageDTO(image);
            
            logger.info("获取图片详情成功，imageId: {}", imageId);
            return imageDTO;
            
        } catch (BizException e) {
            logger.error("获取图片详情失败：{}", e.getMessage());
            throw e;
        } catch (Exception e) {
            logger.error("获取图片详情异常", e);
            throw new BizException("获取图片详情失败");
        }
    }
    
    /**
     * 图片审核
     * 
     * @param imageId 图片ID
     * @param auditStatus 审核状态 1:通过 2:拒绝
     * @param auditRemark 审核备注
     */
    @Transactional(rollbackFor = Exception.class)
    public void auditImage(Long imageId, Integer auditStatus, String auditRemark) {
        try {
            logger.info("图片审核，imageId: {}, auditStatus: {}, auditRemark: {}", imageId, auditStatus, auditRemark);
            
            ProcessedImage image = processedImageMapper.selectById(imageId);
            if (image == null) {
                throw new BizException("图片不存在");
            }
            
            // 更新审核状态
            image.setAuditStatus(auditStatus);
            image.setAuditRemark(auditRemark);

            // 根据审核结果设置公开状态
            if (auditStatus == 1) {
                // 审核通过，保持用户设置的公开状态（通常是1）
                // image.setIsPublic() 保持不变
            } else if (auditStatus == 2) {
                // 审核拒绝，强制设为私有
                image.setIsPublic(0);
            }
            
            int updateResult = processedImageMapper.updateById(image);
            if (updateResult <= 0) {
                throw new BizException("图片审核失败");
            }
            
            String statusDesc = auditStatus == 1 ? "审核通过" : "审核拒绝";
            logger.info("图片审核成功，imageId: {}, status: {}", imageId, statusDesc);
            
        } catch (BizException e) {
            logger.error("图片审核失败：{}", e.getMessage());
            throw e;
        } catch (Exception e) {
            logger.error("图片审核异常", e);
            throw new BizException("图片审核失败");
        }
    }
    
    /**
     * 删除图片
     *
     * @param imageId 图片ID
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteImage(Long imageId) {
        try {
            logger.info("删除图片，imageId: {}", imageId);

            ProcessedImage image = processedImageMapper.selectById(imageId);
            if (image == null) {
                throw new BizException("图片不存在");
            }

            // 1. 先删除MinIO文件
            deleteImageFiles(image);

            // 2. 逻辑删除数据库记录
            int deleteResult = processedImageMapper.deleteById(imageId);
            if (deleteResult <= 0) {
                throw new BizException("删除图片失败");
            }

            logger.info("删除图片成功，imageId: {}", imageId);
            
        } catch (BizException e) {
            logger.error("删除图片失败：{}", e.getMessage());
            throw e;
        } catch (Exception e) {
            logger.error("删除图片异常", e);
            throw new BizException("删除图片失败");
        }
    }

    /**
     * 删除图片相关的MinIO文件
     *
     * @param image 图片信息
     */
    private void deleteImageFiles(ProcessedImage image) {
        if (image == null) {
            return;
        }

        logger.info("开始删除图片文件，imageId: {}", image.getId());

        try {
            // 删除主图片
            deleteMinioFileByUrl(image.getImageUrl(), "主图片");

            // 删除缩略图
            deleteMinioFileByUrl(image.getThumbnailUrl(), "缩略图");

            // 删除原图
            deleteMinioFileByUrl(image.getOriginalImageUrl(), "原图");

            logger.info("图片文件删除完成，imageId: {}", image.getId());

        } catch (Exception e) {
            logger.error("删除图片文件时发生异常，imageId: {}", image.getId(), e);
            // 不抛出异常，避免影响数据库删除操作
        }
    }

    /**
     * 通过URL删除MinIO文件
     *
     * @param url 文件URL
     * @param fileType 文件类型描述
     */
    private void deleteMinioFileByUrl(String url, String fileType) {
        if (url == null || url.trim().isEmpty()) {
            logger.debug("{}URL为空，跳过删除", fileType);
            return;
        }

        try {
            boolean deleted = minioManager.deleteFileByUrl(url);
            if (deleted) {
                logger.debug("{}删除成功，url: {}", fileType, url);
            } else {
                logger.debug("{}删除跳过（非MinIO地址或提取失败），url: {}", fileType, url);
            }
        } catch (Exception e) {
            logger.error("{}删除失败，url: {}", fileType, url, e);
            // 不抛出异常，避免影响主流程
        }
    }

    /**
     * 获取图片统计信息
     *
     * @return 统计信息
     */
    public ImageStatsResult getImageStats() {
        try {
            logger.info("获取图片统计信息");
            
            ImageStatsResult stats = new ImageStatsResult();
            
            // 总图片数
            QueryWrapper<ProcessedImage> totalWrapper = new QueryWrapper<>();
            stats.setTotalImages(Long.valueOf(processedImageMapper.selectCount(totalWrapper)));

            // 公开图片数（只统计审核通过的公开图片）
            QueryWrapper<ProcessedImage> publicWrapper = new QueryWrapper<>();
            publicWrapper.eq("is_public", 1).eq("audit_status", 1);
            stats.setPublicImages(Long.valueOf(processedImageMapper.selectCount(publicWrapper)));

            // 私有图片数（包括审核拒绝的图片）
            QueryWrapper<ProcessedImage> privateWrapper = new QueryWrapper<>();
            privateWrapper.eq("is_public", 0);
            stats.setPrivateImages(Long.valueOf(processedImageMapper.selectCount(privateWrapper)));

            // 待审核图片数（只统计申请公开的待审核图片）
            QueryWrapper<ProcessedImage> pendingWrapper = new QueryWrapper<>();
            pendingWrapper.eq("audit_status", 0).eq("is_public", 1);
            stats.setPendingAuditImages(Long.valueOf(processedImageMapper.selectCount(pendingWrapper)));
            
            logger.info("获取图片统计信息成功，total: {}, public: {}, private: {}, pending: {}", 
                    stats.getTotalImages(), stats.getPublicImages(), stats.getPrivateImages(), stats.getPendingAuditImages());
            return stats;
            
        } catch (Exception e) {
            logger.error("获取图片统计信息异常", e);
            throw new BizException("获取图片统计信息失败");
        }
    }
    
    /**
     * 转换为图片管理DTO
     * 
     * @param image 图片实体
     * @return DTO
     */
    private ImageManageDTO convertToImageManageDTO(ProcessedImage image) {
        ImageManageDTO dto = new ImageManageDTO();
        dto.setId(image.getId());
        dto.setTaskId(image.getTaskId());
        dto.setUserId(image.getUserId());
        dto.setImageUrl(image.getImageUrl());
        dto.setThumbnailUrl(image.getThumbnailUrl());
        dto.setOriginalImageUrl(image.getOriginalImageUrl());
        dto.setFileName(image.getFileName());
        dto.setFileSize(image.getFileSize());
        dto.setImageWidth(image.getImageWidth());
        dto.setImageHeight(image.getImageHeight());
        dto.setImageFormat(image.getImageFormat());
        dto.setAspectRatio(image.getAspectRatio());
        dto.setProcessType(image.getProcessType());
        dto.setIsPublic(image.getIsPublic());
        dto.setPublicStatusDesc(image.getIsPublic() == 1 ? "公开" : "私有");
        dto.setLikeCount(image.getLikeCount());
        dto.setViewCount(image.getViewCount());
        dto.setAuditStatus(image.getAuditStatus() != null ? image.getAuditStatus() : 0);
        dto.setAuditStatusDesc(getAuditStatusDesc(dto.getAuditStatus()));
        dto.setAuditRemark(image.getAuditRemark());
        
        if (image.getCreateTime() != null) {
            dto.setCreateTime(DateFormatUtil.format(image.getCreateTime()));
        }
        
        // 查询用户信息
        UserInfo userInfo = userInfoMapper.selectById(image.getUserId());
        if (userInfo != null) {
            dto.setUserNickname(userInfo.getNickname());
        }
        
        return dto;
    }
    
    /**
     * 获取审核状态描述
     * 
     * @param auditStatus 审核状态
     * @return 状态描述
     */
    private String getAuditStatusDesc(Integer auditStatus) {
        switch (auditStatus) {
            case 0:
                return "待审核";
            case 1:
                return "审核通过";
            case 2:
                return "审核拒绝";
            default:
                return "未知状态";
        }
    }
    
    /**
     * 图片统计结果
     */
    public static class ImageStatsResult {
        private Long totalImages;
        private Long publicImages;
        private Long privateImages;
        private Long pendingAuditImages;
        
        // Getter and Setter methods
        public Long getTotalImages() {
            return totalImages;
        }
        
        public void setTotalImages(Long totalImages) {
            this.totalImages = totalImages;
        }
        
        public Long getPublicImages() {
            return publicImages;
        }
        
        public void setPublicImages(Long publicImages) {
            this.publicImages = publicImages;
        }
        
        public Long getPrivateImages() {
            return privateImages;
        }
        
        public void setPrivateImages(Long privateImages) {
            this.privateImages = privateImages;
        }
        
        public Long getPendingAuditImages() {
            return pendingAuditImages;
        }
        
        public void setPendingAuditImages(Long pendingAuditImages) {
            this.pendingAuditImages = pendingAuditImages;
        }
    }
}
