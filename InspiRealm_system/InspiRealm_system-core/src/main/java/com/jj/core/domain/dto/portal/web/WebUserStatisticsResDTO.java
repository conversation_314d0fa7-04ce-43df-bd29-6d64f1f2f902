package com.jj.core.domain.dto.portal.web;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Web端用户统计响应DTO
 *
 * <AUTHOR>
 * @date 2025-01-05
 */
@ApiModel(description = "Web端用户统计响应")
public class WebUserStatisticsResDTO {

    @ApiModelProperty(value = "总视频生成次数", example = "25")
    private Integer totalVideoGenerated;

    @ApiModelProperty(value = "成功生成次数", example = "23")
    private Integer successfulGenerated;

    @ApiModelProperty(value = "失败生成次数", example = "2")
    private Integer failedGenerated;

    @ApiModelProperty(value = "总积分消费", example = "230")
    private Integer totalPointsConsumed;

    @ApiModelProperty(value = "本月生成次数", example = "8")
    private Integer monthlyGenerated;

    @ApiModelProperty(value = "本周生成次数", example = "3")
    private Integer weeklyGenerated;

    @ApiModelProperty(value = "今日生成次数", example = "1")
    private Integer dailyGenerated;

    @ApiModelProperty(value = "平均生成时长（秒）", example = "125")
    private Integer avgGenerationTime;

    // Getters and Setters
    public Integer getTotalVideoGenerated() { return totalVideoGenerated; }
    public void setTotalVideoGenerated(Integer totalVideoGenerated) { this.totalVideoGenerated = totalVideoGenerated; }
    public Integer getSuccessfulGenerated() { return successfulGenerated; }
    public void setSuccessfulGenerated(Integer successfulGenerated) { this.successfulGenerated = successfulGenerated; }
    public Integer getFailedGenerated() { return failedGenerated; }
    public void setFailedGenerated(Integer failedGenerated) { this.failedGenerated = failedGenerated; }
    public Integer getTotalPointsConsumed() { return totalPointsConsumed; }
    public void setTotalPointsConsumed(Integer totalPointsConsumed) { this.totalPointsConsumed = totalPointsConsumed; }
    public Integer getMonthlyGenerated() { return monthlyGenerated; }
    public void setMonthlyGenerated(Integer monthlyGenerated) { this.monthlyGenerated = monthlyGenerated; }
    public Integer getWeeklyGenerated() { return weeklyGenerated; }
    public void setWeeklyGenerated(Integer weeklyGenerated) { this.weeklyGenerated = weeklyGenerated; }
    public Integer getDailyGenerated() { return dailyGenerated; }
    public void setDailyGenerated(Integer dailyGenerated) { this.dailyGenerated = dailyGenerated; }
    public Integer getAvgGenerationTime() { return avgGenerationTime; }
    public void setAvgGenerationTime(Integer avgGenerationTime) { this.avgGenerationTime = avgGenerationTime; }
}