package com.jj.core.service.square;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jj.common.exception.BizException;
import com.jj.core.domain.dto.square.SquareImageDTO;
import com.jj.core.domain.dto.square.ImageDetailDTO;
import com.jj.dao.mapper.ImageLikeMapper;
import com.jj.dao.mapper.ProcessedImageMapper;
import com.jj.dao.mapper.UserInfoMapper;
import com.jj.dao.mapper.ImageProcessTaskMapper;
import com.jj.dao.model.image.ProcessedImage;
import com.jj.dao.model.image.ImageProcessTask;
import com.jj.dao.model.square.ImageLike;
import com.jj.dao.model.user.UserInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jj.core.utils.DateFormatUtil;

import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 广场服务
 * 
 * <AUTHOR>
 * @date 2024-07-29
 */
@Service
public class SquareService {
    
    private static final Logger logger = LoggerFactory.getLogger(SquareService.class);
    
    @Autowired
    private ProcessedImageMapper processedImageMapper;
    
    @Autowired
    private UserInfoMapper userInfoMapper;

    @Autowired
    private ImageProcessTaskMapper imageProcessTaskMapper;
    
    @Autowired
    private ImageLikeMapper imageLikeMapper;
    
    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    
    /**
     * 分页查询广场图片
     * 
     * @param page 页码
     * @param size 每页大小
     * @param processType 处理类型
     * @param aspectRatio 图片比例
     * @param currentUserId 当前用户ID（用于查询点赞状态）
     * @return 图片分页数据
     */
    public IPage<SquareImageDTO> getSquareImages(Integer page, Integer size, String processType, 
                                               String aspectRatio, Long currentUserId) {
        try {
            logger.info("分页查询广场图片，page: {}, size: {}, processType: {}, aspectRatio: {}, currentUserId: {}", 
                    page, size, processType, aspectRatio, currentUserId);
            
            Page<ProcessedImage> pageParam = new Page<>(page, size);
            IPage<ProcessedImage> imagePage = processedImageMapper.selectPublicImages(pageParam, processType, aspectRatio);
            
            // 转换为DTO
            IPage<SquareImageDTO> dtoPage = imagePage.convert(image -> convertToSquareImageDTO(image, currentUserId));
            
            logger.info("分页查询广场图片成功，total: {}", dtoPage.getTotal());
            return dtoPage;
            
        } catch (Exception e) {
            logger.error("分页查询广场图片异常", e);
            throw new BizException("查询广场图片失败");
        }
    }
    
    /**
     * 点赞或取消点赞图片
     * 
     * @param imageId 图片ID
     * @param userId 用户ID
     * @param isLike 是否点赞
     * @return 点赞后的状态
     */
    @Transactional(rollbackFor = Exception.class)
    public LikeResultDTO likeImage(Long imageId, Long userId, Boolean isLike) {
        try {
            logger.info("点赞操作，imageId: {}, userId: {}, isLike: {}", imageId, userId, isLike);
            
            // 查询图片是否存在且公开
            ProcessedImage image = processedImageMapper.selectById(imageId);
            if (image == null || image.getIsPublic() != 1) {
                throw new BizException("图片不存在或未公开");
            }
            
            // 不能给自己的图片点赞
            if (image.getUserId().equals(userId)) {
                throw new BizException("不能给自己的图片点赞");
            }
            
            // 更新点赞记录
            int likeStatus = isLike ? 1 : 0;
            int updateResult = imageLikeMapper.upsertLikeRecord(imageId, userId, likeStatus);
            if (updateResult <= 0) {
                throw new BizException("点赞操作失败");
            }
            
            // 更新图片点赞数
            if (isLike) {
                processedImageMapper.incrementLikeCount(imageId);
            } else {
                processedImageMapper.decrementLikeCount(imageId);
            }
            
            // 增加图片查看数
            processedImageMapper.incrementViewCount(imageId);
            
            // 查询最新的点赞数
            ProcessedImage updatedImage = processedImageMapper.selectById(imageId);
            
            LikeResultDTO result = new LikeResultDTO();
            result.setImageId(imageId);
            result.setIsLiked(isLike);
            result.setLikeCount(updatedImage.getLikeCount());
            
            logger.info("点赞操作成功，imageId: {}, userId: {}, isLike: {}, likeCount: {}", 
                    imageId, userId, isLike, updatedImage.getLikeCount());
            return result;
            
        } catch (BizException e) {
            logger.error("点赞操作失败：{}", e.getMessage());
            throw e;
        } catch (Exception e) {
            logger.error("点赞操作异常", e);
            throw new BizException("点赞操作失败");
        }
    }
    
    /**
     * 转换为广场图片DTO
     * 
     * @param image 图片实体
     * @param currentUserId 当前用户ID
     * @return DTO
     */
    private SquareImageDTO convertToSquareImageDTO(ProcessedImage image, Long currentUserId) {
        SquareImageDTO dto = new SquareImageDTO();
        dto.setId(image.getId());
        dto.setImageUrl(image.getImageUrl());
        dto.setThumbnailUrl(image.getThumbnailUrl());
        dto.setImageWidth(image.getImageWidth());
        dto.setImageHeight(image.getImageHeight());
        dto.setAspectRatio(image.getAspectRatio());
        dto.setProcessType(image.getProcessType());
        dto.setLikeCount(image.getLikeCount());
        dto.setViewCount(image.getViewCount());
        
        if (image.getCreateTime() != null) {
            dto.setCreateTime(DateFormatUtil.format(image.getCreateTime()));
        }
        
        // 查询用户信息
        UserInfo userInfo = userInfoMapper.selectById(image.getUserId());
        if (userInfo != null) {
            SquareImageDTO.UserInfoDTO userInfoDTO = new SquareImageDTO.UserInfoDTO();
            userInfoDTO.setId(userInfo.getId());
            userInfoDTO.setNickname(userInfo.getNickname());
            userInfoDTO.setAvatarUrl(userInfo.getAvatarUrl());
            dto.setUserInfo(userInfoDTO);
        }
        
        // 查询当前用户是否点赞
        if (currentUserId != null) {
            ImageLike imageLike = imageLikeMapper.selectByImageIdAndUserId(image.getId(), currentUserId);
            dto.setIsLiked(imageLike != null && imageLike.getLikeStatus() == 1);
        } else {
            dto.setIsLiked(false);
        }
        
        return dto;
    }
    
    /**
     * 点赞结果DTO
     */
    public static class LikeResultDTO {
        
        private Long imageId;
        private Boolean isLiked;
        private Integer likeCount;
        
        public Long getImageId() {
            return imageId;
        }
        
        public void setImageId(Long imageId) {
            this.imageId = imageId;
        }
        
        public Boolean getIsLiked() {
            return isLiked;
        }
        
        public void setIsLiked(Boolean isLiked) {
            this.isLiked = isLiked;
        }
        
        public Integer getLikeCount() {
            return likeCount;
        }
        
        public void setLikeCount(Integer likeCount) {
            this.likeCount = likeCount;
        }
        
        @Override
        public String toString() {
            return "LikeResultDTO{" +
                    "imageId=" + imageId +
                    ", isLiked=" + isLiked +
                    ", likeCount=" + likeCount +
                    '}';
        }
    }

    /**
     * 获取图片详情
     *
     * @param imageId 图片ID
     * @param currentUserId 当前用户ID
     * @return 图片详情
     */
    public ImageDetailDTO getImageDetail(Long imageId, Long currentUserId) {
        try {
            logger.info("获取图片详情，imageId: {}, currentUserId: {}", imageId, currentUserId);

            // 查询图片信息
            ProcessedImage image = processedImageMapper.selectById(imageId);
            if (image == null) {
                throw new BizException("图片不存在");
            }

            // 检查权限：只有公开且审核通过且未删除的图片才能查看详情
            if (image.getDel() == 1) {
                throw new BizException("图片已删除");
            }

            if (image.getIsPublic() != 1) {
                // 如果是私有图片，只有作者本人可以查看
                if (currentUserId == null || !image.getUserId().equals(currentUserId)) {
                    throw new BizException("图片未公开，无权查看");
                }
            }

            if (image.getAuditStatus() != 1) {
                // 如果审核未通过，只有作者本人可以查看
                if (currentUserId == null || !image.getUserId().equals(currentUserId)) {
                    throw new BizException("图片审核未通过，无权查看");
                }
            }

            // 查询作者信息
            UserInfo author = userInfoMapper.selectById(image.getUserId());

            // 查询任务信息获取prompt
            ImageProcessTask task = null;
            if (image.getTaskId() != null) {
                task = imageProcessTaskMapper.selectById(image.getTaskId());
            }

            // 查询当前用户是否点赞过
            boolean isLiked = false;
            if (currentUserId != null) {
                ImageLike like = imageLikeMapper.selectByImageIdAndUserId(imageId, currentUserId);
                isLiked = like != null && like.getLikeStatus() == 1;
            }

            // 增加查看数
            processedImageMapper.incrementViewCount(imageId);

            // 转换为DTO
            ImageDetailDTO dto = new ImageDetailDTO();
            dto.setId(image.getId());
            dto.setOriginalImageUrl(image.getOriginalImageUrl());
            dto.setImageUrl(image.getImageUrl());
            dto.setThumbnailUrl(image.getThumbnailUrl());
            dto.setImageWidth(image.getImageWidth());
            dto.setImageHeight(image.getImageHeight());
            dto.setAspectRatio(image.getAspectRatio());
            dto.setProcessType(image.getProcessType());

            // 从任务中获取prompt信息
            if (task != null) {
                dto.setPrompt(task.getPrompt());
            }

            dto.setViewCount(image.getViewCount() + 1); // 加上刚才增加的查看数
            dto.setLikeCount(image.getLikeCount());
            dto.setIsLiked(isLiked);
            dto.setIsPublic(image.getIsPublic() == 1);
            dto.setAuditStatus(image.getAuditStatus());
            dto.setAuditRemark(image.getAuditRemark());
            dto.setIsDeleted(image.getDel() == 1);
            dto.setCreateTime(DateFormatUtil.formatDate(image.getCreateTime()));

            // 设置作者信息
            if (author != null) {
                ImageDetailDTO.UserInfoDTO userInfo = new ImageDetailDTO.UserInfoDTO();
                userInfo.setNickname(author.getNickname());
                userInfo.setAvatarUrl(author.getAvatarUrl());
                dto.setUserInfo(userInfo);
            }

            logger.info("获取图片详情成功，imageId: {}", imageId);
            return dto;

        } catch (Exception e) {
            logger.error("获取图片详情失败，imageId: {}", imageId, e);
            if (e instanceof BizException) {
                throw e;
            }
            throw new BizException("获取图片详情失败");
        }
    }

    /**
     * 增加图片浏览量
     *
     * @param imageId 图片ID
     */
    public void incrementViewCount(Long imageId) {
        try {
            logger.info("增加图片浏览量，imageId: {}", imageId);

            // 查询图片是否存在且可访问
            ProcessedImage image = processedImageMapper.selectById(imageId);
            if (image == null) {
                throw new BizException("图片不存在");
            }

            // 只有公开且审核通过且未删除的图片才能增加浏览量
            if (image.getDel() == 1) {
                throw new BizException("图片已删除");
            }

            if (image.getIsPublic() != 1) {
                throw new BizException("图片未公开");
            }

            if (image.getAuditStatus() != 1) {
                throw new BizException("图片审核未通过");
            }

            // 增加浏览量
            processedImageMapper.incrementViewCount(imageId);

            logger.info("增加图片浏览量成功，imageId: {}", imageId);

        } catch (Exception e) {
            logger.error("增加图片浏览量失败，imageId: {}", imageId, e);
            if (e instanceof BizException) {
                throw e;
            }
            throw new BizException("增加图片浏览量失败");
        }
    }
}
