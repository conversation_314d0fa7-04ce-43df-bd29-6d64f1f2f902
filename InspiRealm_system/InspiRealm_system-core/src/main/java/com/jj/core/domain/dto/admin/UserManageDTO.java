package com.jj.core.domain.dto.admin;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 用户管理DTO
 * 
 * <AUTHOR>
 * @date 2024-07-29
 */
@ApiModel(description = "用户管理信息")
public class UserManageDTO {
    
    /**
     * 用户ID
     */
    @ApiModelProperty(value = "用户ID", example = "1")
    private Long id;
    
    /**
     * 微信OpenID
     */
    @ApiModelProperty(value = "微信OpenID", example = "oABC123DEF456GHI789")
    private String openId;
    
    /**
     * 昵称
     */
    @ApiModelProperty(value = "昵称", example = "张三")
    private String nickname;
    
    /**
     * 头像URL
     */
    @ApiModelProperty(value = "头像URL", example = "https://wx.qlogo.cn/mmopen/xxx")
    private String avatarUrl;
    
    /**
     * 性别 0:未知 1:男 2:女
     */
    @ApiModelProperty(value = "性别", example = "1")
    private Integer gender;
    
    /**
     * 手机号
     */
    @ApiModelProperty(value = "手机号", example = "13800138000")
    private String phone;
    
    /**
     * 邮箱
     */
    @ApiModelProperty(value = "邮箱", example = "<EMAIL>")
    private String email;
    
    /**
     * 状态 0:禁用 1:正常
     */
    @ApiModelProperty(value = "状态", example = "1")
    private Integer status;
    
    /**
     * 状态描述
     */
    @ApiModelProperty(value = "状态描述", example = "正常")
    private String statusDesc;
    
    /**
     * 总积分
     */
    @ApiModelProperty(value = "总积分", example = "1000")
    private Integer totalPoints;
    
    /**
     * 可用积分
     */
    @ApiModelProperty(value = "可用积分", example = "800")
    private Integer availablePoints;
    
    /**
     * 锁定积分
     */
    @ApiModelProperty(value = "锁定积分", example = "50")
    private Integer lockedPoints;
    
    /**
     * 已消费积分
     */
    @ApiModelProperty(value = "已消费积分", example = "150")
    private Integer consumedPoints;
    
    /**
     * 注册时间
     */
    @ApiModelProperty(value = "注册时间", example = "2024-07-29 10:30:00")
    private String createTime;
    
    /**
     * 最后登录时间
     */
    @ApiModelProperty(value = "最后登录时间", example = "2024-07-29 15:30:00")
    private String lastLoginTime;
    
    // Getter and Setter methods
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getOpenId() {
        return openId;
    }
    
    public void setOpenId(String openId) {
        this.openId = openId;
    }
    
    public String getNickname() {
        return nickname;
    }
    
    public void setNickname(String nickname) {
        this.nickname = nickname;
    }
    
    public String getAvatarUrl() {
        return avatarUrl;
    }
    
    public void setAvatarUrl(String avatarUrl) {
        this.avatarUrl = avatarUrl;
    }
    
    public Integer getGender() {
        return gender;
    }
    
    public void setGender(Integer gender) {
        this.gender = gender;
    }
    
    public String getPhone() {
        return phone;
    }
    
    public void setPhone(String phone) {
        this.phone = phone;
    }
    
    public String getEmail() {
        return email;
    }
    
    public void setEmail(String email) {
        this.email = email;
    }
    
    public Integer getStatus() {
        return status;
    }
    
    public void setStatus(Integer status) {
        this.status = status;
    }
    
    public String getStatusDesc() {
        return statusDesc;
    }
    
    public void setStatusDesc(String statusDesc) {
        this.statusDesc = statusDesc;
    }
    
    public Integer getTotalPoints() {
        return totalPoints;
    }
    
    public void setTotalPoints(Integer totalPoints) {
        this.totalPoints = totalPoints;
    }
    
    public Integer getAvailablePoints() {
        return availablePoints;
    }
    
    public void setAvailablePoints(Integer availablePoints) {
        this.availablePoints = availablePoints;
    }
    
    public Integer getLockedPoints() {
        return lockedPoints;
    }
    
    public void setLockedPoints(Integer lockedPoints) {
        this.lockedPoints = lockedPoints;
    }
    
    public Integer getConsumedPoints() {
        return consumedPoints;
    }
    
    public void setConsumedPoints(Integer consumedPoints) {
        this.consumedPoints = consumedPoints;
    }
    
    public String getCreateTime() {
        return createTime;
    }
    
    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }
    
    public String getLastLoginTime() {
        return lastLoginTime;
    }
    
    public void setLastLoginTime(String lastLoginTime) {
        this.lastLoginTime = lastLoginTime;
    }
    
    @Override
    public String toString() {
        return "UserManageDTO{" +
                "id=" + id +
                ", openId='" + openId + '\'' +
                ", nickname='" + nickname + '\'' +
                ", avatarUrl='" + avatarUrl + '\'' +
                ", gender=" + gender +
                ", phone='" + phone + '\'' +
                ", email='" + email + '\'' +
                ", status=" + status +
                ", statusDesc='" + statusDesc + '\'' +
                ", totalPoints=" + totalPoints +
                ", availablePoints=" + availablePoints +
                ", lockedPoints=" + lockedPoints +
                ", consumedPoints=" + consumedPoints +
                ", createTime='" + createTime + '\'' +
                ", lastLoginTime='" + lastLoginTime + '\'' +
                '}';
    }
}
