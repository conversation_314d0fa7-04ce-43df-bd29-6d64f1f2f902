package com.jj.core.service.user;

import com.jj.core.domain.dto.portal.web.*;

/**
 * Web端用户管理服务接口
 *
 * <AUTHOR>
 * @date 2025-01-05
 */
public interface WebUserService {

    /**
     * 获取用户信息
     *
     * @return 用户信息
     */
    WebUserProfileResDTO getUserProfile();

    /**
     * 更新用户信息
     *
     * @param reqDTO 更新请求
     */
    void updateUserProfile(WebUpdateUserProfileReqDTO reqDTO);

    /**
     * 修改密码
     *
     * @param reqDTO 修改密码请求
     */
    void changePassword(WebChangePasswordReqDTO reqDTO);

    /**
     * 获取用户积分信息
     *
     * @return 积分信息
     */
    WebUserPointsResDTO getUserPoints();

    /**
     * 获取积分消费记录
     *
     * @param reqDTO 查询请求
     * @return 消费记录
     */
    WebPointsHistoryResDTO getPointsHistory(WebPointsHistoryReqDTO reqDTO);

    /**
     * 获取用户统计信息
     *
     * @return 统计信息
     */
    WebUserStatisticsResDTO getUserStatistics();
}