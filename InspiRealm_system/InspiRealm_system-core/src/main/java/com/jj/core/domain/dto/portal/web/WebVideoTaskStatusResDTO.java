package com.jj.core.domain.dto.portal.web;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Web端视频任务状态响应DTO
 *
 * <AUTHOR>
 * @date 2025-01-05
 */
@ApiModel(description = "Web端视频任务状态响应")
public class WebVideoTaskStatusResDTO {

    @ApiModelProperty(value = "任务ID", example = "task_123456789")
    private String taskId;

    @ApiModelProperty(value = "任务编号", example = "VEO3_20250105_001")
    private String taskNo;

    @ApiModelProperty(value = "任务状态", example = "3")
    private Integer status;

    @ApiModelProperty(value = "状态描述", example = "生成成功")
    private String statusDesc;

    @ApiModelProperty(value = "进度百分比", example = "100")
    private Integer progress;

    @ApiModelProperty(value = "生成提示词", example = "一只可爱的小猫在花园里玩耍")
    private String prompt;

    @ApiModelProperty(value = "增强后的提示词", example = "一只毛茸茸的橘色小猫在阳光明媚的花园里快乐地玩耍，周围有五彩斑斓的花朵")
    private String enhancedPrompt;

    @ApiModelProperty(value = "视频URL", example = "https://example.com/video.mp4")
    private String videoUrl;

    @ApiModelProperty(value = "缩略图URL", example = "https://example.com/thumbnail.jpg")
    private String thumbnailUrl;

    @ApiModelProperty(value = "视频时长（秒）", example = "5")
    private Integer duration;

    @ApiModelProperty(value = "视频分辨率", example = "1920x1080")
    private String resolution;

    @ApiModelProperty(value = "文件大小（字节）", example = "10485760")
    private Long fileSize;

    @ApiModelProperty(value = "积分消耗", example = "10")
    private Integer pointsCost;

    @ApiModelProperty(value = "错误信息", example = "")
    private String errorMessage;

    @ApiModelProperty(value = "创建时间", example = "2025-01-05 10:30:00")
    private String createTime;

    @ApiModelProperty(value = "开始时间", example = "2025-01-05 10:30:05")
    private String startTime;

    @ApiModelProperty(value = "完成时间", example = "2025-01-05 10:32:15")
    private String endTime;

    // Getters and Setters
    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public String getTaskNo() {
        return taskNo;
    }

    public void setTaskNo(String taskNo) {
        this.taskNo = taskNo;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getStatusDesc() {
        return statusDesc;
    }

    public void setStatusDesc(String statusDesc) {
        this.statusDesc = statusDesc;
    }

    public Integer getProgress() {
        return progress;
    }

    public void setProgress(Integer progress) {
        this.progress = progress;
    }

    public String getPrompt() {
        return prompt;
    }

    public void setPrompt(String prompt) {
        this.prompt = prompt;
    }

    public String getEnhancedPrompt() {
        return enhancedPrompt;
    }

    public void setEnhancedPrompt(String enhancedPrompt) {
        this.enhancedPrompt = enhancedPrompt;
    }

    public String getVideoUrl() {
        return videoUrl;
    }

    public void setVideoUrl(String videoUrl) {
        this.videoUrl = videoUrl;
    }

    public String getThumbnailUrl() {
        return thumbnailUrl;
    }

    public void setThumbnailUrl(String thumbnailUrl) {
        this.thumbnailUrl = thumbnailUrl;
    }

    public Integer getDuration() {
        return duration;
    }

    public void setDuration(Integer duration) {
        this.duration = duration;
    }

    public String getResolution() {
        return resolution;
    }

    public void setResolution(String resolution) {
        this.resolution = resolution;
    }

    public Long getFileSize() {
        return fileSize;
    }

    public void setFileSize(Long fileSize) {
        this.fileSize = fileSize;
    }

    public Integer getPointsCost() {
        return pointsCost;
    }

    public void setPointsCost(Integer pointsCost) {
        this.pointsCost = pointsCost;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }
}