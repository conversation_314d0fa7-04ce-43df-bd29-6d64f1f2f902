package com.jj.core.domain.dto.wechat;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 微信登录响应DTO
 * 
 * <AUTHOR>
 * @date 2024-07-29
 */
@ApiModel(description = "微信登录响应")
public class WeChatLoginResDTO {
    
    /**
     * 访问令牌
     */
    @ApiModelProperty(value = "访问令牌", example = "abc123def456ghi789")
    private String token;
    
    /**
     * 用户信息
     */
    @ApiModelProperty(value = "用户信息")
    private UserInfoDTO userInfo;
    
    /**
     * 是否新用户
     */
    @ApiModelProperty(value = "是否新用户", example = "true")
    private Boolean isNewUser;
    
    public String getToken() {
        return token;
    }
    
    public void setToken(String token) {
        this.token = token;
    }
    
    public UserInfoDTO getUserInfo() {
        return userInfo;
    }
    
    public void setUserInfo(UserInfoDTO userInfo) {
        this.userInfo = userInfo;
    }
    
    public Boolean getIsNewUser() {
        return isNewUser;
    }
    
    public void setIsNewUser(Boolean isNewUser) {
        this.isNewUser = isNewUser;
    }
    
    @Override
    public String toString() {
        return "WeChatLoginResDTO{" +
                "token='" + token + '\'' +
                ", userInfo=" + userInfo +
                ", isNewUser=" + isNewUser +
                '}';
    }
    
    /**
     * 用户信息DTO
     */
    @ApiModel(description = "用户信息")
    public static class UserInfoDTO {
        
        /**
         * 用户ID
         */
        @ApiModelProperty(value = "用户ID", example = "1")
        private Long id;
        
        /**
         * 用户昵称
         */
        @ApiModelProperty(value = "用户昵称", example = "张三")
        private String nickname;
        
        /**
         * 头像URL
         */
        @ApiModelProperty(value = "头像URL", example = "https://wx.qlogo.cn/mmopen/xxx")
        private String avatarUrl;
        
        /**
         * 可用积分
         */
        @ApiModelProperty(value = "可用积分", example = "100")
        private Integer availablePoints;
        
        public Long getId() {
            return id;
        }
        
        public void setId(Long id) {
            this.id = id;
        }
        
        public String getNickname() {
            return nickname;
        }
        
        public void setNickname(String nickname) {
            this.nickname = nickname;
        }
        
        public String getAvatarUrl() {
            return avatarUrl;
        }
        
        public void setAvatarUrl(String avatarUrl) {
            this.avatarUrl = avatarUrl;
        }
        
        public Integer getAvailablePoints() {
            return availablePoints;
        }
        
        public void setAvailablePoints(Integer availablePoints) {
            this.availablePoints = availablePoints;
        }
        
        @Override
        public String toString() {
            return "UserInfoDTO{" +
                    "id=" + id +
                    ", nickname='" + nickname + '\'' +
                    ", avatarUrl='" + avatarUrl + '\'' +
                    ", availablePoints=" + availablePoints +
                    '}';
        }
    }
}
