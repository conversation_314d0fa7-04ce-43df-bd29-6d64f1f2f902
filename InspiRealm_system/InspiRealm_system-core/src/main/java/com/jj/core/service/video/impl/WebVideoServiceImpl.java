package com.jj.core.service.video.impl;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.jj.core.domain.dto.portal.web.*;
import com.jj.core.manager.MinioManager;
import com.jj.core.service.video.WebVideoService;
import com.jj.core.service.video.Veo3ApiService;
import com.jj.dao.mapper.VideoGenerationTaskMapper;
import com.jj.dao.mapper.WebUserPointsMapper;
import com.jj.dao.model.video.VideoGenerationTask;
import com.jj.dao.model.points.WebUserPoints;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.UUID;

/**
 * Web端视频生成服务实现类
 *
 * <AUTHOR>
 * @date 2025-01-05
 */
@Service
@Transactional
public class WebVideoServiceImpl implements WebVideoService {

    private static final Logger logger = LoggerFactory.getLogger(WebVideoServiceImpl.class);

    @Autowired
    private VideoGenerationTaskMapper videoGenerationTaskMapper;

    @Autowired
    private WebUserPointsMapper webUserPointsMapper;

    @Autowired
    private Veo3ApiService veo3ApiService;

    @Autowired
    private MinioManager minioManager;

    private final ObjectMapper objectMapper = new ObjectMapper();

    // Veo3视频生成积分消耗
    private static final int BASE_POINTS_COST = 50;  // 基础生成消耗
    private static final int UPSAMPLE_POINTS_COST = 30;  // 高清增强额外消耗

    @Override
    public WebVideoGenerateResDTO generateVideo(WebVideoGenerateReqDTO reqDTO) {
        logger.info("开始处理视频生成请求，提示词: {}", reqDTO.getPrompt());

        try {
            // 获取当前用户ID
            Long currentUserId = getCurrentUserId();
            if (currentUserId == null) {
                throw new RuntimeException("用户未登录");
            }

            // 1. 计算积分消耗
            int totalPointsCost = BASE_POINTS_COST;
            if (reqDTO.getEnableUpsample() != null && reqDTO.getEnableUpsample()) {
                totalPointsCost += UPSAMPLE_POINTS_COST;
            }
            logger.info("积分消耗计算: 基础={}, 增强={}, 总计={}",
                    BASE_POINTS_COST,
                    reqDTO.getEnableUpsample() ? UPSAMPLE_POINTS_COST : 0,
                    totalPointsCost);

            // 2. 检查用户积分是否足够
            WebUserPoints userPoints = webUserPointsMapper.selectByUserId(currentUserId);
            if (userPoints == null || userPoints.getAvailablePoints() < totalPointsCost) {
                throw new RuntimeException("积分不足，需要 " + totalPointsCost + " 积分，当前可用 " +
                        (userPoints != null ? userPoints.getAvailablePoints() : 0) + " 积分");
            }

            // 3. 锁定积分
            int lockResult = webUserPointsMapper.lockPoints(currentUserId, totalPointsCost);
            if (lockResult <= 0) {
                throw new RuntimeException("积分锁定失败");
            }

            // 3. 创建视频生成任务
            VideoGenerationTask task = new VideoGenerationTask();
            task.setTaskNo(generateTaskNo());
            task.setUserId(currentUserId);
            task.setExternalTaskId(null); // 调用Veo3 API后设置
            task.setPrompt(reqDTO.getPrompt());
            task.setEnhancedPrompt(null); // 如果启用增强会设置
            task.setModel("veo3");
            task.setEnhancePrompt(reqDTO.getEnhancePrompt() ? 1 : 0);
            task.setEnableUpsample(reqDTO.getEnableUpsample() ? 1 : 0);
            // 优先使用imageUrls，如果没有则使用inputImages（向后兼容）
            if (reqDTO.getImageUrls() != null && !reqDTO.getImageUrls().isEmpty()) {
                task.setInputImages(convertImageUrlsToJson(reqDTO.getImageUrls()));
            } else {
                task.setInputImages(null); // 不再保存Base64数据
            }
            task.setAspectRatio(reqDTO.getAspectRatio());
            task.setDuration(reqDTO.getDuration());
            task.setStatus(1); // 待处理
            task.setProgress(0);
            task.setPointsCost(totalPointsCost);
            task.setVideoFormat("mp4");
            task.setVersion(1);

            int insertResult = videoGenerationTaskMapper.insert(task);
            if (insertResult <= 0) {
                // 回滚积分锁定
                webUserPointsMapper.unlockPoints(currentUserId, totalPointsCost);
                throw new RuntimeException("任务创建失败");
            }

            // 4. 调用Veo3 API（异步处理）
            String externalTaskId = veo3ApiService.createVideoTask(task);
            task.setExternalTaskId(externalTaskId);
            task.setStatus(2); // 处理中
            task.setStartTime(new Date());
            videoGenerationTaskMapper.updateById(task);

            // 构造响应
            WebVideoGenerateResDTO resDTO = new WebVideoGenerateResDTO();
            resDTO.setTaskId(task.getId().toString());
            resDTO.setTaskNo(task.getTaskNo());
            resDTO.setStatus(task.getStatus());
            resDTO.setStatusDesc(getStatusDesc(task.getStatus()));
            resDTO.setEstimatedTime(120); // 预计2分钟完成
            resDTO.setPointsCost(task.getPointsCost());
            resDTO.setCreateTime(formatDate(task.getCreateTime()));

            logger.info("视频生成任务创建成功，任务ID: {}", task.getId());
            return resDTO;

        } catch (Exception e) {
            logger.error("视频生成任务创建失败", e);
            throw new RuntimeException("视频生成失败: " + e.getMessage());
        }
    }

    @Override
    public WebVideoTaskStatusResDTO queryTaskStatus(String taskId) {
        logger.info("查询任务状态，任务ID: {}", taskId);

        try {
            VideoGenerationTask task = videoGenerationTaskMapper.selectById(Long.valueOf(taskId));
            if (task == null) {
                throw new RuntimeException("任务不存在");
            }

            // TODO: 验证任务是否属于当前用户
            // Long currentUserId = getCurrentUserId();
            // if (!task.getUserId().equals(currentUserId)) {
            //     throw new RuntimeException("无权访问该任务");
            // }

            // 构造响应
            WebVideoTaskStatusResDTO resDTO = new WebVideoTaskStatusResDTO();
            resDTO.setTaskId(task.getId().toString());
            resDTO.setTaskNo(task.getTaskNo());
            resDTO.setStatus(task.getStatus());
            resDTO.setStatusDesc(getStatusDesc(task.getStatus()));
            resDTO.setProgress(task.getProgress());
            resDTO.setPrompt(task.getPrompt());
            resDTO.setEnhancedPrompt(task.getEnhancedPrompt());
            resDTO.setVideoUrl(task.getVideoUrl());
            resDTO.setThumbnailUrl(task.getThumbnailUrl());
            resDTO.setDuration(task.getDuration());
            resDTO.setResolution(getResolution(task.getVideoWidth(), task.getVideoHeight()));
            resDTO.setFileSize(task.getFileSize());
            resDTO.setPointsCost(task.getPointsCost());
            resDTO.setErrorMessage(task.getErrorMessage());
            resDTO.setCreateTime(formatDate(task.getCreateTime()));
            resDTO.setStartTime(formatDate(task.getStartTime()));
            resDTO.setEndTime(formatDate(task.getEndTime()));

            return resDTO;

        } catch (Exception e) {
            logger.error("查询任务状态失败", e);
            throw new RuntimeException("查询失败: " + e.getMessage());
        }
    }

    @Override
    public WebVideoTaskListResDTO getTaskList(WebVideoTaskListReqDTO reqDTO) {
        logger.info("获取任务列表，页码: {}, 页大小: {}", reqDTO.getPage(), reqDTO.getPageSize());

        try {
            // 获取当前用户ID
            Long currentUserId = getCurrentUserId();
            if (currentUserId == null) {
                throw new RuntimeException("用户未登录");
            }

            // 分页查询
            Page<VideoGenerationTask> page = new Page<>(reqDTO.getPage(), reqDTO.getPageSize());
            IPage<VideoGenerationTask> taskPage = videoGenerationTaskMapper.selectUserTasksPage(
                page, currentUserId, reqDTO.getStatus());

            // 构造响应
            WebVideoTaskListResDTO resDTO = new WebVideoTaskListResDTO();
            resDTO.setTotal(taskPage.getTotal());
            resDTO.setPage(reqDTO.getPage());
            resDTO.setPageSize(reqDTO.getPageSize());
            resDTO.setTotalPages((int) Math.ceil((double) taskPage.getTotal() / reqDTO.getPageSize()));

            List<WebVideoTaskStatusResDTO> taskList = new ArrayList<>();
            for (VideoGenerationTask task : taskPage.getRecords()) {
                WebVideoTaskStatusResDTO taskDTO = new WebVideoTaskStatusResDTO();
                taskDTO.setTaskId(task.getId().toString());
                taskDTO.setTaskNo(task.getTaskNo());
                taskDTO.setStatus(task.getStatus());
                taskDTO.setStatusDesc(getStatusDesc(task.getStatus()));
                taskDTO.setProgress(task.getProgress());
                taskDTO.setPrompt(task.getPrompt());
                taskDTO.setVideoUrl(task.getVideoUrl());
                taskDTO.setThumbnailUrl(task.getThumbnailUrl());
                taskDTO.setDuration(task.getDuration());
                taskDTO.setPointsCost(task.getPointsCost());
                taskDTO.setCreateTime(formatDate(task.getCreateTime()));
                taskList.add(taskDTO);
            }
            resDTO.setTasks(taskList);

            return resDTO;

        } catch (Exception e) {
            logger.error("获取任务列表失败", e);
            throw new RuntimeException("获取任务列表失败: " + e.getMessage());
        }
    }

    @Override
    public void deleteTask(String taskId) {
        logger.info("删除任务，任务ID: {}", taskId);

        try {
            VideoGenerationTask task = videoGenerationTaskMapper.selectById(Long.valueOf(taskId));
            if (task == null) {
                throw new RuntimeException("任务不存在");
            }

            // 验证任务是否属于当前用户
            Long currentUserId = getCurrentUserId();
            if (!task.getUserId().equals(currentUserId)) {
                throw new RuntimeException("无权删除该任务");
            }

            // 只能删除已完成或失败的任务
            if (task.getStatus() != 3 && task.getStatus() != 4) {
                throw new RuntimeException("只能删除已完成或失败的任务");
            }

            // 逻辑删除
            int result = videoGenerationTaskMapper.deleteById(Long.valueOf(taskId));
            if (result <= 0) {
                throw new RuntimeException("任务删除失败");
            }

            logger.info("任务删除成功");

        } catch (Exception e) {
            logger.error("删除任务失败", e);
            throw new RuntimeException("删除任务失败: " + e.getMessage());
        }
    }

    @Override
    public WebImageUploadResDTO uploadImages(List<MultipartFile> files) {
        logger.info("上传图片，文件数量: {}", files.size());

        try {
            if (files == null || files.isEmpty()) {
                throw new RuntimeException("请选择要上传的图片");
            }

            if (files.size() > 5) {
                throw new RuntimeException("最多只能上传5张图片");
            }

            List<String> imageUrls = new ArrayList<>();
            for (MultipartFile file : files) {
                // 验证文件类型
                String contentType = file.getContentType();
                if (contentType == null || !contentType.startsWith("image/")) {
                    throw new RuntimeException("只支持图片文件");
                }

                // 验证文件大小（最大10MB）
                if (file.getSize() > 10 * 1024 * 1024) {
                    throw new RuntimeException("图片文件不能超过10MB");
                }

                // 实际上传到MinIO文件服务器
                try {
                    String fileName = generateImageFileName(file.getOriginalFilename());
                    Long userId = getCurrentUserId();
                    String imageUrl = minioManager.uploadFile(file.getInputStream(), fileName, contentType, userId);
                    imageUrls.add(imageUrl);
                    logger.info("图片上传成功: {}", imageUrl);
                } catch (Exception e) {
                    logger.error("图片上传失败: {}", e.getMessage());
                    throw new RuntimeException("图片上传失败: " + e.getMessage());
                }
            }

            // 构造响应
            WebImageUploadResDTO resDTO = new WebImageUploadResDTO();
            resDTO.setImageUrls(imageUrls);
            resDTO.setCount(imageUrls.size());

            logger.info("图片上传成功，数量: {}", imageUrls.size());
            return resDTO;

        } catch (Exception e) {
            logger.error("图片上传失败", e);
            throw new RuntimeException("图片上传失败: " + e.getMessage());
        }
    }

    /**
     * 获取当前用户ID
     * 从请求上下文中获取当前用户ID
     */
    private Long getCurrentUserId() {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes != null) {
            HttpServletRequest request = attributes.getRequest();
            Object userId = request.getAttribute("currentUserId");
            if (userId instanceof Long) {
                return (Long) userId;
            }
        }
        throw new RuntimeException("无法获取当前用户信息");
    }

    /**
     * 生成任务编号
     */
    private String generateTaskNo() {
        LocalDateTime now = LocalDateTime.now();
        String dateStr = now.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        String timeStr = now.format(DateTimeFormatter.ofPattern("HHmmss"));
        return "VEO3_" + dateStr + "_" + timeStr + "_" + (int)(Math.random() * 1000);
    }

    /**
     * 将输入图片URL列表转换为JSON字符串
     */
    private String convertImageUrlsToJson(List<String> imageUrls) {
        if (imageUrls == null || imageUrls.isEmpty()) {
            return null;
        }

        try {
            // 使用Jackson序列化，禁用Unicode转义以保持URL原始格式
            ObjectMapper mapper = new ObjectMapper();
            mapper.getFactory().configure(JsonGenerator.Feature.ESCAPE_NON_ASCII, false);
            return mapper.writeValueAsString(imageUrls);
        } catch (Exception e) {
            logger.error("转换图片URL列表为JSON失败", e);
            return null;
        }
    }

    /**
     * 生成图片文件名
     */
    private String generateImageFileName(String originalFilename) {
        String extension = "";
        if (originalFilename != null && originalFilename.contains(".")) {
            extension = originalFilename.substring(originalFilename.lastIndexOf("."));
        }
        return "video-input/" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy/MM/dd"))
               + "/" + UUID.randomUUID().toString() + extension;
    }



    /**
     * 获取状态描述
     */
    private String getStatusDesc(Integer status) {
        if (status == null) {
            return "未知";
        }
        switch (status) {
            case 1: return "待处理";
            case 2: return "处理中";
            case 3: return "生成成功";
            case 4: return "生成失败";
            default: return "未知状态";
        }
    }

    /**
     * 获取分辨率字符串
     */
    private String getResolution(Integer width, Integer height) {
        if (width == null || height == null) {
            return null;
        }
        return width + "x" + height;
    }

    /**
     * 格式化日期
     */
    private String formatDate(Date date) {
        if (date == null) {
            return null;
        }
        LocalDateTime localDateTime = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
        return localDateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    }
}