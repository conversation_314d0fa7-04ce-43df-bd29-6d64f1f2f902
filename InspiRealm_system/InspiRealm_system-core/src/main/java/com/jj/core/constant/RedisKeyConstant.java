package com.jj.core.constant;

/**
 * <AUTHOR>
 * @Classname RedisKeyConstant
 * @Created by cmx
 */
public interface RedisKeyConstant {

    /**
     * USER_TOKEN 用户对应得所有TOKEN便于切换组织
     */
    String USER_TOKEN = "USER_TOKEN";

    /**
     * TOKEN 用户TOKEN 解析信息使用
     */
    String TOKEN = "TOKEN";

    /**
     * system所有菜单
     */
    String MENU = "MENU_SYSTEM";

    /**
     * 手机登录验证码
     */
    String PHONE_LOGIN_CODE = "PHONE_LOGIN_CODE_%s";

    /**
     * 手机密码验证码
     */
    String PHONE_PASSWORD_CODE = "PHONE_PASSWORD_CODE_%s";

    /**
     * 手机登录验证码时长
     */
    String PHONE_LOGIN_TIME = "PHONE_LOGIN_TIME_%s";

    /**
     * 手机修改验证码时长
     */
    String PHONE_PASSWORD_TIME = "PHONE_PASSWORD_TIME_%s";

    /**
     * 用户观看次数
     */
    String USER_ARTICLE_VIEWS = "USER_ARTICLE_VIEWS_%s";

    /**
     * openID
     */
    String OPEN_ID = "openId";

}
