package com.jj.core.domain.dto.admin;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 系统统计DTO
 * 
 * <AUTHOR>
 * @date 2024-07-29
 */
@ApiModel(description = "系统统计信息")
public class SystemStatsDTO {
    
    /**
     * 用户统计
     */
    @ApiModelProperty(value = "用户统计")
    private UserStatsDTO userStats;
    
    /**
     * 图片统计
     */
    @ApiModelProperty(value = "图片统计")
    private ImageStatsDTO imageStats;
    
    /**
     * 积分统计
     */
    @ApiModelProperty(value = "积分统计")
    private PointsStatsDTO pointsStats;
    
    /**
     * 系统运行状态
     */
    @ApiModelProperty(value = "系统运行状态")
    private SystemStatusDTO systemStatus;
    
    public UserStatsDTO getUserStats() {
        return userStats;
    }
    
    public void setUserStats(UserStatsDTO userStats) {
        this.userStats = userStats;
    }
    
    public ImageStatsDTO getImageStats() {
        return imageStats;
    }
    
    public void setImageStats(ImageStatsDTO imageStats) {
        this.imageStats = imageStats;
    }
    
    public PointsStatsDTO getPointsStats() {
        return pointsStats;
    }
    
    public void setPointsStats(PointsStatsDTO pointsStats) {
        this.pointsStats = pointsStats;
    }
    
    public SystemStatusDTO getSystemStatus() {
        return systemStatus;
    }
    
    public void setSystemStatus(SystemStatusDTO systemStatus) {
        this.systemStatus = systemStatus;
    }
    
    /**
     * 用户统计DTO
     */
    @ApiModel(description = "用户统计")
    public static class UserStatsDTO {
        
        @ApiModelProperty(value = "总用户数", example = "1000")
        private Long totalUsers;
        
        @ApiModelProperty(value = "今日新增用户", example = "10")
        private Long todayNewUsers;
        
        @ApiModelProperty(value = "本周新增用户", example = "50")
        private Long weekNewUsers;
        
        @ApiModelProperty(value = "本月新增用户", example = "200")
        private Long monthNewUsers;
        
        @ApiModelProperty(value = "活跃用户数", example = "800")
        private Long activeUsers;
        
        @ApiModelProperty(value = "正常用户数", example = "950")
        private Long normalUsers;
        
        @ApiModelProperty(value = "禁用用户数", example = "50")
        private Long disabledUsers;
        
        // Getter and Setter methods
        public Long getTotalUsers() {
            return totalUsers;
        }
        
        public void setTotalUsers(Long totalUsers) {
            this.totalUsers = totalUsers;
        }
        
        public Long getTodayNewUsers() {
            return todayNewUsers;
        }
        
        public void setTodayNewUsers(Long todayNewUsers) {
            this.todayNewUsers = todayNewUsers;
        }
        
        public Long getWeekNewUsers() {
            return weekNewUsers;
        }
        
        public void setWeekNewUsers(Long weekNewUsers) {
            this.weekNewUsers = weekNewUsers;
        }
        
        public Long getMonthNewUsers() {
            return monthNewUsers;
        }
        
        public void setMonthNewUsers(Long monthNewUsers) {
            this.monthNewUsers = monthNewUsers;
        }
        
        public Long getActiveUsers() {
            return activeUsers;
        }
        
        public void setActiveUsers(Long activeUsers) {
            this.activeUsers = activeUsers;
        }
        
        public Long getNormalUsers() {
            return normalUsers;
        }
        
        public void setNormalUsers(Long normalUsers) {
            this.normalUsers = normalUsers;
        }
        
        public Long getDisabledUsers() {
            return disabledUsers;
        }
        
        public void setDisabledUsers(Long disabledUsers) {
            this.disabledUsers = disabledUsers;
        }
    }
    
    /**
     * 图片统计DTO
     */
    @ApiModel(description = "图片统计")
    public static class ImageStatsDTO {
        
        @ApiModelProperty(value = "总图片数", example = "5000")
        private Long totalImages;
        
        @ApiModelProperty(value = "今日新增图片", example = "50")
        private Long todayNewImages;
        
        @ApiModelProperty(value = "本周新增图片", example = "300")
        private Long weekNewImages;
        
        @ApiModelProperty(value = "本月新增图片", example = "1200")
        private Long monthNewImages;
        
        @ApiModelProperty(value = "公开图片数", example = "3000")
        private Long publicImages;
        
        @ApiModelProperty(value = "私有图片数", example = "2000")
        private Long privateImages;
        
        @ApiModelProperty(value = "处理成功率", example = "95.5")
        private Double successRate;
        
        // Getter and Setter methods
        public Long getTotalImages() {
            return totalImages;
        }
        
        public void setTotalImages(Long totalImages) {
            this.totalImages = totalImages;
        }
        
        public Long getTodayNewImages() {
            return todayNewImages;
        }
        
        public void setTodayNewImages(Long todayNewImages) {
            this.todayNewImages = todayNewImages;
        }
        
        public Long getWeekNewImages() {
            return weekNewImages;
        }
        
        public void setWeekNewImages(Long weekNewImages) {
            this.weekNewImages = weekNewImages;
        }
        
        public Long getMonthNewImages() {
            return monthNewImages;
        }
        
        public void setMonthNewImages(Long monthNewImages) {
            this.monthNewImages = monthNewImages;
        }
        
        public Long getPublicImages() {
            return publicImages;
        }
        
        public void setPublicImages(Long publicImages) {
            this.publicImages = publicImages;
        }
        
        public Long getPrivateImages() {
            return privateImages;
        }
        
        public void setPrivateImages(Long privateImages) {
            this.privateImages = privateImages;
        }
        
        public Double getSuccessRate() {
            return successRate;
        }
        
        public void setSuccessRate(Double successRate) {
            this.successRate = successRate;
        }
    }
    
    /**
     * 积分统计DTO
     */
    @ApiModel(description = "积分统计")
    public static class PointsStatsDTO {
        
        @ApiModelProperty(value = "总积分发放", example = "100000")
        private Long totalPointsIssued;
        
        @ApiModelProperty(value = "总积分消费", example = "80000")
        private Long totalPointsConsumed;
        
        @ApiModelProperty(value = "今日积分发放", example = "1000")
        private Long todayPointsIssued;
        
        @ApiModelProperty(value = "今日积分消费", example = "800")
        private Long todayPointsConsumed;
        
        @ApiModelProperty(value = "平均用户积分", example = "150")
        private Double avgUserPoints;
        
        // Getter and Setter methods
        public Long getTotalPointsIssued() {
            return totalPointsIssued;
        }
        
        public void setTotalPointsIssued(Long totalPointsIssued) {
            this.totalPointsIssued = totalPointsIssued;
        }
        
        public Long getTotalPointsConsumed() {
            return totalPointsConsumed;
        }
        
        public void setTotalPointsConsumed(Long totalPointsConsumed) {
            this.totalPointsConsumed = totalPointsConsumed;
        }
        
        public Long getTodayPointsIssued() {
            return todayPointsIssued;
        }
        
        public void setTodayPointsIssued(Long todayPointsIssued) {
            this.todayPointsIssued = todayPointsIssued;
        }
        
        public Long getTodayPointsConsumed() {
            return todayPointsConsumed;
        }
        
        public void setTodayPointsConsumed(Long todayPointsConsumed) {
            this.todayPointsConsumed = todayPointsConsumed;
        }
        
        public Double getAvgUserPoints() {
            return avgUserPoints;
        }
        
        public void setAvgUserPoints(Double avgUserPoints) {
            this.avgUserPoints = avgUserPoints;
        }
    }
    
    /**
     * 系统状态DTO
     */
    @ApiModel(description = "系统状态")
    public static class SystemStatusDTO {
        
        @ApiModelProperty(value = "系统运行时间", example = "7天12小时30分钟")
        private String uptime;
        
        @ApiModelProperty(value = "CPU使用率", example = "25.5")
        private Double cpuUsage;
        
        @ApiModelProperty(value = "内存使用率", example = "60.8")
        private Double memoryUsage;
        
        @ApiModelProperty(value = "磁盘使用率", example = "45.2")
        private Double diskUsage;
        
        @ApiModelProperty(value = "数据库状态", example = "正常")
        private String databaseStatus;
        
        @ApiModelProperty(value = "Redis状态", example = "正常")
        private String redisStatus;
        
        @ApiModelProperty(value = "MinIO状态", example = "正常")
        private String minioStatus;
        
        // Getter and Setter methods
        public String getUptime() {
            return uptime;
        }
        
        public void setUptime(String uptime) {
            this.uptime = uptime;
        }
        
        public Double getCpuUsage() {
            return cpuUsage;
        }
        
        public void setCpuUsage(Double cpuUsage) {
            this.cpuUsage = cpuUsage;
        }
        
        public Double getMemoryUsage() {
            return memoryUsage;
        }
        
        public void setMemoryUsage(Double memoryUsage) {
            this.memoryUsage = memoryUsage;
        }
        
        public Double getDiskUsage() {
            return diskUsage;
        }
        
        public void setDiskUsage(Double diskUsage) {
            this.diskUsage = diskUsage;
        }
        
        public String getDatabaseStatus() {
            return databaseStatus;
        }
        
        public void setDatabaseStatus(String databaseStatus) {
            this.databaseStatus = databaseStatus;
        }
        
        public String getRedisStatus() {
            return redisStatus;
        }
        
        public void setRedisStatus(String redisStatus) {
            this.redisStatus = redisStatus;
        }
        
        public String getMinioStatus() {
            return minioStatus;
        }
        
        public void setMinioStatus(String minioStatus) {
            this.minioStatus = minioStatus;
        }
    }
    
    @Override
    public String toString() {
        return "SystemStatsDTO{" +
                "userStats=" + userStats +
                ", imageStats=" + imageStats +
                ", pointsStats=" + pointsStats +
                ", systemStatus=" + systemStatus +
                '}';
    }
}
