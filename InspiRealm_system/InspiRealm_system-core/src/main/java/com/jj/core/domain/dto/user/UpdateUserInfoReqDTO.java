package com.jj.core.domain.dto.user;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

/**
 * 更新用户信息请求DTO
 * 
 * <AUTHOR>
 * @date 2024-07-29
 */
@ApiModel(description = "更新用户信息请求")
public class UpdateUserInfoReqDTO {
    
    /**
     * 用户昵称
     */
    @ApiModelProperty(value = "用户昵称", example = "张三")
    @Size(max = 50, message = "昵称长度不能超过50个字符")
    private String nickname;
    
    /**
     * 头像URL
     */
    @ApiModelProperty(value = "头像URL", example = "https://wx.qlogo.cn/mmopen/xxx")
    @Size(max = 500, message = "头像URL长度不能超过500个字符")
    private String avatarUrl;

    /**
     * 头像Base64数据
     */
    @ApiModelProperty(value = "头像Base64数据", example = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...")
    private String avatarBase64;
    
    /**
     * 性别 0:未知 1:男 2:女
     */
    @ApiModelProperty(value = "性别 0:未知 1:男 2:女", example = "1")
    private Integer gender;
    
    /**
     * 手机号
     */
    @ApiModelProperty(value = "手机号", example = "13800138000")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String phone;
    
    /**
     * 邮箱
     */
    @ApiModelProperty(value = "邮箱", example = "<EMAIL>")
    @Size(max = 100, message = "邮箱长度不能超过100个字符")
    private String email;
    
    public String getNickname() {
        return nickname;
    }
    
    public void setNickname(String nickname) {
        this.nickname = nickname;
    }
    
    public String getAvatarUrl() {
        return avatarUrl;
    }
    
    public void setAvatarUrl(String avatarUrl) {
        this.avatarUrl = avatarUrl;
    }

    public String getAvatarBase64() {
        return avatarBase64;
    }

    public void setAvatarBase64(String avatarBase64) {
        this.avatarBase64 = avatarBase64;
    }

    public Integer getGender() {
        return gender;
    }
    
    public void setGender(Integer gender) {
        this.gender = gender;
    }
    
    public String getPhone() {
        return phone;
    }
    
    public void setPhone(String phone) {
        this.phone = phone;
    }
    
    public String getEmail() {
        return email;
    }
    
    public void setEmail(String email) {
        this.email = email;
    }
    
    @Override
    public String toString() {
        return "UpdateUserInfoReqDTO{" +
                "nickname='" + nickname + '\'' +
                ", avatarUrl='" + avatarUrl + '\'' +
                ", gender=" + gender +
                ", phone='" + phone + '\'' +
                ", email='" + email + '\'' +
                '}';
    }
}
