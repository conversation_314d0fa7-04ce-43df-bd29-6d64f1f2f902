package com.jj.core.service.email;

/**
 * 邮件发送服务接口
 *
 * <AUTHOR>
 * @date 2025-01-05
 */
public interface EmailService {

    /**
     * 发送验证码邮件
     *
     * @param email 邮箱地址
     * @param code 验证码
     * @param type 验证类型 1:注册验证 2:密码重置 3:邮箱变更
     */
    void sendVerificationCode(String email, String code, Integer type);

    /**
     * 发送普通邮件
     *
     * @param to 收件人
     * @param subject 主题
     * @param content 内容
     */
    void sendSimpleEmail(String to, String subject, String content);

    /**
     * 发送HTML邮件
     *
     * @param to 收件人
     * @param subject 主题
     * @param htmlContent HTML内容
     */
    void sendHtmlEmail(String to, String subject, String htmlContent);
}