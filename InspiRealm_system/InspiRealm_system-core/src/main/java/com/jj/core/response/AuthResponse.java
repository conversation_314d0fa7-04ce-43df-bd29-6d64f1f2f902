package com.jj.core.response;

import com.jj.core.enums.AuthStatusEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * 认证响应结果类
 * 标准化的认证响应格式，便于前端统一处理
 *
 * <AUTHOR>
 * @date 2025-01-06
 */
@ApiModel(description = "认证响应结果")
public class AuthResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 认证状态码
     */
    @ApiModelProperty(value = "认证状态码", example = "200")
    private Integer authCode;

    /**
     * 认证状态描述
     */
    @ApiModelProperty(value = "认证状态描述", example = "认证成功")
    private String authMessage;

    /**
     * 是否需要重新登录
     */
    @ApiModelProperty(value = "是否需要重新登录", example = "false")
    private Boolean requireReLogin;

    /**
     * 是否认证成功
     */
    @ApiModelProperty(value = "是否认证成功", example = "true")
    private Boolean authSuccess;

    /**
     * 扩展信息（可选）
     */
    @ApiModelProperty(value = "扩展信息")
    private Object extraInfo;

    /**
     * 时间戳
     */
    @ApiModelProperty(value = "时间戳", example = "1690617600000")
    private Long timestamp;

    public AuthResponse() {
        this.timestamp = System.currentTimeMillis();
    }

    public AuthResponse(AuthStatusEnum authStatus) {
        this();
        this.authCode = authStatus.getCode();
        this.authMessage = authStatus.getMessage();
        this.requireReLogin = authStatus.isRequireReLogin();
        this.authSuccess = authStatus.isSuccess();
    }

    public AuthResponse(AuthStatusEnum authStatus, Object extraInfo) {
        this(authStatus);
        this.extraInfo = extraInfo;
    }

    /**
     * 创建成功响应
     *
     * @return 成功的认证响应
     */
    public static AuthResponse success() {
        return new AuthResponse(AuthStatusEnum.AUTH_SUCCESS);
    }

    /**
     * 创建成功响应（带扩展信息）
     *
     * @param extraInfo 扩展信息
     * @return 成功的认证响应
     */
    public static AuthResponse success(Object extraInfo) {
        return new AuthResponse(AuthStatusEnum.AUTH_SUCCESS, extraInfo);
    }

    /**
     * 创建失败响应
     *
     * @param authStatus 认证状态枚举
     * @return 失败的认证响应
     */
    public static AuthResponse failure(AuthStatusEnum authStatus) {
        return new AuthResponse(authStatus);
    }

    /**
     * 创建失败响应（带扩展信息）
     *
     * @param authStatus 认证状态枚举
     * @param extraInfo 扩展信息
     * @return 失败的认证响应
     */
    public static AuthResponse failure(AuthStatusEnum authStatus, Object extraInfo) {
        return new AuthResponse(authStatus, extraInfo);
    }

    /**
     * 创建Token无效响应
     *
     * @return Token无效的认证响应
     */
    public static AuthResponse tokenInvalid() {
        return new AuthResponse(AuthStatusEnum.TOKEN_INVALID);
    }

    /**
     * 创建Token过期响应
     *
     * @return Token过期的认证响应
     */
    public static AuthResponse tokenExpired() {
        return new AuthResponse(AuthStatusEnum.TOKEN_EXPIRED);
    }

    /**
     * 创建未登录响应
     *
     * @return 未登录的认证响应
     */
    public static AuthResponse notLoggedIn() {
        return new AuthResponse(AuthStatusEnum.NOT_LOGGED_IN);
    }

    /**
     * 创建权限不足响应
     *
     * @return 权限不足的认证响应
     */
    public static AuthResponse permissionDenied() {
        return new AuthResponse(AuthStatusEnum.PERMISSION_DENIED);
    }

    // Getter and Setter methods
    public Integer getAuthCode() {
        return authCode;
    }

    public void setAuthCode(Integer authCode) {
        this.authCode = authCode;
    }

    public String getAuthMessage() {
        return authMessage;
    }

    public void setAuthMessage(String authMessage) {
        this.authMessage = authMessage;
    }

    public Boolean getRequireReLogin() {
        return requireReLogin;
    }

    public void setRequireReLogin(Boolean requireReLogin) {
        this.requireReLogin = requireReLogin;
    }

    public Boolean getAuthSuccess() {
        return authSuccess;
    }

    public void setAuthSuccess(Boolean authSuccess) {
        this.authSuccess = authSuccess;
    }

    public Object getExtraInfo() {
        return extraInfo;
    }

    public void setExtraInfo(Object extraInfo) {
        this.extraInfo = extraInfo;
    }

    public Long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(Long timestamp) {
        this.timestamp = timestamp;
    }

    @Override
    public String toString() {
        return "AuthResponse{" +
                "authCode=" + authCode +
                ", authMessage='" + authMessage + '\'' +
                ", requireReLogin=" + requireReLogin +
                ", authSuccess=" + authSuccess +
                ", extraInfo=" + extraInfo +
                ", timestamp=" + timestamp +
                '}';
    }
}
