package com.jj.core.domain.dto.wechat;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;

/**
 * 微信登录请求DTO
 * 
 * <AUTHOR>
 * @date 2024-07-29
 */
@ApiModel(description = "微信登录请求")
public class WeChatLoginReqDTO {
    
    /**
     * 微信授权码
     */
    @ApiModelProperty(value = "微信授权码", required = true, example = "0123456789abcdef")
    @NotBlank(message = "微信授权码不能为空")
    private String code;
    
    /**
     * 用户信息
     */
    @ApiModelProperty(value = "用户信息")
    private WeChatUserInfoDTO userInfo;
    
    public String getCode() {
        return code;
    }
    
    public void setCode(String code) {
        this.code = code;
    }
    
    public WeChatUserInfoDTO getUserInfo() {
        return userInfo;
    }
    
    public void setUserInfo(WeChatUserInfoDTO userInfo) {
        this.userInfo = userInfo;
    }
    
    @Override
    public String toString() {
        return "WeChatLoginReqDTO{" +
                "code='" + code + '\'' +
                ", userInfo=" + userInfo +
                '}';
    }
}
