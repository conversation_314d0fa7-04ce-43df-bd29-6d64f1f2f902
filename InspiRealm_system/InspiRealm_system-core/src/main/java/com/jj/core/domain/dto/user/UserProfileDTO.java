package com.jj.core.domain.dto.user;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 用户资料DTO
 * 
 * <AUTHOR>
 * @date 2024-07-29
 */
@ApiModel(description = "用户资料")
public class UserProfileDTO {
    
    /**
     * 用户ID
     */
    @ApiModelProperty(value = "用户ID", example = "1")
    private Long id;
    
    /**
     * 微信OpenID
     */
    @ApiModelProperty(value = "微信OpenID", example = "oABC123DEF456")
    private String openId;
    
    /**
     * 用户昵称
     */
    @ApiModelProperty(value = "用户昵称", example = "张三")
    private String nickname;
    
    /**
     * 头像URL
     */
    @ApiModelProperty(value = "头像URL", example = "https://wx.qlogo.cn/mmopen/xxx")
    private String avatarUrl;
    
    /**
     * 性别 0:未知 1:男 2:女
     */
    @ApiModelProperty(value = "性别 0:未知 1:男 2:女", example = "1")
    private Integer gender;
    
    /**
     * 国家
     */
    @ApiModelProperty(value = "国家", example = "中国")
    private String country;
    
    /**
     * 省份
     */
    @ApiModelProperty(value = "省份", example = "广东")
    private String province;
    
    /**
     * 城市
     */
    @ApiModelProperty(value = "城市", example = "深圳")
    private String city;
    
    /**
     * 手机号
     */
    @ApiModelProperty(value = "手机号", example = "13800138000")
    private String phone;
    
    /**
     * 邮箱
     */
    @ApiModelProperty(value = "邮箱", example = "<EMAIL>")
    private String email;
    
    /**
     * 用户状态 0:禁用 1:正常
     */
    @ApiModelProperty(value = "用户状态 0:禁用 1:正常", example = "1")
    private Integer status;
    
    /**
     * 积分信息
     */
    @ApiModelProperty(value = "积分信息")
    private PointsInfoDTO pointsInfo;
    
    /**
     * 注册时间
     */
    @ApiModelProperty(value = "注册时间", example = "2024-07-29 10:30:00")
    private String createTime;
    
    /**
     * 最后登录时间
     */
    @ApiModelProperty(value = "最后登录时间", example = "2024-07-29 15:30:00")
    private String lastLoginTime;
    
    // Getter and Setter methods
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getOpenId() {
        return openId;
    }
    
    public void setOpenId(String openId) {
        this.openId = openId;
    }
    
    public String getNickname() {
        return nickname;
    }
    
    public void setNickname(String nickname) {
        this.nickname = nickname;
    }
    
    public String getAvatarUrl() {
        return avatarUrl;
    }
    
    public void setAvatarUrl(String avatarUrl) {
        this.avatarUrl = avatarUrl;
    }
    
    public Integer getGender() {
        return gender;
    }
    
    public void setGender(Integer gender) {
        this.gender = gender;
    }
    
    public String getCountry() {
        return country;
    }
    
    public void setCountry(String country) {
        this.country = country;
    }
    
    public String getProvince() {
        return province;
    }
    
    public void setProvince(String province) {
        this.province = province;
    }
    
    public String getCity() {
        return city;
    }
    
    public void setCity(String city) {
        this.city = city;
    }
    
    public String getPhone() {
        return phone;
    }
    
    public void setPhone(String phone) {
        this.phone = phone;
    }
    
    public String getEmail() {
        return email;
    }
    
    public void setEmail(String email) {
        this.email = email;
    }
    
    public Integer getStatus() {
        return status;
    }
    
    public void setStatus(Integer status) {
        this.status = status;
    }
    
    public PointsInfoDTO getPointsInfo() {
        return pointsInfo;
    }
    
    public void setPointsInfo(PointsInfoDTO pointsInfo) {
        this.pointsInfo = pointsInfo;
    }
    
    public String getCreateTime() {
        return createTime;
    }
    
    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }
    
    public String getLastLoginTime() {
        return lastLoginTime;
    }
    
    public void setLastLoginTime(String lastLoginTime) {
        this.lastLoginTime = lastLoginTime;
    }
    
    @Override
    public String toString() {
        return "UserProfileDTO{" +
                "id=" + id +
                ", openId='" + openId + '\'' +
                ", nickname='" + nickname + '\'' +
                ", avatarUrl='" + avatarUrl + '\'' +
                ", gender=" + gender +
                ", country='" + country + '\'' +
                ", province='" + province + '\'' +
                ", city='" + city + '\'' +
                ", phone='" + phone + '\'' +
                ", email='" + email + '\'' +
                ", status=" + status +
                ", pointsInfo=" + pointsInfo +
                ", createTime='" + createTime + '\'' +
                ", lastLoginTime='" + lastLoginTime + '\'' +
                '}';
    }
    
    /**
     * 积分信息DTO
     */
    @ApiModel(description = "积分信息")
    public static class PointsInfoDTO {
        
        /**
         * 总积分
         */
        @ApiModelProperty(value = "总积分", example = "1000")
        private Integer totalPoints;
        
        /**
         * 可用积分
         */
        @ApiModelProperty(value = "可用积分", example = "800")
        private Integer availablePoints;
        
        /**
         * 锁定积分
         */
        @ApiModelProperty(value = "锁定积分", example = "50")
        private Integer lockedPoints;
        
        /**
         * 已消费积分
         */
        @ApiModelProperty(value = "已消费积分", example = "150")
        private Integer consumedPoints;
        
        public Integer getTotalPoints() {
            return totalPoints;
        }
        
        public void setTotalPoints(Integer totalPoints) {
            this.totalPoints = totalPoints;
        }
        
        public Integer getAvailablePoints() {
            return availablePoints;
        }
        
        public void setAvailablePoints(Integer availablePoints) {
            this.availablePoints = availablePoints;
        }
        
        public Integer getLockedPoints() {
            return lockedPoints;
        }
        
        public void setLockedPoints(Integer lockedPoints) {
            this.lockedPoints = lockedPoints;
        }
        
        public Integer getConsumedPoints() {
            return consumedPoints;
        }
        
        public void setConsumedPoints(Integer consumedPoints) {
            this.consumedPoints = consumedPoints;
        }
        
        @Override
        public String toString() {
            return "PointsInfoDTO{" +
                    "totalPoints=" + totalPoints +
                    ", availablePoints=" + availablePoints +
                    ", lockedPoints=" + lockedPoints +
                    ", consumedPoints=" + consumedPoints +
                    '}';
        }
    }
}
