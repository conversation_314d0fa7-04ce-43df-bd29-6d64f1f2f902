package com.jj.core.auth;

import java.io.Serializable;

/**
 * 微信用户Token信息
 * 
 * <AUTHOR>
 * @date 2024-07-29
 */
public class WeChatTokenInfo implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 微信OpenID
     */
    private String openId;
    
    /**
     * Token类型
     */
    private String tokenType;
    
    /**
     * 创建时间
     */
    private Long createTime;
    
    public Long getUserId() {
        return userId;
    }
    
    public void setUserId(Long userId) {
        this.userId = userId;
    }
    
    public String getOpenId() {
        return openId;
    }
    
    public void setOpenId(String openId) {
        this.openId = openId;
    }
    
    public String getTokenType() {
        return tokenType;
    }
    
    public void setTokenType(String tokenType) {
        this.tokenType = tokenType;
    }
    
    public Long getCreateTime() {
        return createTime;
    }
    
    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }
    
    @Override
    public String toString() {
        return "WeChatTokenInfo{" +
                "userId=" + userId +
                ", openId='" + openId + '\'' +
                ", tokenType='" + tokenType + '\'' +
                ", createTime=" + createTime +
                '}';
    }
}
