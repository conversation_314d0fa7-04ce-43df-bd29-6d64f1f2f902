package com.jj.core.domain.dto.portal.web;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

/**
 * Web端Base64图片数据DTO
 *
 * <AUTHOR>
 * @date 2025-01-05
 */
@ApiModel(description = "Base64图片数据")
public class WebImageBase64DTO {

    @ApiModelProperty(value = "Base64图片数据", required = true, example = "iVBORw0KGgoAAAANSUhEUgAA...")
    @NotBlank(message = "Base64图片数据不能为空")
    private String base64;

    @ApiModelProperty(value = "文件名", required = true, example = "image.jpg")
    @NotBlank(message = "文件名不能为空")
    private String filename;

    @ApiModelProperty(value = "MIME类型", required = true, example = "image/jpeg")
    @NotBlank(message = "MIME类型不能为空")
    @Pattern(regexp = "^image/(jpeg|jpg|png|gif|webp)$", message = "只支持图片格式")
    private String mimeType;

    public String getBase64() {
        return base64;
    }

    public void setBase64(String base64) {
        this.base64 = base64;
    }

    public String getFilename() {
        return filename;
    }

    public void setFilename(String filename) {
        this.filename = filename;
    }

    public String getMimeType() {
        return mimeType;
    }

    public void setMimeType(String mimeType) {
        this.mimeType = mimeType;
    }

    @Override
    public String toString() {
        return "WebImageBase64DTO{" +
                "filename='" + filename + '\'' +
                ", mimeType='" + mimeType + '\'' +
                ", base64Length=" + (base64 != null ? base64.length() : 0) +
                '}';
    }
}
