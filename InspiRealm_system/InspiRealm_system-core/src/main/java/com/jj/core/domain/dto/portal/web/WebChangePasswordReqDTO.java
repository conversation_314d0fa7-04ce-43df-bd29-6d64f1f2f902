package com.jj.core.domain.dto.portal.web;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

/**
 * Web端修改密码请求DTO
 *
 * <AUTHOR>
 * @date 2025-01-05
 */
@ApiModel(description = "Web端修改密码请求")
public class WebChangePasswordReqDTO {

    @ApiModelProperty(value = "当前密码", required = true, example = "OldPassword123")
    @NotBlank(message = "当前密码不能为空")
    private String currentPassword;

    @ApiModelProperty(value = "新密码", required = true, example = "newpassword123")
    @NotBlank(message = "新密码不能为空")
    @Size(min = 6, max = 50, message = "密码长度必须在6-50位之间")
    private String newPassword;

    @ApiModelProperty(value = "确认新密码", required = true, example = "NewPassword123")
    @NotBlank(message = "确认新密码不能为空")
    private String confirmNewPassword;

    // Getters and Setters
    public String getCurrentPassword() { return currentPassword; }
    public void setCurrentPassword(String currentPassword) { this.currentPassword = currentPassword; }
    public String getNewPassword() { return newPassword; }
    public void setNewPassword(String newPassword) { this.newPassword = newPassword; }
    public String getConfirmNewPassword() { return confirmNewPassword; }
    public void setConfirmNewPassword(String confirmNewPassword) { this.confirmNewPassword = confirmNewPassword; }
}