package com.jj.core.domain.dto.admin;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * 积分管理请求DTO
 * 
 * <AUTHOR>
 * @date 2024-07-29
 */
@ApiModel(description = "积分管理请求")
public class PointsManageReqDTO {
    
    /**
     * 用户ID
     */
    @ApiModelProperty(value = "用户ID", required = true, example = "1")
    @NotNull(message = "用户ID不能为空")
    @Min(value = 1, message = "用户ID必须大于0")
    private Long userId;
    
    /**
     * 积分数量
     */
    @ApiModelProperty(value = "积分数量", required = true, example = "100")
    @NotNull(message = "积分数量不能为空")
    @Min(value = 1, message = "积分数量必须大于0")
    @Max(value = 10000, message = "积分数量不能超过10000")
    private Integer pointsAmount;
    
    /**
     * 操作类型 1:充值 2:扣除
     */
    @ApiModelProperty(value = "操作类型 1:充值 2:扣除", required = true, example = "1")
    @NotNull(message = "操作类型不能为空")
    @Min(value = 1, message = "操作类型值必须为1或2")
    @Max(value = 2, message = "操作类型值必须为1或2")
    private Integer operationType;
    
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注", example = "管理员手动充值")
    @Size(max = 200, message = "备注不能超过200个字符")
    private String remark;
    
    public Long getUserId() {
        return userId;
    }
    
    public void setUserId(Long userId) {
        this.userId = userId;
    }
    
    public Integer getPointsAmount() {
        return pointsAmount;
    }
    
    public void setPointsAmount(Integer pointsAmount) {
        this.pointsAmount = pointsAmount;
    }
    
    public Integer getOperationType() {
        return operationType;
    }
    
    public void setOperationType(Integer operationType) {
        this.operationType = operationType;
    }
    
    public String getRemark() {
        return remark;
    }
    
    public void setRemark(String remark) {
        this.remark = remark;
    }
    
    @Override
    public String toString() {
        return "PointsManageReqDTO{" +
                "userId=" + userId +
                ", pointsAmount=" + pointsAmount +
                ", operationType=" + operationType +
                ", remark='" + remark + '\'' +
                '}';
    }
}
