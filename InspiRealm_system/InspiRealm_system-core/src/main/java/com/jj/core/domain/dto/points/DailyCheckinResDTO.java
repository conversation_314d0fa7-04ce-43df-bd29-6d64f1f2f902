package com.jj.core.domain.dto.points;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 每日签到响应DTO
 * 
 * <AUTHOR>
 * @date 2024-07-29
 */
@ApiModel(description = "每日签到响应")
public class DailyCheckinResDTO {
    
    /**
     * 是否签到成功
     */
    @ApiModelProperty(value = "是否签到成功", example = "true")
    private Boolean success;
    
    /**
     * 获得积分
     */
    @ApiModelProperty(value = "获得积分", example = "10")
    private Integer earnedPoints;
    
    /**
     * 签到后总积分
     */
    @ApiModelProperty(value = "签到后总积分", example = "110")
    private Integer totalPoints;
    
    /**
     * 连续签到天数
     */
    @ApiModelProperty(value = "连续签到天数", example = "3")
    private Integer consecutiveDays;
    
    /**
     * 是否已签到
     */
    @ApiModelProperty(value = "是否已签到", example = "false")
    private Boolean alreadyCheckedIn;
    
    /**
     * 签到时间
     */
    @ApiModelProperty(value = "签到时间", example = "2024-07-29 10:30:00")
    private String checkinTime;
    
    public Boolean getSuccess() {
        return success;
    }
    
    public void setSuccess(Boolean success) {
        this.success = success;
    }
    
    public Integer getEarnedPoints() {
        return earnedPoints;
    }
    
    public void setEarnedPoints(Integer earnedPoints) {
        this.earnedPoints = earnedPoints;
    }
    
    public Integer getTotalPoints() {
        return totalPoints;
    }
    
    public void setTotalPoints(Integer totalPoints) {
        this.totalPoints = totalPoints;
    }
    
    public Integer getConsecutiveDays() {
        return consecutiveDays;
    }
    
    public void setConsecutiveDays(Integer consecutiveDays) {
        this.consecutiveDays = consecutiveDays;
    }
    
    public Boolean getAlreadyCheckedIn() {
        return alreadyCheckedIn;
    }
    
    public void setAlreadyCheckedIn(Boolean alreadyCheckedIn) {
        this.alreadyCheckedIn = alreadyCheckedIn;
    }
    
    public String getCheckinTime() {
        return checkinTime;
    }
    
    public void setCheckinTime(String checkinTime) {
        this.checkinTime = checkinTime;
    }
    
    @Override
    public String toString() {
        return "DailyCheckinResDTO{" +
                "success=" + success +
                ", earnedPoints=" + earnedPoints +
                ", totalPoints=" + totalPoints +
                ", consecutiveDays=" + consecutiveDays +
                ", alreadyCheckedIn=" + alreadyCheckedIn +
                ", checkinTime='" + checkinTime + '\'' +
                '}';
    }
}
