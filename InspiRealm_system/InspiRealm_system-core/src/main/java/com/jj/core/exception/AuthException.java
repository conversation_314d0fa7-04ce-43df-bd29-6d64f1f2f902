package com.jj.core.exception;

import com.jj.core.enums.AuthStatusEnum;

/**
 * 认证异常类
 * 专门用于认证相关的异常处理，包含详细的认证状态信息
 *
 * <AUTHOR>
 * @date 2025-01-06
 */
public class AuthException extends RuntimeException {

    private static final long serialVersionUID = 1L;

    /**
     * 认证状态枚举
     */
    private final AuthStatusEnum authStatus;

    /**
     * 扩展信息
     */
    private Object extraInfo;

    public AuthException(AuthStatusEnum authStatus) {
        super(authStatus.getMessage());
        this.authStatus = authStatus;
    }

    public AuthException(AuthStatusEnum authStatus, String message) {
        super(message);
        this.authStatus = authStatus;
    }

    public AuthException(AuthStatusEnum authStatus, String message, Throwable cause) {
        super(message, cause);
        this.authStatus = authStatus;
    }

    public AuthException(AuthStatusEnum authStatus, Object extraInfo) {
        super(authStatus.getMessage());
        this.authStatus = authStatus;
        this.extraInfo = extraInfo;
    }

    public AuthException(AuthStatusEnum authStatus, String message, Object extraInfo) {
        super(message);
        this.authStatus = authStatus;
        this.extraInfo = extraInfo;
    }

    /**
     * 创建Token无效异常
     *
     * @return Token无效异常
     */
    public static AuthException tokenInvalid() {
        return new AuthException(AuthStatusEnum.TOKEN_INVALID);
    }

    /**
     * 创建Token过期异常
     *
     * @return Token过期异常
     */
    public static AuthException tokenExpired() {
        return new AuthException(AuthStatusEnum.TOKEN_EXPIRED);
    }

    /**
     * 创建会话失效异常
     *
     * @return 会话失效异常
     */
    public static AuthException sessionInvalid() {
        return new AuthException(AuthStatusEnum.SESSION_INVALID);
    }

    /**
     * 创建Token缺失异常
     *
     * @return Token缺失异常
     */
    public static AuthException tokenMissing() {
        return new AuthException(AuthStatusEnum.TOKEN_MISSING);
    }

    /**
     * 创建Token格式错误异常
     *
     * @return Token格式错误异常
     */
    public static AuthException tokenFormatError() {
        return new AuthException(AuthStatusEnum.TOKEN_FORMAT_ERROR);
    }

    /**
     * 创建未登录异常
     *
     * @return 未登录异常
     */
    public static AuthException notLoggedIn() {
        return new AuthException(AuthStatusEnum.NOT_LOGGED_IN);
    }

    /**
     * 创建权限不足异常
     *
     * @return 权限不足异常
     */
    public static AuthException permissionDenied() {
        return new AuthException(AuthStatusEnum.PERMISSION_DENIED);
    }

    /**
     * 创建账户被禁用异常
     *
     * @return 账户被禁用异常
     */
    public static AuthException accountDisabled() {
        return new AuthException(AuthStatusEnum.ACCOUNT_DISABLED);
    }

    public AuthStatusEnum getAuthStatus() {
        return authStatus;
    }

    public Object getExtraInfo() {
        return extraInfo;
    }

    public void setExtraInfo(Object extraInfo) {
        this.extraInfo = extraInfo;
    }

    /**
     * 获取认证状态码
     *
     * @return 认证状态码
     */
    public int getAuthCode() {
        return authStatus.getCode();
    }

    /**
     * 是否需要重新登录
     *
     * @return true表示需要重新登录
     */
    public boolean isRequireReLogin() {
        return authStatus.isRequireReLogin();
    }

    /**
     * 是否为认证成功
     *
     * @return true表示认证成功
     */
    public boolean isAuthSuccess() {
        return authStatus.isSuccess();
    }
}
