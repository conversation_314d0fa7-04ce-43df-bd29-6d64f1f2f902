package com.jj.core.service.scheduled;

import com.jj.core.manager.MinioManager;
import com.jj.dao.mapper.ImageProcessTaskMapper;
import com.jj.dao.mapper.ProcessedImageMapper;
import com.jj.dao.model.image.ImageProcessTask;
import com.jj.dao.model.image.ProcessedImage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 图片数据定时清理服务
 * 每5小时执行一次，清理3天前的图片处理数据
 * 
 * <AUTHOR>
 * @date 2025-08-01
 */
@Service
public class ImageCleanupScheduledService {

    private static final Logger logger = LoggerFactory.getLogger(ImageCleanupScheduledService.class);

    @Autowired
    private ImageProcessTaskMapper imageProcessTaskMapper;

    @Autowired
    private ProcessedImageMapper processedImageMapper;

    @Autowired
    private MinioManager minioManager;

    /**
     * 清理天数：3天前的数据
     */
    private static final int CLEANUP_DAYS = 3;

    /**
     * 定时清理任务
     * 每5小时执行一次：5 * 60 * 60 * 1000 = 18000000毫秒
     */
    @Scheduled(fixedRate = 18000000)
    public void cleanupOldImageData() {
        logger.info("=== 开始执行图片数据定时清理任务 ===");
        
        try {
            // 执行清理
            cleanupImageProcessData();
            logger.info("=== 图片数据定时清理任务完成 ===");
        } catch (Exception e) {
            logger.error("图片数据定时清理任务执行失败", e);
        }
    }

    /**
     * 清理图片处理数据
     */
    @Transactional(rollbackFor = Exception.class)
    public void cleanupImageProcessData() {
        logger.info("开始清理{}天前的图片处理数据", CLEANUP_DAYS);

        // 1. 查询需要清理的任务
        List<ImageProcessTask> oldTasks = imageProcessTaskMapper.selectOldTasks(CLEANUP_DAYS);
        
        if (CollectionUtils.isEmpty(oldTasks)) {
            logger.info("没有发现需要清理的任务数据");
            return;
        }

        logger.info("发现 {} 个需要清理的任务", oldTasks.size());

        // 2. 提取任务ID列表
        List<Long> taskIds = oldTasks.stream()
                .map(ImageProcessTask::getId)
                .collect(Collectors.toList());

        // 3. 查询相关的处理后图片
        List<ProcessedImage> processedImages = processedImageMapper.selectByTaskIds(taskIds);
        
        if (!CollectionUtils.isEmpty(processedImages)) {
            logger.info("发现 {} 个需要清理的图片记录", processedImages.size());
            
            // 4. 清理图片文件和数据
            cleanupProcessedImages(processedImages);
        }

        // 5. 清理任务数据
        cleanupImageProcessTasks(oldTasks);

        logger.info("图片处理数据清理完成，清理任务数: {}, 清理图片数: {}", 
                oldTasks.size(), processedImages != null ? processedImages.size() : 0);
    }

    /**
     * 清理处理后的图片
     */
    private void cleanupProcessedImages(List<ProcessedImage> processedImages) {
        int successCount = 0;
        int failCount = 0;

        for (ProcessedImage image : processedImages) {
            try {
                cleanupSingleProcessedImage(image);
                successCount++;
                logger.debug("图片清理成功，imageId: {}, taskId: {}", image.getId(), image.getTaskId());
            } catch (Exception e) {
                failCount++;
                logger.error("图片清理失败，imageId: {}, taskId: {}", image.getId(), image.getTaskId(), e);
            }
        }

        logger.info("处理后图片清理完成，成功: {}, 失败: {}", successCount, failCount);
    }

    /**
     * 清理单个处理后的图片
     */
    private void cleanupSingleProcessedImage(ProcessedImage image) {
        logger.debug("开始清理图片，imageId: {}, taskId: {}", image.getId(), image.getTaskId());

        // 1. 删除MinIO文件
        deleteMinioFiles(image);

        // 2. 软删除数据库记录
        int deleteResult = processedImageMapper.softDeleteImage(image.getId());
        if (deleteResult <= 0) {
            logger.warn("软删除图片记录失败，imageId: {}", image.getId());
        } else {
            logger.debug("软删除图片记录成功，imageId: {}", image.getId());
        }
    }

    /**
     * 删除MinIO文件
     */
    private void deleteMinioFiles(ProcessedImage image) {
        // 删除主图片
        deleteMinioFileByUrl(image.getImageUrl(), "主图片");
        
        // 删除缩略图
        deleteMinioFileByUrl(image.getThumbnailUrl(), "缩略图");
        
        // 删除原图
        deleteMinioFileByUrl(image.getOriginalImageUrl(), "原图");
    }

    /**
     * 通过URL删除MinIO文件
     */
    private void deleteMinioFileByUrl(String url, String fileType) {
        if (!StringUtils.hasText(url)) {
            logger.debug("{}URL为空，跳过删除", fileType);
            return;
        }

        try {
            boolean deleted = minioManager.deleteFileByUrl(url);
            if (deleted) {
                logger.debug("{}删除成功，url: {}", fileType, url);
            } else {
                logger.debug("{}删除跳过（非MinIO地址或提取失败），url: {}", fileType, url);
            }
        } catch (Exception e) {
            logger.error("{}删除失败，url: {}", fileType, url, e);
            // 不抛出异常，避免影响整个清理流程
        }
    }

    /**
     * 清理图片处理任务
     */
    private void cleanupImageProcessTasks(List<ImageProcessTask> tasks) {
        int successCount = 0;
        int failCount = 0;

        for (ImageProcessTask task : tasks) {
            try {
                cleanupSingleImageProcessTask(task);
                successCount++;
                logger.debug("任务清理成功，taskId: {}, taskNo: {}", task.getId(), task.getTaskNo());
            } catch (Exception e) {
                failCount++;
                logger.error("任务清理失败，taskId: {}, taskNo: {}", task.getId(), task.getTaskNo(), e);
            }
        }

        logger.info("图片处理任务清理完成，成功: {}, 失败: {}", successCount, failCount);
    }

    /**
     * 清理单个图片处理任务
     */
    private void cleanupSingleImageProcessTask(ImageProcessTask task) {
        logger.debug("开始清理任务，taskId: {}, taskNo: {}", task.getId(), task.getTaskNo());

        // 软删除任务记录
        int deleteResult = imageProcessTaskMapper.softDeleteTask(task.getId());
        if (deleteResult <= 0) {
            logger.warn("软删除任务记录失败，taskId: {}", task.getId());
        } else {
            logger.debug("软删除任务记录成功，taskId: {}", task.getId());
        }
    }

    /**
     * 手动触发清理（用于测试或管理员操作）
     */
    public void manualCleanup() {
        logger.info("手动触发图片数据清理");
        cleanupOldImageData();
    }
}
