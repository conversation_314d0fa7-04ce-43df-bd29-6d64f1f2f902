package com.jj.core.domain.dto.points;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 积分余额DTO
 * 
 * <AUTHOR>
 * @date 2024-07-29
 */
@ApiModel(description = "积分余额信息")
public class PointsBalanceDTO {
    
    /**
     * 用户ID
     */
    @ApiModelProperty(value = "用户ID", example = "1")
    private Long userId;
    
    /**
     * 总积分
     */
    @ApiModelProperty(value = "总积分", example = "1000")
    private Integer totalPoints;
    
    /**
     * 可用积分
     */
    @ApiModelProperty(value = "可用积分", example = "800")
    private Integer availablePoints;
    
    /**
     * 锁定积分
     */
    @ApiModelProperty(value = "锁定积分", example = "50")
    private Integer lockedPoints;
    
    /**
     * 已消费积分
     */
    @ApiModelProperty(value = "已消费积分", example = "150")
    private Integer consumedPoints;
    
    public Long getUserId() {
        return userId;
    }
    
    public void setUserId(Long userId) {
        this.userId = userId;
    }
    
    public Integer getTotalPoints() {
        return totalPoints;
    }
    
    public void setTotalPoints(Integer totalPoints) {
        this.totalPoints = totalPoints;
    }
    
    public Integer getAvailablePoints() {
        return availablePoints;
    }
    
    public void setAvailablePoints(Integer availablePoints) {
        this.availablePoints = availablePoints;
    }
    
    public Integer getLockedPoints() {
        return lockedPoints;
    }
    
    public void setLockedPoints(Integer lockedPoints) {
        this.lockedPoints = lockedPoints;
    }
    
    public Integer getConsumedPoints() {
        return consumedPoints;
    }
    
    public void setConsumedPoints(Integer consumedPoints) {
        this.consumedPoints = consumedPoints;
    }
    
    @Override
    public String toString() {
        return "PointsBalanceDTO{" +
                "userId=" + userId +
                ", totalPoints=" + totalPoints +
                ", availablePoints=" + availablePoints +
                ", lockedPoints=" + lockedPoints +
                ", consumedPoints=" + consumedPoints +
                '}';
    }
}
