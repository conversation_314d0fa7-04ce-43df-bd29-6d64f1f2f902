package com.jj.core.service.admin;

import com.jj.common.exception.BizException;
import com.jj.core.auth.TokenManager;
import com.jj.core.domain.dto.admin.AdminLoginReqDTO;
import com.jj.core.domain.dto.admin.AdminLoginResDTO;
import com.jj.core.domain.dto.admin.AdminChangePasswordReqDTO;
import com.jj.dao.mapper.AdminUserMapper;
import com.jj.dao.model.admin.AdminUser;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.DigestUtils;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;

/**
 * 管理员认证服务
 * 
 * <AUTHOR>
 * @date 2024-07-29
 */
@Service
public class AdminAuthService {
    
    private static final Logger logger = LoggerFactory.getLogger(AdminAuthService.class);
    
    @Autowired
    private AdminUserMapper adminUserMapper;
    
    @Autowired
    private TokenManager tokenManager;
    
    /**
     * 管理员登录
     * 
     * @param reqDTO 登录请求
     * @param request HTTP请求
     * @return 登录响应
     */
    public AdminLoginResDTO login(AdminLoginReqDTO reqDTO, HttpServletRequest request) {
        try {
            logger.info("管理员登录请求，username: {}", reqDTO.getUsername());
            
            // 查询管理员用户
            AdminUser adminUser = adminUserMapper.selectByUsername(reqDTO.getUsername());
            if (adminUser == null) {
                throw new BizException("用户名或密码错误");
            }
            
            // 检查用户状态
            if (adminUser.getStatus() != 1) {
                throw new BizException("账号已被禁用，请联系系统管理员");
            }
            
            // 验证密码
            String encryptedPassword = encryptPassword(reqDTO.getPassword());
            if (!encryptedPassword.equals(adminUser.getPassword())) {
                throw new BizException("用户名或密码错误");
            }
            
            // 生成Token
            String token = tokenManager.generateAdminToken(adminUser.getId(), adminUser.getUsername(), adminUser.getRole());
            
            // 更新最后登录信息
            String clientIp = getClientIp(request);
            adminUserMapper.updateLastLoginInfo(adminUser.getId(), new Date(), clientIp);
            
            // 构建响应
            AdminLoginResDTO resDTO = new AdminLoginResDTO();
            resDTO.setToken(token);
            resDTO.setAdminId(adminUser.getId());
            resDTO.setUsername(adminUser.getUsername());
            resDTO.setRealName(adminUser.getRealName());
            resDTO.setRole(adminUser.getRole());
            resDTO.setRoleDesc(getRoleDesc(adminUser.getRole()));
            resDTO.setExpiresIn(TokenManager.ADMIN_TOKEN_EXPIRE_TIME);
            
            logger.info("管理员登录成功，username: {}, adminId: {}, role: {}", 
                    reqDTO.getUsername(), adminUser.getId(), adminUser.getRole());
            return resDTO;
            
        } catch (BizException e) {
            logger.error("管理员登录失败：{}", e.getMessage());
            throw e;
        } catch (Exception e) {
            logger.error("管理员登录异常", e);
            throw new BizException("登录失败，请重试");
        }
    }
    
    /**
     * 密码加密
     * 
     * @param password 原始密码
     * @return 加密后的密码
     */
    public String encryptPassword(String password) {
        // 使用MD5加密，实际项目中建议使用BCrypt等更安全的加密方式
        return DigestUtils.md5DigestAsHex(password.getBytes());
    }
    
    /**
     * 获取角色描述
     * 
     * @param role 角色代码
     * @return 角色描述
     */
    private String getRoleDesc(Integer role) {
        switch (role) {
            case 1:
                return "超级管理员";
            case 2:
                return "普通管理员";
            default:
                return "未知角色";
        }
    }
    
    /**
     * 获取客户端IP地址
     * 
     * @param request HTTP请求
     * @return IP地址
     */
    private String getClientIp(HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For");
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_CLIENT_IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        
        // 处理多个IP的情况，取第一个IP
        if (ip != null && ip.contains(",")) {
            ip = ip.split(",")[0].trim();
        }
        
        return ip;
    }

    /**
     * 修改密码
     *
     * @param reqDTO 修改密码请求
     * @param request HTTP请求
     */
    public void changePassword(AdminChangePasswordReqDTO reqDTO, HttpServletRequest request) {
        try {
            logger.info("管理员修改密码请求");

            // 从Token中获取当前管理员ID
            Long adminId = tokenManager.getAdminIdFromRequest(request);
            if (adminId == null) {
                throw new BizException("未登录或登录已过期");
            }

            // 查询管理员用户
            AdminUser adminUser = adminUserMapper.selectById(adminId);
            if (adminUser == null) {
                throw new BizException("用户不存在");
            }

            // 验证原密码
            String encryptedOldPassword = encryptPassword(reqDTO.getOldPassword());
            if (!encryptedOldPassword.equals(adminUser.getPassword())) {
                throw new BizException("原密码错误");
            }

            // 检查新密码是否与原密码相同
            String encryptedNewPassword = encryptPassword(reqDTO.getNewPassword());
            if (encryptedNewPassword.equals(adminUser.getPassword())) {
                throw new BizException("新密码不能与原密码相同");
            }

            // 更新密码
            adminUser.setPassword(encryptedNewPassword);
            adminUserMapper.updateById(adminUser);

            logger.info("管理员修改密码成功，adminId: {}", adminId);

        } catch (BizException e) {
            logger.error("管理员修改密码失败：{}", e.getMessage());
            throw e;
        } catch (Exception e) {
            logger.error("管理员修改密码异常", e);
            throw new BizException("修改密码失败，请重试");
        }
    }
}
