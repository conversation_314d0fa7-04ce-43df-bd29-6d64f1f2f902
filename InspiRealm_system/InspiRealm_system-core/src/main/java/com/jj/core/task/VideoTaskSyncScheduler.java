package com.jj.core.task;

import com.jj.core.service.video.Veo3ApiService;
import com.jj.dao.mapper.VideoGenerationTaskMapper;
import com.jj.dao.mapper.WebUserPointsMapper;
import com.jj.dao.model.video.VideoGenerationTask;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.Date;
import java.util.List;

/**
 * 视频任务状态同步定时器
 *
 * <AUTHOR>
 * @date 2025-01-05
 */
@Component
public class VideoTaskSyncScheduler {

    private static final Logger logger = LoggerFactory.getLogger(VideoTaskSyncScheduler.class);

    @Autowired
    private VideoGenerationTaskMapper videoGenerationTaskMapper;

    @Autowired
    private Veo3ApiService veo3ApiService;

    @Autowired
    private WebUserPointsMapper webUserPointsMapper;

    /**
     * 每50秒同步一次处理中的任务状态
     */
    @Scheduled(fixedRate = 5000)
    public void syncProcessingTasks() {
        try {
            // 查询所有处理中的任务
            List<VideoGenerationTask> processingTasks = videoGenerationTaskMapper.selectProcessingTasks();

            if (processingTasks.isEmpty()) {
                return;
            }

            logger.info("开始同步处理中的任务状态，任务数量: {}", processingTasks.size());

            for (VideoGenerationTask task : processingTasks) {
                try {
                    syncSingleTask(task);
                } catch (Exception e) {
                    logger.error("同步任务状态失败，任务ID: {}", task.getId(), e);
                }
            }

            logger.info("任务状态同步完成");

        } catch (Exception e) {
            logger.error("任务状态同步异常", e);
        }
    }

    /**
     * 同步单个任务状态
     */
    private void syncSingleTask(VideoGenerationTask task) {
        if (!StringUtils.hasText(task.getExternalTaskId())) {
            logger.warn("任务外部ID为空，跳过同步，任务ID: {}", task.getId());
            return;
        }

        try {
            // 查询Veo3任务状态
            Veo3ApiService.Veo3TaskStatus veo3Status = veo3ApiService.queryTaskStatus(task.getExternalTaskId());

            // 更新任务状态
            boolean needUpdate = false;

            // 根据Veo3状态更新本地任务状态
            switch (veo3Status.getStatus().toLowerCase()) {
                case "pending":
                case "processing":
                    if (task.getStatus() != 2) {
                        task.setStatus(2); // 处理中
                        needUpdate = true;
                    }
                    break;

                case "completed":
                case "success":
                    if (task.getStatus() != 3) {
                        task.setStatus(3); // 已完成
                        task.setProgress(100);
                        task.setEndTime(new Date());
                        if (StringUtils.hasText(veo3Status.getVideoUrl())) {
                            task.setVideoUrl(veo3Status.getVideoUrl());
                        }

                        // 处理积分：视频生成成功时
                        handlePointsOnSuccess(task);

                        needUpdate = true;
                    }
                    break;

                case "failed":
                case "error":
                    if (task.getStatus() != 4) {
                        task.setStatus(4); // 失败
                        task.setEndTime(new Date());
                        if (StringUtils.hasText(veo3Status.getErrorMessage())) {
                            task.setErrorMessage(veo3Status.getErrorMessage());
                        }

                        // 处理积分：视频生成失败时
                        handlePointsOnFailure(task);

                        needUpdate = true;
                    }
                    break;

                default:
                    logger.warn("未知的Veo3任务状态: {}, 任务ID: {}", veo3Status.getStatus(), task.getId());
                    break;
            }

            if (needUpdate) {
                task.setUpdateTime(new Date());
                videoGenerationTaskMapper.updateById(task);
                logger.info("任务状态已更新，任务ID: {}, 状态: {}", task.getId(), task.getStatus());
            }

        } catch (Exception e) {
            logger.error("同步任务状态失败，任务ID: {}, 外部任务ID: {}", task.getId(), task.getExternalTaskId(), e);
        }
    }

    /**
     * 处理视频生成成功时的积分逻辑
     * 成功时：locked_points -= pointsCost, consumed_points += pointsCost, available_points 不变
     */
    private void handlePointsOnSuccess(VideoGenerationTask task) {
        try {
            Long userId = task.getUserId();
            Integer pointsCost = task.getPointsCost();

            if (userId == null || pointsCost == null || pointsCost <= 0) {
                logger.warn("积分处理参数无效，userId: {}, pointsCost: {}, 任务ID: {}",
                        userId, pointsCost, task.getId());
                return;
            }

            logger.info("开始处理视频生成成功的积分，userId: {}, pointsCost: {}, 任务ID: {}",
                    userId, pointsCost, task.getId());

            // 视频生成成功：释放锁定积分并记录消费
            int result = webUserPointsMapper.confirmConsumePoints(userId, pointsCost);

            if (result > 0) {
                logger.info("积分消费确认成功，userId: {}, pointsCost: {}, 任务ID: {}",
                        userId, pointsCost, task.getId());
            } else {
                logger.error("积分消费确认失败，userId: {}, pointsCost: {}, 任务ID: {}",
                        userId, pointsCost, task.getId());
            }

        } catch (Exception e) {
            logger.error("处理视频生成成功积分时发生异常，任务ID: {}", task.getId(), e);
        }
    }

    /**
     * 处理视频生成失败时的积分逻辑
     * 失败时：locked_points -= pointsCost, available_points += pointsCost, consumed_points 不变
     */
    private void handlePointsOnFailure(VideoGenerationTask task) {
        try {
            Long userId = task.getUserId();
            Integer pointsCost = task.getPointsCost();

            if (userId == null || pointsCost == null || pointsCost <= 0) {
                logger.warn("积分处理参数无效，userId: {}, pointsCost: {}, 任务ID: {}",
                        userId, pointsCost, task.getId());
                return;
            }

            logger.info("开始处理视频生成失败的积分退还，userId: {}, pointsCost: {}, 任务ID: {}",
                    userId, pointsCost, task.getId());

            // 视频生成失败：释放锁定积分并退还给用户
            int result = webUserPointsMapper.unlockPoints(userId, pointsCost);

            if (result > 0) {
                logger.info("积分退还成功，userId: {}, pointsCost: {}, 任务ID: {}",
                        userId, pointsCost, task.getId());
            } else {
                logger.error("积分退还失败，userId: {}, pointsCost: {}, 任务ID: {}",
                        userId, pointsCost, task.getId());
            }

        } catch (Exception e) {
            logger.error("处理视频生成失败积分时发生异常，任务ID: {}", task.getId(), e);
        }
    }
}