package com.jj.core.service.user.impl;

import com.jj.core.domain.dto.portal.web.*;
import com.jj.core.service.user.WebUserService;
import com.jj.dao.mapper.WebUserMapper;
import com.jj.dao.mapper.WebUserPointsMapper;
import com.jj.dao.model.user.WebUser;
import com.jj.dao.model.points.WebUserPoints;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;

/**
 * Web端用户管理服务实现类
 *
 * <AUTHOR>
 * @date 2025-01-05
 */
@Service
@Transactional
public class WebUserServiceImpl implements WebUserService {

    private static final Logger logger = LoggerFactory.getLogger(WebUserServiceImpl.class);

    @Autowired
    private WebUserMapper webUserMapper;

    @Autowired
    private WebUserPointsMapper webUserPointsMapper;

    @Override
    public WebUserProfileResDTO getUserProfile() {
        logger.info("获取用户信息");

        try {
            Long currentUserId = getCurrentUserId();
            if (currentUserId == null) {
                throw new RuntimeException("用户未登录");
            }

            WebUser webUser = webUserMapper.selectById(currentUserId);
            if (webUser == null) {
                throw new RuntimeException("用户不存在");
            }

            WebUserProfileResDTO resDTO = new WebUserProfileResDTO();
            resDTO.setId(webUser.getId());
            resDTO.setUsername(webUser.getUsername());
            resDTO.setEmail(webUser.getEmail());
            resDTO.setNickname(webUser.getNickname());
            resDTO.setAvatarUrl(webUser.getAvatarUrl());
            resDTO.setPhone(webUser.getPhone());
            resDTO.setGender(webUser.getGender());
            resDTO.setBirthday(formatDate(webUser.getBirthday()));
            resDTO.setCountry(webUser.getCountry());
            resDTO.setProvince(webUser.getProvince());
            resDTO.setCity(webUser.getCity());
            resDTO.setUserType(webUser.getUserType());
            resDTO.setEmailVerified(webUser.getEmailVerified() == 1);
            resDTO.setCreateTime(formatDate(webUser.getCreateTime()));

            return resDTO;

        } catch (Exception e) {
            logger.error("获取用户信息失败", e);
            throw new RuntimeException("获取用户信息失败: " + e.getMessage());
        }
    }

    @Override
    public void updateUserProfile(WebUpdateUserProfileReqDTO reqDTO) {
        logger.info("更新用户信息");

        try {
            Long currentUserId = getCurrentUserId();
            WebUser webUser = webUserMapper.selectById(currentUserId);

            if (reqDTO.getNickname() != null) webUser.setNickname(reqDTO.getNickname());
            if (reqDTO.getAvatarUrl() != null) webUser.setAvatarUrl(reqDTO.getAvatarUrl());
            if (reqDTO.getPhone() != null) webUser.setPhone(reqDTO.getPhone());
            if (reqDTO.getGender() != null) webUser.setGender(reqDTO.getGender());
            if (reqDTO.getCountry() != null) webUser.setCountry(reqDTO.getCountry());
            if (reqDTO.getProvince() != null) webUser.setProvince(reqDTO.getProvince());
            if (reqDTO.getCity() != null) webUser.setCity(reqDTO.getCity());

            webUserMapper.updateById(webUser);
            logger.info("用户信息更新成功");

        } catch (Exception e) {
            logger.error("更新用户信息失败", e);
            throw new RuntimeException("更新用户信息失败: " + e.getMessage());
        }
    }

    @Override
    public void changePassword(WebChangePasswordReqDTO reqDTO) {
        logger.info("修改用户密码");

        try {
            if (!reqDTO.getNewPassword().equals(reqDTO.getConfirmNewPassword())) {
                throw new RuntimeException("两次输入的新密码不一致");
            }

            Long currentUserId = getCurrentUserId();
            WebUser webUser = webUserMapper.selectById(currentUserId);

            if (!verifyPassword(reqDTO.getCurrentPassword(), webUser.getPasswordHash())) {
                throw new RuntimeException("当前密码错误");
            }

            webUser.setPasswordHash(hashPassword(reqDTO.getNewPassword()));
            webUserMapper.updateById(webUser);

            logger.info("密码修改成功");

        } catch (Exception e) {
            logger.error("修改密码失败", e);
            throw new RuntimeException("修改密码失败: " + e.getMessage());
        }
    }

    @Override
    public WebUserPointsResDTO getUserPoints() {
        logger.info("获取用户积分信息");

        try {
            Long currentUserId = getCurrentUserId();
            WebUserPoints userPoints = webUserPointsMapper.selectByUserId(currentUserId);

            WebUserPointsResDTO resDTO = new WebUserPointsResDTO();
            resDTO.setUserId(currentUserId);
            resDTO.setTotalPoints(userPoints.getTotalPoints());
            resDTO.setAvailablePoints(userPoints.getAvailablePoints());
            resDTO.setLockedPoints(userPoints.getLockedPoints());
            resDTO.setConsumedPoints(userPoints.getConsumedPoints());

            return resDTO;

        } catch (Exception e) {
            logger.error("获取积分信息失败", e);
            throw new RuntimeException("获取积分信息失败: " + e.getMessage());
        }
    }

    @Override
    public WebPointsHistoryResDTO getPointsHistory(WebPointsHistoryReqDTO reqDTO) {
        logger.info("获取积分历史记录");

        try {
            // TODO: 实现积分历史查询
            WebPointsHistoryResDTO resDTO = new WebPointsHistoryResDTO();
            resDTO.setTotal(0L);
            resDTO.setPage(reqDTO.getPage());
            resDTO.setPageSize(reqDTO.getPageSize());
            resDTO.setRecords(new ArrayList<>());

            return resDTO;

        } catch (Exception e) {
            logger.error("获取积分历史失败", e);
            throw new RuntimeException("获取积分历史失败: " + e.getMessage());
        }
    }

    @Override
    public WebUserStatisticsResDTO getUserStatistics() {
        logger.info("获取用户统计信息");

        try {
            // TODO: 实现用户统计查询
            WebUserStatisticsResDTO resDTO = new WebUserStatisticsResDTO();
            resDTO.setTotalVideoGenerated(0);
            resDTO.setSuccessfulGenerated(0);
            resDTO.setFailedGenerated(0);
            resDTO.setTotalPointsConsumed(0);
            resDTO.setMonthlyGenerated(0);
            resDTO.setWeeklyGenerated(0);
            resDTO.setDailyGenerated(0);
            resDTO.setAvgGenerationTime(0);

            return resDTO;

        } catch (Exception e) {
            logger.error("获取统计信息失败", e);
            throw new RuntimeException("获取统计信息失败: " + e.getMessage());
        }
    }

    // 辅助方法
    private Long getCurrentUserId() {
        // 从请求上下文中获取当前用户ID
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes != null) {
            HttpServletRequest request = attributes.getRequest();
            Object userId = request.getAttribute("currentUserId");
            if (userId instanceof Long) {
                return (Long) userId;
            }
        }
        throw new RuntimeException("无法获取当前用户信息");
    }

    private String hashPassword(String password) {
        try {
            MessageDigest md = MessageDigest.getInstance("SHA-256");
            byte[] hash = md.digest(password.getBytes(StandardCharsets.UTF_8));
            StringBuilder hexString = new StringBuilder();
            for (byte b : hash) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) hexString.append('0');
                hexString.append(hex);
            }
            return hexString.toString();
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("密码加密失败", e);
        }
    }

    private boolean verifyPassword(String password, String hashedPassword) {
        return hashPassword(password).equals(hashedPassword);
    }

    private String formatDate(Date date) {
        if (date == null) return null;
        LocalDateTime localDateTime = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
        return localDateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    }
}