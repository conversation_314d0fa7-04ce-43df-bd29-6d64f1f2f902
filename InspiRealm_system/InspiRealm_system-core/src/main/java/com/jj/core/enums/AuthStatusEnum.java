package com.jj.core.enums;

/**
 * 认证状态码枚举
 * 定义标准化的认证状态码，便于前端统一处理
 *
 * <AUTHOR>
 * @date 2025-01-06
 */
public enum AuthStatusEnum {

    /**
     * 认证成功
     */
    AUTH_SUCCESS(200, "认证成功", false),

    /**
     * 认证失败 - 通用认证失败
     */
    AUTH_FAILED(401, "认证失败", true),

    /**
     * Token无效 - Token格式错误或不存在
     */
    TOKEN_INVALID(4001, "访问令牌无效", true),

    /**
     * Token过期 - Token已过期
     */
    TOKEN_EXPIRED(4002, "访问令牌已过期", true),

    /**
     * 会话失效 - 会话状态异常
     */
    SESSION_INVALID(4003, "会话已失效", true),

    /**
     * 未提供Token - 缺少认证信息
     */
    TOKEN_MISSING(4004, "未提供访问令牌", true),

    /**
     * Token格式错误 - Authorization头格式不正确
     */
    TOKEN_FORMAT_ERROR(4005, "访问令牌格式错误", true),

    /**
     * 权限不足 - 用户权限不够
     */
    PERMISSION_DENIED(403, "权限不足", false),

    /**
     * 未登录 - 需要登录但未提供有效认证
     */
    NOT_LOGGED_IN(4006, "未登录，请先登录", true),

    /**
     * 账户被禁用
     */
    ACCOUNT_DISABLED(4007, "账户已被禁用", true),

    /**
     * 认证服务异常
     */
    AUTH_SERVICE_ERROR(500, "认证服务异常", false);

    /**
     * 状态码
     */
    private final int code;

    /**
     * 状态描述
     */
    private final String message;

    /**
     * 是否需要重新登录
     */
    private final boolean requireReLogin;

    AuthStatusEnum(int code, String message, boolean requireReLogin) {
        this.code = code;
        this.message = message;
        this.requireReLogin = requireReLogin;
    }

    public int getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }

    public boolean isRequireReLogin() {
        return requireReLogin;
    }

    /**
     * 根据状态码获取枚举
     *
     * @param code 状态码
     * @return 对应的枚举，如果不存在返回null
     */
    public static AuthStatusEnum getByCode(int code) {
        for (AuthStatusEnum status : values()) {
            if (status.getCode() == code) {
                return status;
            }
        }
        return null;
    }

    /**
     * 判断是否为成功状态
     *
     * @return true表示成功，false表示失败
     */
    public boolean isSuccess() {
        return this == AUTH_SUCCESS;
    }

    /**
     * 判断是否为认证相关错误（4xx）
     *
     * @return true表示认证错误，false表示其他错误
     */
    public boolean isAuthError() {
        return code >= 4000 && code < 5000;
    }
}
