package com.jj.core.domain.dto.square;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 广场图片DTO
 * 
 * <AUTHOR>
 * @date 2024-07-29
 */
@ApiModel(description = "广场图片")
public class SquareImageDTO {
    
    /**
     * 图片ID
     */
    @ApiModelProperty(value = "图片ID", example = "1")
    private Long id;
    
    /**
     * 图片URL
     */
    @ApiModelProperty(value = "图片URL", example = "https://minio.example.com/bucket/image.jpg")
    private String imageUrl;
    
    /**
     * 缩略图URL
     */
    @ApiModelProperty(value = "缩略图URL", example = "https://minio.example.com/bucket/thumbnail.jpg")
    private String thumbnailUrl;
    
    /**
     * 图片宽度
     */
    @ApiModelProperty(value = "图片宽度", example = "1024")
    private Integer imageWidth;
    
    /**
     * 图片高度
     */
    @ApiModelProperty(value = "图片高度", example = "768")
    private Integer imageHeight;
    
    /**
     * 图片比例
     */
    @ApiModelProperty(value = "图片比例", example = "4:3")
    private String aspectRatio;
    
    /**
     * 处理类型
     */
    @ApiModelProperty(value = "处理类型", example = "beautify")
    private String processType;
    
    /**
     * 点赞数
     */
    @ApiModelProperty(value = "点赞数", example = "10")
    private Integer likeCount;
    
    /**
     * 查看数
     */
    @ApiModelProperty(value = "查看数", example = "100")
    private Integer viewCount;
    
    /**
     * 用户信息
     */
    @ApiModelProperty(value = "用户信息")
    private UserInfoDTO userInfo;
    
    /**
     * 是否已点赞
     */
    @ApiModelProperty(value = "是否已点赞", example = "false")
    private Boolean isLiked;
    
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间", example = "2024-07-29 10:30:00")
    private String createTime;


    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getImageUrl() {
        return imageUrl;
    }
    
    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }
    
    public String getThumbnailUrl() {
        return thumbnailUrl;
    }
    
    public void setThumbnailUrl(String thumbnailUrl) {
        this.thumbnailUrl = thumbnailUrl;
    }
    
    public Integer getImageWidth() {
        return imageWidth;
    }
    
    public void setImageWidth(Integer imageWidth) {
        this.imageWidth = imageWidth;
    }
    
    public Integer getImageHeight() {
        return imageHeight;
    }
    
    public void setImageHeight(Integer imageHeight) {
        this.imageHeight = imageHeight;
    }
    
    public String getAspectRatio() {
        return aspectRatio;
    }
    
    public void setAspectRatio(String aspectRatio) {
        this.aspectRatio = aspectRatio;
    }
    
    public String getProcessType() {
        return processType;
    }
    
    public void setProcessType(String processType) {
        this.processType = processType;
    }
    
    public Integer getLikeCount() {
        return likeCount;
    }
    
    public void setLikeCount(Integer likeCount) {
        this.likeCount = likeCount;
    }
    
    public Integer getViewCount() {
        return viewCount;
    }
    
    public void setViewCount(Integer viewCount) {
        this.viewCount = viewCount;
    }
    
    public UserInfoDTO getUserInfo() {
        return userInfo;
    }
    
    public void setUserInfo(UserInfoDTO userInfo) {
        this.userInfo = userInfo;
    }
    
    public Boolean getIsLiked() {
        return isLiked;
    }
    
    public void setIsLiked(Boolean isLiked) {
        this.isLiked = isLiked;
    }
    
    public String getCreateTime() {
        return createTime;
    }
    
    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }
    
    @Override
    public String toString() {
        return "SquareImageDTO{" +
                "id=" + id +
                ", imageUrl='" + imageUrl + '\'' +
                ", thumbnailUrl='" + thumbnailUrl + '\'' +
                ", imageWidth=" + imageWidth +
                ", imageHeight=" + imageHeight +
                ", aspectRatio='" + aspectRatio + '\'' +
                ", processType='" + processType + '\'' +
                ", likeCount=" + likeCount +
                ", viewCount=" + viewCount +
                ", userInfo=" + userInfo +
                ", isLiked=" + isLiked +
                ", createTime='" + createTime + '\'' +
                '}';
    }
    
    /**
     * 用户信息DTO
     */
    @ApiModel(description = "用户信息")
    public static class UserInfoDTO {
        
        /**
         * 用户ID
         */
        @ApiModelProperty(value = "用户ID", example = "1")
        private Long id;
        
        /**
         * 用户昵称
         */
        @ApiModelProperty(value = "用户昵称", example = "张三")
        private String nickname;
        
        /**
         * 头像URL
         */
        @ApiModelProperty(value = "头像URL", example = "https://wx.qlogo.cn/mmopen/xxx")
        private String avatarUrl;
        
        public Long getId() {
            return id;
        }
        
        public void setId(Long id) {
            this.id = id;
        }
        
        public String getNickname() {
            return nickname;
        }
        
        public void setNickname(String nickname) {
            this.nickname = nickname;
        }
        
        public String getAvatarUrl() {
            return avatarUrl;
        }
        
        public void setAvatarUrl(String avatarUrl) {
            this.avatarUrl = avatarUrl;
        }
        
        @Override
        public String toString() {
            return "UserInfoDTO{" +
                    "id=" + id +
                    ", nickname='" + nickname + '\'' +
                    ", avatarUrl='" + avatarUrl + '\'' +
                    '}';
        }
    }
}
