package com.jj.core.auth;

/**
 * 用户上下文管理
 * 使用ThreadLocal存储当前请求的用户信息
 * 
 * <AUTHOR>
 * @date 2024-07-29
 */
public class UserContext {
    
    /**
     * 微信用户信息ThreadLocal
     */
    private static final ThreadLocal<WeChatTokenInfo> WECHAT_USER_CONTEXT = new ThreadLocal<>();
    
    /**
     * 管理员信息ThreadLocal
     */
    private static final ThreadLocal<AdminTokenInfo> ADMIN_USER_CONTEXT = new ThreadLocal<>();
    
    /**
     * 设置当前微信用户信息
     * 
     * @param tokenInfo 微信用户Token信息
     */
    public static void setCurrentWeChatUser(WeChatTokenInfo tokenInfo) {
        WECHAT_USER_CONTEXT.set(tokenInfo);
    }
    
    /**
     * 获取当前微信用户信息
     * 
     * @return 微信用户Token信息
     */
    public static WeChatTokenInfo getCurrentWeChatUser() {
        return WECHAT_USER_CONTEXT.get();
    }
    
    /**
     * 获取当前微信用户ID
     * 
     * @return 用户ID
     */
    public static Long getCurrentWeChatUserId() {
        WeChatTokenInfo tokenInfo = WECHAT_USER_CONTEXT.get();
        return tokenInfo != null ? tokenInfo.getUserId() : null;
    }
    
    /**
     * 获取当前微信用户OpenID
     * 
     * @return OpenID
     */
    public static String getCurrentWeChatOpenId() {
        WeChatTokenInfo tokenInfo = WECHAT_USER_CONTEXT.get();
        return tokenInfo != null ? tokenInfo.getOpenId() : null;
    }
    
    /**
     * 设置当前管理员信息
     * 
     * @param tokenInfo 管理员Token信息
     */
    public static void setCurrentAdmin(AdminTokenInfo tokenInfo) {
        ADMIN_USER_CONTEXT.set(tokenInfo);
    }
    
    /**
     * 获取当前管理员信息
     * 
     * @return 管理员Token信息
     */
    public static AdminTokenInfo getCurrentAdmin() {
        return ADMIN_USER_CONTEXT.get();
    }
    
    /**
     * 获取当前管理员ID
     * 
     * @return 管理员ID
     */
    public static Long getCurrentAdminId() {
        AdminTokenInfo tokenInfo = ADMIN_USER_CONTEXT.get();
        return tokenInfo != null ? tokenInfo.getAdminId() : null;
    }
    
    /**
     * 获取当前管理员用户名
     *
     * @return 用户名
     */
    public static String getCurrentAdminUsername() {
        AdminTokenInfo tokenInfo = ADMIN_USER_CONTEXT.get();
        return tokenInfo != null ? tokenInfo.getUsername() : null;
    }

    /**
     * 获取当前管理员角色
     *
     * @return 管理员角色
     */
    public static Integer getCurrentAdminRole() {
        AdminTokenInfo tokenInfo = ADMIN_USER_CONTEXT.get();
        return tokenInfo != null ? tokenInfo.getRole() : null;
    }

    /**
     * 设置当前管理员ID（简化方法）
     *
     * @param adminId 管理员ID
     */
    public static void setCurrentAdminId(Long adminId) {
        // 这个方法主要用于拦截器，实际使用时建议使用setCurrentAdmin
    }

    /**
     * 设置当前管理员用户名（简化方法）
     *
     * @param username 管理员用户名
     */
    public static void setCurrentAdminUsername(String username) {
        // 这个方法主要用于拦截器，实际使用时建议使用setCurrentAdmin
    }

    /**
     * 设置当前管理员角色（简化方法）
     *
     * @param role 管理员角色
     */
    public static void setCurrentAdminRole(Integer role) {
        // 这个方法主要用于拦截器，实际使用时建议使用setCurrentAdmin
    }
    
    /**
     * 清除当前微信用户信息
     */
    public static void clearWeChatUser() {
        WECHAT_USER_CONTEXT.remove();
    }

    /**
     * 清除微信用户上下文
     */
    public static void clearWeChatContext() {
        WECHAT_USER_CONTEXT.remove();
    }

    /**
     * 清除当前管理员信息
     */
    public static void clearAdmin() {
        ADMIN_USER_CONTEXT.remove();
    }

    /**
     * 清除管理员上下文
     */
    public static void clearAdminContext() {
        ADMIN_USER_CONTEXT.remove();
    }
    
    /**
     * 清除所有用户信息
     */
    public static void clear() {
        WECHAT_USER_CONTEXT.remove();
        ADMIN_USER_CONTEXT.remove();
    }
}
