package com.jj.core.domain.dto.image;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 图片处理响应DTO
 * 
 * <AUTHOR>
 * @date 2024-07-29
 */
@ApiModel(description = "图片处理响应")
public class ImageProcessResDTO {
    
    /**
     * 任务编号
     */
    @ApiModelProperty(value = "任务编号", example = "TASK_20240729_001")
    private String taskNo;
    
    /**
     * 积分消耗
     */
    @ApiModelProperty(value = "积分消耗", example = "5")
    private Integer pointsCost;
    
    /**
     * 预计处理时间（秒）
     */
    @ApiModelProperty(value = "预计处理时间（秒）", example = "30")
    private Integer estimatedTime;
    
    /**
     * 任务状态 1:待处理 2:处理中 3:成功 4:失败 5:取消
     */
    @ApiModelProperty(value = "任务状态", example = "1")
    private Integer status;
    
    /**
     * 任务状态描述
     */
    @ApiModelProperty(value = "任务状态描述", example = "待处理")
    private String statusDesc;
    
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间", example = "2024-07-29 10:30:00")
    private String createTime;
    
    public String getTaskNo() {
        return taskNo;
    }
    
    public void setTaskNo(String taskNo) {
        this.taskNo = taskNo;
    }
    
    public Integer getPointsCost() {
        return pointsCost;
    }
    
    public void setPointsCost(Integer pointsCost) {
        this.pointsCost = pointsCost;
    }
    
    public Integer getEstimatedTime() {
        return estimatedTime;
    }
    
    public void setEstimatedTime(Integer estimatedTime) {
        this.estimatedTime = estimatedTime;
    }
    
    public Integer getStatus() {
        return status;
    }
    
    public void setStatus(Integer status) {
        this.status = status;
    }
    
    public String getStatusDesc() {
        return statusDesc;
    }
    
    public void setStatusDesc(String statusDesc) {
        this.statusDesc = statusDesc;
    }
    
    public String getCreateTime() {
        return createTime;
    }
    
    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }
    
    @Override
    public String toString() {
        return "ImageProcessResDTO{" +
                "taskNo='" + taskNo + '\'' +
                ", pointsCost=" + pointsCost +
                ", estimatedTime=" + estimatedTime +
                ", status=" + status +
                ", statusDesc='" + statusDesc + '\'' +
                ", createTime='" + createTime + '\'' +
                '}';
    }
}
