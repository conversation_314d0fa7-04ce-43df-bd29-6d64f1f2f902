package com.jj.core.domain.dto.image;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * 图片处理请求DTO
 * 
 * <AUTHOR>
 * @date 2024-07-29
 */
@ApiModel(description = "图片处理请求")
public class ImageProcessReqDTO {
    
    /**
     * 处理要求描述
     */
    @ApiModelProperty(value = "处理要求描述", required = true, example = "美化一下这种图片,加上 我爱中国 四个字 尺寸[4:3]")
    @NotBlank(message = "处理要求描述不能为空")
    @Size(max = 1000, message = "处理要求描述不能超过1000个字符")
    private String prompt;
    
    /**
     * 原图片URL
     */
    @ApiModelProperty(value = "原图片URL", example = "https://example.com/image.png")
    @Size(max = 500, message = "图片URL不能超过500个字符")
    private String imageUrl;
    
    /**
     * 原图片base64
     */
    @ApiModelProperty(value = "原图片base64", example = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...")
    private String imageBase64;
    
    /**
     * 图片尺寸比例
     */
    @ApiModelProperty(value = "图片尺寸比例", example = "4:3")
    @Size(max = 20, message = "图片尺寸比例不能超过20个字符")
    private String aspectRatio;
    
    /**
     * 处理类型
     */
    @ApiModelProperty(value = "处理类型", example = "beautify")
    @Size(max = 50, message = "处理类型不能超过50个字符")
    private String processType;
    
    /**
     * 添加的文字内容
     */
    @ApiModelProperty(value = "添加的文字内容", example = "我爱中国")
    @Size(max = 200, message = "文字内容不能超过200个字符")
    private String textContent;
    
    /**
     * 风格类型
     */
    @ApiModelProperty(value = "风格类型", example = "realistic")
    @Size(max = 50, message = "风格类型不能超过50个字符")
    private String style;
    
    public String getPrompt() {
        return prompt;
    }
    
    public void setPrompt(String prompt) {
        this.prompt = prompt;
    }
    
    public String getImageUrl() {
        return imageUrl;
    }
    
    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }
    
    public String getImageBase64() {
        return imageBase64;
    }
    
    public void setImageBase64(String imageBase64) {
        this.imageBase64 = imageBase64;
    }
    
    public String getAspectRatio() {
        return aspectRatio;
    }
    
    public void setAspectRatio(String aspectRatio) {
        this.aspectRatio = aspectRatio;
    }
    
    public String getProcessType() {
        return processType;
    }
    
    public void setProcessType(String processType) {
        this.processType = processType;
    }
    
    public String getTextContent() {
        return textContent;
    }
    
    public void setTextContent(String textContent) {
        this.textContent = textContent;
    }
    
    public String getStyle() {
        return style;
    }
    
    public void setStyle(String style) {
        this.style = style;
    }
    
    @Override
    public String toString() {
        return "ImageProcessReqDTO{" +
                "prompt='" + prompt + '\'' +
                ", imageUrl='" + imageUrl + '\'' +
                ", imageBase64='" + (imageBase64 != null ? "***" : null) + '\'' +
                ", aspectRatio='" + aspectRatio + '\'' +
                ", processType='" + processType + '\'' +
                ", textContent='" + textContent + '\'' +
                ", style='" + style + '\'' +
                '}';
    }
}
