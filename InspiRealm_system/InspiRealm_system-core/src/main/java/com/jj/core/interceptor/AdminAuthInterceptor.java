package com.jj.core.interceptor;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.jj.common.response.JsonResult;
import com.jj.core.annotation.AdminAuth;
import com.jj.core.auth.AdminTokenInfo;
import com.jj.core.auth.TokenManager;
import com.jj.core.auth.UserContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;

/**
 * 管理员权限拦截器
 * 
 * <AUTHOR>
 * @date 2024-07-29
 */
@Component
public class AdminAuthInterceptor implements HandlerInterceptor {
    
    private static final Logger logger = LoggerFactory.getLogger(AdminAuthInterceptor.class);
    
    @Autowired
    private TokenManager tokenManager;
    
    private final ObjectMapper objectMapper = new ObjectMapper();
    
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        // 只处理HandlerMethod
        if (!(handler instanceof HandlerMethod)) {
            return true;
        }
        
        HandlerMethod handlerMethod = (HandlerMethod) handler;
        
        // 检查类级别的@AdminAuth注解
        AdminAuth classAuth = handlerMethod.getBeanType().getAnnotation(AdminAuth.class);
        // 检查方法级别的@AdminAuth注解
        AdminAuth methodAuth = handlerMethod.getMethodAnnotation(AdminAuth.class);
        
        // 如果没有@AdminAuth注解，则不需要验证
        if (classAuth == null && methodAuth == null) {
            return true;
        }
        
        // 方法级别的注解优先级更高
        AdminAuth adminAuth = methodAuth != null ? methodAuth : classAuth;
        
        try {
            // 获取Token
            String token = getTokenFromRequest(request);
            if (token == null || token.trim().isEmpty()) {
                logger.warn("管理员访问接口缺少Token，URI: {}", request.getRequestURI());
                writeErrorResponse(response, "未登录或Token无效");
                return false;
            }
            
            // 验证管理员Token
            AdminTokenInfo adminTokenInfo = tokenManager.validateAdminToken(token);
            if (adminTokenInfo == null) {
                logger.warn("管理员Token验证失败，token: {}, URI: {}", token, request.getRequestURI());
                writeErrorResponse(response, "Token无效或已过期");
                return false;
            }
            
            // 检查是否需要超级管理员权限
            if (adminAuth.requireSuperAdmin() && adminTokenInfo.getRole() != 1) {
                logger.warn("需要超级管理员权限，当前用户角色: {}, adminId: {}, URI: {}", 
                        adminTokenInfo.getRole(), adminTokenInfo.getAdminId(), request.getRequestURI());
                writeErrorResponse(response, "权限不足，需要超级管理员权限");
                return false;
            }
            
            // 设置当前管理员信息到上下文
            UserContext.setCurrentAdmin(adminTokenInfo);
            
            logger.debug("管理员权限验证通过，adminId: {}, username: {}, role: {}, URI: {}", 
                    adminTokenInfo.getAdminId(), adminTokenInfo.getUsername(), adminTokenInfo.getRole(), request.getRequestURI());
            
            return true;
            
        } catch (Exception e) {
            logger.error("管理员权限验证异常，URI: {}", request.getRequestURI(), e);
            writeErrorResponse(response, "权限验证失败");
            return false;
        }
    }
    
    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        // 清理上下文
        UserContext.clearAdmin();
    }
    
    /**
     * 从请求中获取Token
     * 
     * @param request 请求对象
     * @return Token
     */
    private String getTokenFromRequest(HttpServletRequest request) {
        // 优先从Header中获取
        String token = request.getHeader("Authorization");
        if (token != null && token.startsWith("Bearer ")) {
            return token.substring(7);
        }
        
        // 从Token Header中获取
        token = request.getHeader("Token");
        if (token != null && !token.trim().isEmpty()) {
            return token;
        }
        
        // 从参数中获取
        return request.getParameter("token");
    }
    
    /**
     * 写入错误响应
     * 
     * @param response 响应对象
     * @param message 错误消息
     */
    private void writeErrorResponse(HttpServletResponse response, String message) throws IOException {
        response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
        response.setContentType("application/json;charset=UTF-8");
        
        JsonResult<Object> result = JsonResult.error(401, message);
        
        try (PrintWriter writer = response.getWriter()) {
            writer.write(objectMapper.writeValueAsString(result));
            writer.flush();
        }
    }
}
