package com.jj.core.domain.dto.wechat;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 微信用户信息DTO
 * 
 * <AUTHOR>
 * @date 2024-07-29
 */
@ApiModel(description = "微信用户信息")
public class WeChatUserInfoDTO {
    
    /**
     * 用户昵称
     */
    @ApiModelProperty(value = "用户昵称", example = "张三")
    private String nickName;
    
    /**
     * 头像URL
     */
    @ApiModelProperty(value = "头像URL", example = "https://wx.qlogo.cn/mmopen/xxx")
    private String avatarUrl;
    
    /**
     * 性别 0:未知 1:男 2:女
     */
    @ApiModelProperty(value = "性别 0:未知 1:男 2:女", example = "1")
    private Integer gender;
    
    /**
     * 国家
     */
    @ApiModelProperty(value = "国家", example = "中国")
    private String country;
    
    /**
     * 省份
     */
    @ApiModelProperty(value = "省份", example = "广东")
    private String province;
    
    /**
     * 城市
     */
    @ApiModelProperty(value = "城市", example = "深圳")
    private String city;
    
    public String getNickName() {
        return nickName;
    }
    
    public void setNickName(String nickName) {
        this.nickName = nickName;
    }
    
    public String getAvatarUrl() {
        return avatarUrl;
    }
    
    public void setAvatarUrl(String avatarUrl) {
        this.avatarUrl = avatarUrl;
    }
    
    public Integer getGender() {
        return gender;
    }
    
    public void setGender(Integer gender) {
        this.gender = gender;
    }
    
    public String getCountry() {
        return country;
    }
    
    public void setCountry(String country) {
        this.country = country;
    }
    
    public String getProvince() {
        return province;
    }
    
    public void setProvince(String province) {
        this.province = province;
    }
    
    public String getCity() {
        return city;
    }
    
    public void setCity(String city) {
        this.city = city;
    }
    
    @Override
    public String toString() {
        return "WeChatUserInfoDTO{" +
                "nickName='" + nickName + '\'' +
                ", avatarUrl='" + avatarUrl + '\'' +
                ", gender=" + gender +
                ", country='" + country + '\'' +
                ", province='" + province + '\'' +
                ", city='" + city + '\'' +
                '}';
    }
}
