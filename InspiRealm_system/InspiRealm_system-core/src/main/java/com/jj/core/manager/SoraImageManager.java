package com.jj.core.manager;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.jj.common.exception.BizException;
import okhttp3.MediaType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;
import okhttp3.*;
import okhttp3.sse.EventSource;
import okhttp3.sse.EventSourceListener;
import okhttp3.sse.EventSources;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Sora Image API管理器
 * 负责与sora_image模型的API交互
 * 
 * <AUTHOR>
 * @date 2024-07-29
 */
@Component
public class SoraImageManager {
    
    private static final Logger logger = LoggerFactory.getLogger(SoraImageManager.class);
    
    @Value("${ai.sora-image-api-url:}")
    private String soraImageApiUrl;
    
    @Value("${ai.sora-image-api-key:}")
    private String soraImageApiKey;

    private final RestTemplate restTemplate;
    private final ObjectMapper objectMapper = new ObjectMapper();

    // OkHttpClient for streaming requests
    private final OkHttpClient okHttpClient = new OkHttpClient.Builder()
            .connectTimeout(30, TimeUnit.SECONDS)
            .readTimeout(10, TimeUnit.MINUTES)  // 长时间读取超时，适合AI处理
            .writeTimeout(30, TimeUnit.SECONDS)
            .build();

    /**
     * 构造函数，注入专门配置的AI RestTemplate
     */
    public SoraImageManager(@Qualifier("aiRestTemplate") RestTemplate restTemplate) {
        this.restTemplate = restTemplate;
    }
    
    /**
     * 最大重试次数 - 由于AI生成时间长，减少重试次数避免过长等待
     */
    private static final int MAX_RETRY_COUNT = 1;

    /**
     * 重试间隔（毫秒） - 增加重试间隔，给服务器更多恢复时间
     */
    private static final long RETRY_INTERVAL = 5000;
    
    /**
     * 处理图片
     * 
     * @param request 处理请求
     * @return 处理结果
     */
    public SoraImageResult processImage(SoraImageRequest request) {
        logger.info("开始调用Sora Image API，prompt: {}", request.getPrompt());
        
        int retryCount = 0;
        Exception lastException = null;
        
        while (retryCount < MAX_RETRY_COUNT) {
            try {
                // 构建请求
                HttpHeaders headers = new HttpHeaders();
                headers.setContentType(org.springframework.http.MediaType.APPLICATION_JSON);
                headers.setBearerAuth(soraImageApiKey);

                Map<String, Object> requestBody = buildRequestBody(request);
                HttpEntity<Map<String, Object>> httpEntity = new HttpEntity<>(requestBody, headers);
                
                logger.info("调用Sora Image API，重试次数: {}, URL: {}, 预计处理时间: 3-5分钟", retryCount + 1, soraImageApiUrl);
                
                logger.info("=== 准备调用流式请求 ===");
                logger.info("重试次数: {}", retryCount + 1);

                try {
                    // 使用流式请求
                    SoraImageResult result = processImageWithStreaming(requestBody);
                    if (result != null) {
                        if (result.isSuccess()){
                            logger.info("=== 图片处理成功 ===");
                            logger.info("imageUrl: {}", result.getImageUrl());
                            return result;
                        }else{
                            throw new BizException(result.getErrorMessage());
                        }
                    } else {
                        logger.error("=== 流式请求返回null ===");
                        throw new BizException("请求处理失败");
                    }
                } catch (Exception streamingException) {
                    logger.error("=== 流式请求失败，尝试普通请求 ===");
                    logger.error("流式请求错误: {}", streamingException.getMessage());
                    throw streamingException; // 抛出原始的流式请求异常
                }
            } catch (Exception e) {
                // 详细记录错误信息，特别是超时相关的错误
                if (e.getMessage() != null && e.getMessage().contains("Unexpected end of file")) {
                    logger.warn("Sora Image API连接意外断开，重试次数: {}, 这通常是由于处理时间过长导致的网络超时", retryCount);
                } else {
                    logger.error("Sora Image API调用失败，重试次数: {}, 错误类型: {}, 错误信息: {}",
                            retryCount, e.getClass().getSimpleName(), e.getMessage());
                }
                throw e;
            }
        }
        
        logger.error("Sora Image API调用最终失败，已重试{}次", MAX_RETRY_COUNT);
        throw new BizException("图片处理失败，请稍后重试");
    }

    /**
     * 使用普通RestTemplate请求作为备用方案
     */
    private SoraImageResult processImageWithRestTemplate(HttpEntity<Map<String, Object>> httpEntity) {
        try {
            logger.info("=== 使用普通RestTemplate请求 ===");

            ResponseEntity<String> response = restTemplate.exchange(
                    soraImageApiUrl,
                    HttpMethod.POST,
                    httpEntity,
                    String.class
            );

            if (response.getStatusCode() == HttpStatus.OK) {
                String responseBody = response.getBody();
                logger.info("普通请求成功，响应: {}", responseBody);

                SoraImageResult result = parseResponse(responseBody);
                return result;
            } else {
                logger.error("普通请求失败，状态码: {}", response.getStatusCode());
                return null;
            }

        } catch (Exception e) {
            logger.error("普通请求异常: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 使用流式请求处理图片
     *
     * @param requestBody 请求体
     * @return 处理结果
     */
    private SoraImageResult processImageWithStreaming(Map<String, Object> requestBody) {
        logger.info("=== 开始流式请求处理图片 ===");
        logger.info("请求URL: {}", soraImageApiUrl);
        logger.info("请求体: {}", requestBody);
        logger.info("API Key: {}...", soraImageApiKey != null ? soraImageApiKey.substring(0, Math.min(10, soraImageApiKey.length())) : "null");

        try {
            // 构建OkHttp请求
            String jsonBody = objectMapper.writeValueAsString(requestBody);
            logger.info("JSON请求体: {}", jsonBody);

            RequestBody body = RequestBody.create(MediaType.get("application/json"), jsonBody);

            Request request = new Request.Builder()
                    .url(soraImageApiUrl)
                    .post(body)
                    .addHeader("Authorization", "Bearer " + soraImageApiKey)
                    .addHeader("Content-Type", "application/json")
                    .build();

            logger.info("构建的请求: {}", request);
            logger.info("请求头: {}", request.headers());

            // 用于存储最终结果
            AtomicReference<SoraImageResult> resultRef = new AtomicReference<>();
            AtomicReference<Exception> errorRef = new AtomicReference<>();
            CountDownLatch latch = new CountDownLatch(1);

            // 用于累积流式内容
            StringBuilder contentBuilder = new StringBuilder();

            // 使用 OkHttpClient 的异步调用
            logger.info("=== 发起异步HTTP请求 ===");
            okHttpClient.newCall(request).enqueue(new Callback() {
                @Override
                public void onFailure(Call call, IOException e) {
                    logger.error("=== HTTP请求失败 ===");
                    logger.error("错误信息: {}", e.getMessage(), e);
                    errorRef.set(new RuntimeException("HTTP请求失败: " + e.getMessage(), e));
                    latch.countDown();
                }

                @Override
                public void onResponse(Call call, Response response) throws IOException {
                    logger.info("=== 收到HTTP响应 ===");
                        logger.info("响应状态码: {}", response.code());
                    logger.info("响应头: {}", response.headers());

                    if (!response.isSuccessful()) {
                        String errorBody = response.body() != null ? response.body().string() : "无响应体";
                        logger.error("HTTP请求失败，状态码: {}, 响应体: {}", response.code(), errorBody);
                        errorRef.set(new RuntimeException("HTTP请求失败，状态码: " + response.code() + ", 响应: " + errorBody));
                        latch.countDown();
                        return;
                    }

                    try (ResponseBody responseBody = response.body()) {
                        if (responseBody == null) {
                            logger.error("响应体为空");
                            errorRef.set(new RuntimeException("响应体为空"));
                            latch.countDown();
                            return;
                        }

                        // 检查是否是流式响应
                        String contentType = response.header("Content-Type", "");
                        if (contentType.contains("text/event-stream")) {
                            // 处理 Server-Sent Events 流式响应
                            logger.info("处理SSE流式响应");
                            processSSEStream(responseBody, contentBuilder, resultRef, errorRef);
                        } else {
                            // 处理普通响应
                            logger.info("处理普通响应");
                            String responseContent = responseBody.string();
                            logger.info("响应内容: {}", responseContent);
                            if (responseContent.startsWith("data: ")){
                                responseContent.substring("data: ".length() -1);
                            }
                            SoraImageResult result = parseResponse(responseContent);
                            resultRef.set(result);
                        }

                    } catch (Exception e) {
                        logger.error("处理响应时出错: {}", e.getMessage(), e);
                        errorRef.set(e);
                    } finally {
                        latch.countDown();
                    }
                }
            });

            // 等待完成，最多等待10分钟
            logger.info("开始等待流式响应，最多等待10分钟...");
            boolean completed = latch.await(10, TimeUnit.MINUTES);

            logger.info("等待结果: completed = {}", completed);

            if (!completed) {
                logger.warn("流式请求超时");
                throw new BizException("请求超时");
            }

            if (errorRef.get() != null) {
                throw new BizException(errorRef.get().getMessage());
            }

            return resultRef.get();

        } catch (Exception e) {
            logger.error("流式请求处理失败: {}", e.getMessage(), e);
            throw new BizException(e.getMessage());
        }
    }

    public static void main(String[] args) {
        System.out.println("data: {\"id\":\"chatcmpl-89CVcMfjw0ITbiYGoz817kytjH0ZS\",\"object\":\"chat.completion.chunk\",\"created\":1753950409,\"model\":\"sora_image\",\"choices\":[{\"index\":0,\"delta\":{\"role\":\"assistant\",\"content\":\"```\"},\"finish_reason\":null}]}".substring("data: ".length() -1));
    }

    /**
     * 处理 Server-Sent Events 流式响应
     */
    private void processSSEStream(ResponseBody responseBody, StringBuilder contentBuilder,
                                 AtomicReference<SoraImageResult> resultRef, AtomicReference<Exception> errorRef) {
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(responseBody.byteStream(), StandardCharsets.UTF_8))) {
            String line;
            while ((line = reader.readLine()) != null) {
                logger.debug("SSE行: {}", line);

                // 处理 SSE 格式: data: {...}
                if (line.startsWith("data: ")) {
                    String data = line.substring(6); // 去掉 "data: " 前缀

                    // 检查是否是结束标记
                    if ("[DONE]".equals(data.trim())) {
                        logger.info("流式请求完成");

                        // 解析最终内容
                        String finalContent = contentBuilder.toString();
                        if (!finalContent.isEmpty()) {
                            SoraImageResult result = parseStreamingContent(finalContent);
                            resultRef.set(result);
                        }
                        break;
                    }

                    try {
                        // 解析JSON数据
                        JsonNode jsonNode = objectMapper.readTree(data);
                        JsonNode choices = jsonNode.get("choices");

                        if (choices != null && choices.isArray() && choices.size() > 0) {
                            JsonNode delta = choices.get(0).get("delta");
                            if (delta != null && delta.has("content")) {
                                String content = delta.get("content").asText();
                                contentBuilder.append(content);

                                // 解析进度信息
                                parseProgressInfo(content);
                            }
                        }
                    } catch (Exception e) {
                        logger.debug("解析SSE数据时出错: {}", e.getMessage());
                    }
                }
            }
        } catch (Exception e) {
            logger.error("处理SSE流时出错: {}", e.getMessage(), e);
            errorRef.set(e);
        }
    }

    /**
     * 构建请求体
     *
     * @param request 请求参数
     * @return 请求体
     */
    private Map<String, Object> buildRequestBody(SoraImageRequest request) {
        Map<String, Object> requestBody = new HashMap<>();

        // 设置模型
        requestBody.put("model", "sora_image");
        // 构建消息内容
        List<Map<String, Object>> messages = new ArrayList<>();
        Map<String, Object> message = new HashMap<>();
        message.put("role", "user");

        // 构建content数组
        List<Map<String, Object>> content = new ArrayList<>();

        // 添加文本内容
        String textContent = buildTextContent(request);
        Map<String, Object> textPart = new HashMap<>();
        textPart.put("type", "text");
        textPart.put("text", textContent);
        content.add(textPart);

        // 添加图片内容
        if (request.getImageUrl() != null || request.getImageBase64() != null) {
            Map<String, Object> imagePart = new HashMap<>();
            imagePart.put("type", "image_url");

            Map<String, Object> imageUrl = new HashMap<>();
            if (request.getImageUrl() != null) {
                imageUrl.put("url", request.getImageUrl());
            } else if (request.getImageBase64() != null) {
                imageUrl.put("url", request.getImageBase64());
            }

            imagePart.put("image_url", imageUrl);
            content.add(imagePart);
        }

        message.put("content", content);
        messages.add(message);
        requestBody.put("messages", messages);

        // 添加流式请求相关参数
        requestBody.put("stream", true);
        requestBody.put("temperature", 0.5);
        requestBody.put("top_p", 1);
        requestBody.put("presence_penalty", 0);

        return requestBody;
    }

    /**
     * 解析流式内容，提取图片URL
     *
     * @param content 流式内容
     * @return 处理结果
     */
    private SoraImageResult parseStreamingContent(String content) {
        logger.info("解析流式内容: {}", content);

        try {
            SoraImageResult result = new SoraImageResult();
            if (content.contains("图片检测系统认为内容可能违反相关政策")){
                result.setSuccess(false);
                result.setErrorMessage("图片检测系统认为内容可能违反相关政策");
                return result;
            }
            // 查找以https://开头，以图片格式结尾的URL（支持复杂内容中的URL提取）
            String imageUrlPattern = "https://[^\\s\\u4e00-\\u9fff]+\\.(png|jpg|jpeg|gif|bmp|webp|svg)(?:\\?[^\\s\\u4e00-\\u9fff]*)?";
            java.util.regex.Pattern pattern = java.util.regex.Pattern.compile(imageUrlPattern, java.util.regex.Pattern.CASE_INSENSITIVE);
            java.util.regex.Matcher matcher = pattern.matcher(content);

            String imageUrl = null;
            // 找到所有匹配的URL，保留最后一个
            while (matcher.find()) {
                imageUrl = matcher.group(0);
                logger.debug("找到图片URL: {}", imageUrl);
            }

            if (imageUrl != null) {
                logger.info("从流式内容中提取到最终图片URL: {}", imageUrl);


                result.setImageUrl(imageUrl);
                result.setSuccess(true);
                result.setErrorMessage("图片处理成功");

                return result;
            } else {
                logger.warn("未能从流式内容中提取到图片URL");
                throw new BizException("生成失败请稍候重试, 若多次失败请调整提示词或图片");
            }

        } catch (Exception e) {
            logger.error("解析流式内容失败: {}", e.getMessage(), e);
            throw new BizException(e.getMessage());
        }
    }

    /**
     * 解析进度信息
     *
     * @param content 内容片段
     */
    private void parseProgressInfo(String content) {
        try {
            // 解析进度信息：🏃 进度：3.5%
            if (content.contains("进度：")) {
                String progressPattern = "进度：([0-9.]+)%";
                java.util.regex.Pattern pattern = java.util.regex.Pattern.compile(progressPattern);
                java.util.regex.Matcher matcher = pattern.matcher(content);

                if (matcher.find()) {
                    String progressStr = matcher.group(1);
                    double progress = Double.parseDouble(progressStr);
                    logger.info("AI处理进度: {}%", progress);
                }
            }

            // 解析状态信息
            if (content.contains("任务正在队列中")) {
                logger.info("AI任务状态: 正在队列中等待处理");
            } else if (content.contains("开始处理")) {
                logger.info("AI任务状态: 开始处理");
            }

        } catch (Exception e) {
            logger.debug("解析进度信息时出错: {}", e.getMessage());
        }
    }

    /**
     * 构建文本内容
     *
     * @param request 请求参数
     * @return 文本内容
     */
    private String buildTextContent(SoraImageRequest request) {
        StringBuilder textBuilder = new StringBuilder();

        // 基础prompt
        textBuilder.append(request.getPrompt());

        // 添加文字内容
        if (request.getTextContent() != null && !request.getTextContent().trim().isEmpty()) {
            if (!request.getPrompt().contains(request.getTextContent())) {
                textBuilder.append(" 添加文字：").append(request.getTextContent());
            }
        }

        // 添加尺寸要求
        if (request.getAspectRatio() != null && !request.getAspectRatio().trim().isEmpty()) {
            if (!request.getPrompt().contains(request.getAspectRatio())) {
                textBuilder.append(" 尺寸[").append(request.getAspectRatio()).append("]");
            }
        }

        // 添加风格要求
        if (request.getStyle() != null && !request.getStyle().trim().isEmpty()) {
            textBuilder.append(" 风格：").append(request.getStyle());
        }

        // 添加处理类型
        if (request.getProcessType() != null && !request.getProcessType().trim().isEmpty()) {
            textBuilder.append(" 处理类型：").append(request.getProcessType());
        }

        return textBuilder.toString();
    }
    
    /**
     * 解析API响应
     *
     * @param responseBody 响应体
     * @return 解析结果
     */
    private SoraImageResult parseResponse(String responseBody) {
        try {
            JsonNode jsonNode = objectMapper.readTree(responseBody);

            // 检查是否有错误
            if (jsonNode.has("error")) {
                String errorMessage = jsonNode.get("error").asText();
                throw new BizException("Sora Image API返回错误: " + errorMessage);
            }

            // 解析图片URL - 集成parseOpenAIResponse方法的逻辑
            String imageUrl = parseOpenAIResponse(responseBody);
            if (imageUrl == null || imageUrl.trim().isEmpty()) {
                throw new BizException("无法从API响应中解析出图片");
            }

            SoraImageResult result = new SoraImageResult();
            result.setImageUrl(imageUrl);
            result.setSuccess(true);

            // 从响应中解析图片信息（OpenAI响应中通常没有这些信息）
            if (jsonNode.has("width")) {
                result.setWidth(jsonNode.get("width").asInt());
            }
            if (jsonNode.has("height")) {
                result.setHeight(jsonNode.get("height").asInt());
            }
            if (jsonNode.has("format")) {
                result.setFormat(jsonNode.get("format").asText());
            }

            // 如果响应中没有图片信息，尝试从图片URL获取
            if (result.getWidth() == null || result.getHeight() == null || result.getFormat() == null) {
                try {
                    ImageInfo imageInfo = getImageInfoFromUrl(imageUrl);
                    if (imageInfo != null) {
                        if (result.getWidth() == null) {
                            result.setWidth(imageInfo.getWidth());
                        }
                        if (result.getHeight() == null) {
                            result.setHeight(imageInfo.getHeight());
                        }
                        if (result.getFormat() == null) {
                            result.setFormat(imageInfo.getFormat());
                        }
                        logger.info("从图片URL获取到图片信息: {}x{}, 格式: {}",
                                imageInfo.getWidth(), imageInfo.getHeight(), imageInfo.getFormat());
                    }
                } catch (Exception e) {
                    logger.warn("获取图片信息失败，将使用默认值: {}", e.getMessage());
                    // 设置默认值，不影响主流程
                    if (result.getWidth() == null) result.setWidth(1024);
                    if (result.getHeight() == null) result.setHeight(1024);
                    if (result.getFormat() == null) result.setFormat("png");
                }
            }

            return result;

        } catch (Exception e) {
            logger.error("解析Sora Image API响应失败", e);
            throw new BizException("解析图片处理结果失败: " + e.getMessage());
        }
    }

    /**
     * 从 OpenAI API (Chat Completions) 响应内容中解析生成的图片 URL
     */
    private String parseOpenAIResponse(String responseContent) {
        try {
            // 使用 Jackson 解析 JSON
            Map<String, Object> responseMap = objectMapper.readValue(responseContent, Map.class);
            List<Map<String, Object>> choices = (List<Map<String, Object>>) responseMap.get("choices");
            if (choices != null && !choices.isEmpty()) {
                Map<String, Object> firstChoice = choices.get(0);
                Map<String, Object> message = (Map<String, Object>) firstChoice.get("message");
                if (message != null) {
                    String content = (String) message.get("content");
                    if (content != null) {
                        logger.info("解析OpenAI响应内容: {}", content);

                        // 优先查找下载链接 [点击下载](URL)
                        String downloadPattern = "\\[点击下载\\]\\((https://[^)]+)\\)";
                        java.util.regex.Pattern pattern = java.util.regex.Pattern.compile(downloadPattern);
                        java.util.regex.Matcher matcher = pattern.matcher(content);
                        if (matcher.find()) {
                            String downloadUrl = matcher.group(1);
                            logger.info("找到下载链接: {}", downloadUrl);
                            return downloadUrl;
                        }

                        // 备选方案：查找 Markdown 图片格式 ![...](URL)
                        String imagePattern = "!\\[[^\\]]*\\]\\((https://[^)]+)\\)";
                        pattern = java.util.regex.Pattern.compile(imagePattern);
                        matcher = pattern.matcher(content);
                        if (matcher.find()) {
                            String imageUrl = matcher.group(1);
                            logger.info("找到图片链接: {}", imageUrl);
                            return imageUrl;
                        }

                        // 最后备选：查找任何以https://开头的URL
                        String urlPattern = "(https://[^\\s\\)\\]]+)";
                        pattern = java.util.regex.Pattern.compile(urlPattern);
                        matcher = pattern.matcher(content);
                        if (matcher.find()) {
                            String url = matcher.group(1);
                            logger.info("找到URL: {}", url);
                            return url;
                        }

                        // 如果都没找到，抛出异常显示完整内容用于调试
                        logger.error("无法从响应中解析出图片URL，完整内容: {}", content);
                        throw new RuntimeException("无法解析图片URL，响应内容: " + content);
                    }
                }
            }
            logger.warn("无法从 OpenAI 响应中解析出图片 URL: {}", responseContent);
            return null;
        } catch (Exception e) {
            logger.error("解析 OpenAI 响应失败: {}", responseContent, e);
            throw new RuntimeException(e.getMessage());
        }
    }
    


    /**
     * Sora Image请求参数
     */
    public static class SoraImageRequest {

        private String prompt;
        private String imageUrl;
        private String imageBase64;
        private String aspectRatio;
        private String processType;
        private String textContent;
        private String style;

        public String getPrompt() {
            return prompt;
        }

        public void setPrompt(String prompt) {
            this.prompt = prompt;
        }

        public String getImageUrl() {
            return imageUrl;
        }

        public void setImageUrl(String imageUrl) {
            this.imageUrl = imageUrl;
        }

        public String getImageBase64() {
            return imageBase64;
        }

        public void setImageBase64(String imageBase64) {
            this.imageBase64 = imageBase64;
        }

        public String getAspectRatio() {
            return aspectRatio;
        }

        public void setAspectRatio(String aspectRatio) {
            this.aspectRatio = aspectRatio;
        }

        public String getProcessType() {
            return processType;
        }

        public void setProcessType(String processType) {
            this.processType = processType;
        }

        public String getTextContent() {
            return textContent;
        }

        public void setTextContent(String textContent) {
            this.textContent = textContent;
        }

        public String getStyle() {
            return style;
        }

        public void setStyle(String style) {
            this.style = style;
        }

        @Override
        public String toString() {
            return "SoraImageRequest{" +
                    "prompt='" + prompt + '\'' +
                    ", imageUrl='" + imageUrl + '\'' +
                    ", imageBase64='" + (imageBase64 != null ? "***" : null) + '\'' +
                    ", aspectRatio='" + aspectRatio + '\'' +
                    ", processType='" + processType + '\'' +
                    ", textContent='" + textContent + '\'' +
                    ", style='" + style + '\'' +
                    '}';
        }
    }

    /**
     * Sora Image响应结果
     */
    public static class SoraImageResult {

        private boolean success;
        private String imageUrl;
        private Integer width;
        private Integer height;
        private String format;
        private String errorMessage;

        public boolean isSuccess() {
            return success;
        }

        public void setSuccess(boolean success) {
            this.success = success;
        }

        public String getImageUrl() {
            return imageUrl;
        }

        public void setImageUrl(String imageUrl) {
            this.imageUrl = imageUrl;
        }

        public Integer getWidth() {
            return width;
        }

        public void setWidth(Integer width) {
            this.width = width;
        }

        public Integer getHeight() {
            return height;
        }

        public void setHeight(Integer height) {
            this.height = height;
        }

        public String getFormat() {
            return format;
        }

        public void setFormat(String format) {
            this.format = format;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        public void setErrorMessage(String errorMessage) {
            this.errorMessage = errorMessage;
        }

        @Override
        public String toString() {
            return "SoraImageResult{" +
                    "success=" + success +
                    ", imageUrl='" + imageUrl + '\'' +
                    ", width=" + width +
                    ", height=" + height +
                    ", format='" + format + '\'' +
                    ", errorMessage='" + errorMessage + '\'' +
                    '}';
        }
    }

    /**
     * 从图片URL获取图片信息
     *
     * @param imageUrl 图片URL
     * @return 图片信息
     */
    private ImageInfo getImageInfoFromUrl(String imageUrl) {
        try {
            logger.info("开始获取图片信息: {}", imageUrl);

            // 使用RestTemplate获取图片数据
            ResponseEntity<byte[]> response = restTemplate.getForEntity(imageUrl, byte[].class);

            if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
                byte[] imageData = response.getBody();

                // 使用Java内置的ImageIO读取图片信息
                try (java.io.ByteArrayInputStream bis = new java.io.ByteArrayInputStream(imageData)) {
                    java.awt.image.BufferedImage bufferedImage = javax.imageio.ImageIO.read(bis);

                    if (bufferedImage != null) {
                        ImageInfo imageInfo = new ImageInfo();
                        imageInfo.setWidth(bufferedImage.getWidth());
                        imageInfo.setHeight(bufferedImage.getHeight());

                        // 从URL推断格式
                        String format = "png"; // 默认格式
                        if (imageUrl.toLowerCase().contains(".jpg") || imageUrl.toLowerCase().contains(".jpeg")) {
                            format = "jpeg";
                        } else if (imageUrl.toLowerCase().contains(".png")) {
                            format = "png";
                        } else if (imageUrl.toLowerCase().contains(".gif")) {
                            format = "gif";
                        } else if (imageUrl.toLowerCase().contains(".webp")) {
                            format = "webp";
                        }
                        imageInfo.setFormat(format);

                        logger.info("成功获取图片信息: {}x{}, 格式: {}, 大小: {} bytes",
                                imageInfo.getWidth(), imageInfo.getHeight(), imageInfo.getFormat(), imageData.length);

                        return imageInfo;
                    }
                }
            }

            logger.warn("无法获取图片信息，响应状态: {}", response.getStatusCode());
            return null;

        } catch (Exception e) {
            logger.error("获取图片信息失败: {}", imageUrl, e);
            return null;
        }
    }

    /**
     * 图片信息内部类
     */
    private static class ImageInfo {
        private Integer width;
        private Integer height;
        private String format;

        public Integer getWidth() {
            return width;
        }

        public void setWidth(Integer width) {
            this.width = width;
        }

        public Integer getHeight() {
            return height;
        }

        public void setHeight(Integer height) {
            this.height = height;
        }

        public String getFormat() {
            return format;
        }

        public void setFormat(String format) {
            this.format = format;
        }
    }
}
