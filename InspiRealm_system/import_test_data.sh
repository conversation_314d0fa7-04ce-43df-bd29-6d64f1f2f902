#!/bin/bash

# InspiRealm测试数据导入脚本
# 使用方法：./import_test_data.sh [数据库名] [用户名] [密码] [主机]

# 默认参数
DB_NAME=${1:-"inspirealm_system"}
DB_USER=${2:-"root"}
DB_PASSWORD=${3:-""}
DB_HOST=${4:-"localhost"}

echo "========================================="
echo "InspiRealm 测试数据导入工具"
echo "========================================="
echo "数据库: $DB_NAME"
echo "用户名: $DB_USER"
echo "主机: $DB_HOST"
echo "========================================="

# 检查MySQL是否可用
if ! command -v mysql &> /dev/null; then
    echo "错误: 未找到MySQL客户端，请先安装MySQL"
    exit 1
fi

# 检查数据库文件是否存在
if [ ! -f "database.sql" ]; then
    echo "错误: 未找到database.sql文件"
    exit 1
fi

if [ ! -f "test_data.sql" ]; then
    echo "错误: 未找到test_data.sql文件"
    exit 1
fi

# 构建MySQL连接参数
MYSQL_CMD="mysql -h$DB_HOST -u$DB_USER"
if [ -n "$DB_PASSWORD" ]; then
    MYSQL_CMD="$MYSQL_CMD -p$DB_PASSWORD"
fi

echo "正在创建数据库和表结构..."
$MYSQL_CMD < database.sql

if [ $? -eq 0 ]; then
    echo "✅ 数据库表结构创建成功"
else
    echo "❌ 数据库表结构创建失败"
    exit 1
fi

echo "正在导入测试数据..."
$MYSQL_CMD $DB_NAME < test_data.sql

if [ $? -eq 0 ]; then
    echo "✅ 测试数据导入成功"
else
    echo "❌ 测试数据导入失败"
    exit 1
fi

echo "========================================="
echo "🎉 测试数据导入完成！"
echo "========================================="
echo "管理员账号信息："
echo "  超级管理员: admin / admin123"
echo "  普通管理员: manager / admin123"
echo "  客服专员: support / admin123"
echo ""
echo "测试数据统计："
echo "  👥 用户数量: 10个"
echo "  🖼️  图片数量: 7张"
echo "  📋 任务数量: 12个"
echo "  💰 积分记录: 27条"
echo "  ❤️  点赞记录: 31条"
echo ""
echo "现在可以启动应用进行测试了！"
echo "========================================="
