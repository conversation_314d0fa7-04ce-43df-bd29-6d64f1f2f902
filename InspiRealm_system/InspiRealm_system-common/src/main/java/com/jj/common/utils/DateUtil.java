package com.jj.common.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * 日期工具
 */
@Slf4j
public class DateUtil {

    /**
     * 常用变量
     */
    public static final String DATE_FORMAT_FULL = "yyyy-MM-dd HH:mm:ss";
    public static final String DATE_FORMAT_YMD = "yyyy-MM-dd";
    public static final String DATE_FORMAT_HMS = "HH:mm:ss";
    public static final String DATE_FORMAT_HM = "HH:mm";
    public static final String DATE_FORMAT_Y = "yyyy";
    public static final String DATE_FORMAT_YM = "yyyy-MM";
    public static final String DATE_FORMAT_M = "MM";
    public static final String DATE_FORMAT_YMDHM = "yyyy-MM-dd HH:mm";
    public static final String DATE_FORMAT_YMDHMS = "yyyyMMddHHmmss";
    public static final long ONE_DAY_MILLS = 3600000 * 24;
    public static final int WEEK_DAYS = 7;
    private static final int dateLength = DATE_FORMAT_YMDHM.length();

    // 月转化为大写
    public static String monthToUppder(int month) {
        if (month < 10) {
            return numToUpper(month);
        } else if (month == 10) {
            return "十";
        } else {
            return "十" + numToUpper(month - 10);
        }
    }

    // 将数字转化为大写（字体格式可自己定义）
    public static String numToUpper(int num) {
        // String u[] = {"零","壹","贰","叁","肆","伍","陆","柒","捌","玖"};
        String u[] = {"零", "一", "二", "三", "四", "五", "六", "七", "八", "九"};
        char[] str = String.valueOf(num).toCharArray();
        String rstr = "";
        for (int i = 0; i < str.length; i++) {
            rstr = rstr + u[Integer.parseInt(str[i] + "")];
        }
        return rstr;
    }

    /**
     * 获取当前系统时间最近12月的年月（含当月）
     */
    public static String[] getLast12Months(Date timeDay) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
        String time = sdf.format(timeDay);
        // 处理月份输入条件
        if (time.length() == 7) {
            time = time + "-01 00:00:00";
        } else if (time.length() == 110) {
            time = time.substring(0, 7) + "-01 00:00:00";
        }
        Date date = new Date();
        SimpleDateFormat sdp = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");
        try {
            date = sdp.parse(time);
        } catch (Exception e) {
            return null;
        }
        String[] last12Months = new String[12];
        Calendar cal = Calendar.getInstance();
        // 设置输入条件时间
        cal.setTime(date);
        // 要先+1,才能把本月的算进去
        cal.set(Calendar.MONTH, cal.get(Calendar.MONTH) + 1);
        for (int i = 0; i < 12; i++) {
            // 逐次往前推1个月
            cal.set(Calendar.MONTH, cal.get(Calendar.MONTH) - 1);
            last12Months[11 - i] =
                cal.get(Calendar.YEAR) + "-" + addZeroForNum(String.valueOf(cal.get(Calendar.MONTH) + 1), 2);
        }
        return last12Months;
    }

    public static String addZeroForNum(String str, int strLength) {
        int strLen = str.length();
        if (strLen < strLength) {
            while (strLen < strLength) {
                StringBuffer sb = new StringBuffer();
                // 左补0
                sb.append("0").append(str);
                // sb.append(str).append("0");//右补0
                str = sb.toString();
                strLen = str.length();
            }
        }
        return str;
    }

    /**
     * 日期修改天数，减一
     *
     * @param str
     * @param count
     * @return java.lang.String
     * @throws <AUTHOR>             whh
     * @date 2020/7/29 18:10
     */
    public static String changeDateByday(String str, Integer count) {

        Calendar calendar = Calendar.getInstance(); // 创建Calendar 的实例

        try {
            // string转换date
            Date date = formatStringToDate(str, DATE_FORMAT_FULL);
            calendar.setTime(date);
            calendar.add(Calendar.DAY_OF_MONTH, count); // 当前时间减去一天，即一天前的时间
            // date转换string丢出
            Date time = calendar.getTime();
            return formatDateToString(time, DATE_FORMAT_FULL);
        } catch (Exception e) {
            log.error("", e);
        }

        return null;
    }

    /**
     * 日期修改天数，加一
     *
     * @param str
     * @param count
     * @return java.lang.String
     * @throws <AUTHOR>             yanyihao
     * @date 2022/3/8 18:10
     */
    public static String changeDateBydayAdd(String str, Integer count) {
        DateFormat df = new SimpleDateFormat("yyyy-MM-dd");
        Calendar calendar = Calendar.getInstance(); // 创建Calendar 的实例

        try {
            calendar.setTime(df.parse(str));
            calendar.add(Calendar.DAY_OF_MONTH, +count); // 当前月份加上一天
            // date转换string丢出
            Date time = calendar.getTime();
            return formatDateToString(time, DATE_FORMAT_FULL);
        } catch (Exception e) {
            log.error("", e);
        }
        return null;
    }

    /**
     * 日期修改月份，加一
     *
     * @param str
     * @param count
     * @return java.lang.String
     * @throws <AUTHOR>             yanyihao
     * @date 2022/3/1 18:10
     */
    public static String changeDateByMonth(String str, Integer count) {
        DateFormat df = new SimpleDateFormat("yyyy-MM");
        Calendar calendar = Calendar.getInstance(); // 创建Calendar 的实例

        try {
            calendar.setTime(df.parse(str));
            calendar.add(Calendar.MONTH, +count); // 当前月份加上一月
            // date转换string丢出
            Date time = calendar.getTime();
            return formatDateToString(time, DATE_FORMAT_YM);
        } catch (Exception e) {
            log.error("", e);
        }
        return null;
    }

    /**
     * 日期转换为制定格式字符串
     *
     * @param time
     * @param format
     * @return
     */
    public static String formatDateToString(Date time, String format) {
        if (time == null) {
            return null;
        }
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        return sdf.format(time);
    }

    /**
     * 获取两个日期之间的所有日期(时间格式, 按天计算)
     *
     * @param startTime
     *            Date 开始时间 yyyy-MM-dd HH:mm:ss
     * @param endTime
     *            Date 结束时间 yyyy-MM-dd HH:mm:ss
     * @return
     */
    public static List<Date> getBetweenDays(Date startTime, Date endTime) {
        List<Date> result = null;
        if (startTime == null || endTime == null) {
            return null;
        }
        result = new ArrayList<Date>();

        Calendar tempStart = Calendar.getInstance();
        tempStart.setTime(startTime);

        tempStart.add(Calendar.DAY_OF_YEAR, 1);
        SimpleDateFormat sdf = new SimpleDateFormat(DATE_FORMAT_FULL);
        Calendar tempEnd = Calendar.getInstance();
        tempEnd.setTime(endTime);
        if (startTime.before(endTime)) {
            result.add(startTime);
        }
        while (tempStart.before(tempEnd)) {
            result.add(tempStart.getTime());
            tempStart.add(Calendar.DAY_OF_YEAR, 1);
        }
        return result;
    }

    /**
     * 获取两个日期之间的所有日期(字符串格式, 按天计算)
     *
     * @param startTime
     *            String 开始时间 yyyy-MM-dd HH:mm:ss
     * @param endTime
     *            String 结束时间 yyyy-MM-dd HH:mm:ss
     * @return
     */
    public static List<Date> getBetweenDays(String startTime, String endTime) {
        List<Date> result = null;
        try {
            if (StringUtils.isEmpty(startTime) || StringUtils.isEmpty(endTime)) {
                return null;
            }
            // 1、定义转换格式
            SimpleDateFormat df = new SimpleDateFormat(DATE_FORMAT_FULL);

            Date start = df.parse(startTime);
            Date end = df.parse(endTime);
            result = new ArrayList<Date>();

            Calendar tempStart = Calendar.getInstance();
            tempStart.setTime(start);

            tempStart.add(Calendar.DAY_OF_YEAR, 1);
            SimpleDateFormat sdf = new SimpleDateFormat(DATE_FORMAT_FULL);
            Calendar tempEnd = Calendar.getInstance();
            tempEnd.setTime(end);
            result.add(start);
            while (tempStart.before(tempEnd)) {
                result.add(tempStart.getTime());
                tempStart.add(Calendar.DAY_OF_YEAR, 1);
            }
        } catch (ParseException e) {
            log.error("", e);
        }
        return result;
    }

    /**
     * 字符串转换为制定格式日期 (注意：当你输入的日期是2014-12-21 12:12，format对应的应为yyyy-MM-dd HH:mm 否则异常抛出)
     *
     * @param date
     * @param format
     * @return
     * @throws ParseException
     * @throws ParseException @ @
     */
    public static Date formatStringToDate(String date, String format) {
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        try {
            return sdf.parse(date);
        } catch (ParseException ex) {
            ex.printStackTrace();
            throw new RuntimeException(ex.toString());
        }
    }

    /**
     * 转换日期格式
     *
     * @param date
     * @param format
     * @return
     */
    public static Date formatDate(Date date, String format) {
        String formatDate = DateUtil.formatDateToString(date, DateUtil.DATE_FORMAT_FULL);
        Date newDate = formatStringToDate(formatDate, format);
        return newDate;
    }

    /**
     * 判断一个日期是否属于两个时段内
     *
     * @param time
     * @param timeRange
     * @return
     */
    public static boolean isTimeInRange(Date time, Date[] timeRange) {
        return (!time.before(timeRange[0]) && !time.after(timeRange[1]));
    }

    /**
     * 从完整的时间截取精确到分的时间
     *
     * @param fullDateStr
     * @return
     */
    public static String getDateToMinute(String fullDateStr) {
        return fullDateStr == null ? null
            : (fullDateStr.length() >= dateLength ? fullDateStr.substring(0, dateLength) : fullDateStr);
    }

    /**
     * 返回指定年度的所有周。List中包含的是String[2]对象 string[0]本周的开始日期,string[1]是本周的结束日期。 日期的格式为YYYY-MM-DD 每年的第一个周，必须包含星期一且是完整的七天。
     * 例如：2009年的第一个周开始日期为2009-01-05，结束日期为2009-01-11。 星期一在哪一年，那么包含这个星期的周就是哪一年的周。
     * 例如：2008-12-29是星期一，2009-01-04是星期日，哪么这个周就是2008年度的最后一个周。
     *
     * @param year
     *            格式 YYYY ，必须大于1900年度 小于9999年
     * @return @
     */
    public static List<String[]> getWeeksByYear(final int year) {
        int weeks = getWeekNumOfYear(year);
        List<String[]> result = new ArrayList<String[]>(weeks);
        int start = 1;
        int end = 7;
        for (int i = 1; i <= weeks; i++) {
            String[] tempWeek = new String[2];
            tempWeek[0] = getDateForDayOfWeek(year, i, start);
            tempWeek[1] = getDateForDayOfWeek(year, i, end);
            result.add(tempWeek);
        }
        return result;
    }

    /**
     * 计算指定年、周的上一年、周
     *
     * @param year
     * @param week
     * @return @
     */
    public static int[] getLastYearWeek(int year, int week) {
        if (week <= 0) {
            throw new IllegalArgumentException("周序号不能小于1！！");
        }
        int[] result = {week, year};
        if (week == 1) {
            // 上一年
            result[1] -= 1;
            // 最后一周
            result[0] = getWeekNumOfYear(result[1]);
        } else {
            result[0] -= 1;
        }
        return result;
    }

    /**
     * 下一个[周，年]
     *
     * @param year
     * @param week
     * @return @
     */
    public static int[] getNextYearWeek(int year, int week) {
        if (week <= 0) {
            throw new IllegalArgumentException("周序号不能小于1！！");
        }
        int[] result = {week, year};
        int weeks = getWeekNumOfYear(year);
        if (week == weeks) {
            // 下一年
            result[1] += 1;
            // 第一周
            result[0] = 1;
        } else {
            result[0] += 1;
        }
        return result;
    }

    /**
     * 计算指定年度共有多少个周。(从周一开始)
     *
     * @param year
     * @return @
     */
    public static int getWeekNumOfYear(final int year) {
        return getWeekNumOfYear(year, Calendar.MONDAY);
    }

    /**
     * 计算指定年度共有多少个周。
     *
     * @param year
     *            yyyy
     * @return @
     */
    public static int getWeekNumOfYear(final int year, int firstDayOfWeek) {
        // 每年至少有52个周 ，最多有53个周。
        int minWeeks = 52;
        int maxWeeks = 53;
        int result = minWeeks;
        int sIndex = 4;
        String date = getDateForDayOfWeek(year, maxWeeks, firstDayOfWeek);
        // 判断年度是否相符，如果相符说明有53个周。
        if (date.substring(0, sIndex).equals(year)) {
            result = maxWeeks;
        }
        return result;
    }

    public static int getWeeksOfWeekYear(final int year) {
        Calendar cal = Calendar.getInstance();
        cal.setFirstDayOfWeek(Calendar.MONDAY);
        cal.setMinimalDaysInFirstWeek(WEEK_DAYS);
        cal.set(Calendar.YEAR, year);
        return cal.getWeeksInWeekYear();
    }

    /**
     * 获取指定年份的第几周的第几天对应的日期yyyy-MM-dd(从周一开始)
     *
     * @param year
     * @param weekOfYear
     * @param dayOfWeek
     * @return yyyy-MM-dd 格式的日期 @
     */
    public static String getDateForDayOfWeek(int year, int weekOfYear, int dayOfWeek) {
        return getDateForDayOfWeek(year, weekOfYear, dayOfWeek, Calendar.MONDAY);
    }

    /**
     * 获取指定年份的第几周的第几天对应的日期yyyy-MM-dd，指定周几算一周的第一天（firstDayOfWeek）
     *
     * @param year
     * @param weekOfYear
     * @param dayOfWeek
     * @param firstDayOfWeek
     *            指定周几算一周的第一天
     * @return yyyy-MM-dd 格式的日期
     */
    public static String getDateForDayOfWeek(int year, int weekOfYear, int dayOfWeek, int firstDayOfWeek) {
        Calendar cal = Calendar.getInstance();
        cal.setFirstDayOfWeek(firstDayOfWeek);
        cal.set(Calendar.DAY_OF_WEEK, dayOfWeek);
        cal.setMinimalDaysInFirstWeek(WEEK_DAYS);
        cal.set(Calendar.YEAR, year);
        cal.set(Calendar.WEEK_OF_YEAR, weekOfYear);
        return formatDateToString(cal.getTime(), DATE_FORMAT_YMD);
    }

    /**
     * 获取指定日期星期几
     *
     * @param datetime
     * @throws ParseException
     * @throws ParseException @ @
     */
    public static int getWeekOfDate(String datetime) throws ParseException {
        Calendar cal = Calendar.getInstance();
        cal.setFirstDayOfWeek(Calendar.MONDAY);
        cal.setMinimalDaysInFirstWeek(WEEK_DAYS);
        Date date = formatStringToDate(datetime, DATE_FORMAT_YMD);
        cal.setTime(date);
        return cal.get(Calendar.DAY_OF_WEEK);

    }

    /**
     * 计算某年某周内的所有日期(从周一开始 为每周的第一天)
     *
     * @param yearNum
     * @param weekNum
     * @return @
     */
    public static List<String> getWeekDays(int yearNum, int weekNum) {
        return getWeekDays(yearNum, weekNum, Calendar.MONDAY);
    }

    /**
     * 计算某年某周内的所有日期(七天)
     *
     * @return yyyy-MM-dd 格式的日期列表
     */
    public static List<String> getWeekDays(int year, int weekOfYear, int firstDayOfWeek) {
        List<String> dates = new ArrayList<String>();
        int dayOfWeek = firstDayOfWeek;
        for (int i = 0; i < WEEK_DAYS; i++) {
            dates.add(getDateForDayOfWeek(year, weekOfYear, dayOfWeek++, firstDayOfWeek));
        }
        return dates;
    }

    /**
     * 获取目标日期的上周、或本周、或下周的年、周信息
     *
     * @param queryDate
     *            传入的时间
     * @param weekOffset
     *            -1:上周 0:本周 1:下周
     * @param firstDayOfWeek
     *            每周以第几天为首日
     * @return
     * @throws ParseException
     */
    public static int[] getWeekAndYear(String queryDate, int weekOffset, int firstDayOfWeek) throws ParseException {

        Date date = formatStringToDate(queryDate, DATE_FORMAT_YMD);
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.setFirstDayOfWeek(firstDayOfWeek);
        calendar.setMinimalDaysInFirstWeek(WEEK_DAYS);
        int year = calendar.getWeekYear();
        int week = calendar.get(Calendar.WEEK_OF_YEAR);
        int[] result = {week, year};
        switch (weekOffset) {
            case 1:
                result = getNextYearWeek(year, week);
                break;
            case -1:
                result = getLastYearWeek(year, week);
                break;
            default:
                break;
        }
        return result;
    }

    /**
     * 计算个两日期的天数
     *
     * @param startDate
     *            开始日期字串
     * @param endDate
     *            结束日期字串
     * @return
     * @throws ParseException
     */
    public static int getDaysBetween(String startDate, String endDate) throws ParseException {
        int dayGap = 0;
        if (startDate != null && startDate.length() > 0 && endDate != null && endDate.length() > 0) {
            Date end = formatStringToDate(endDate, DATE_FORMAT_YMD);
            Date start = formatStringToDate(startDate, DATE_FORMAT_YMD);
            dayGap = getDaysBetween(start, end);
        }
        return dayGap;
    }

    public static int getDaysBetween(Date startDate, Date endDate) {
        return (int)((endDate.getTime() - startDate.getTime()) / ONE_DAY_MILLS);
    }

    /**
     * 计算两个日期之间的天数差
     *
     * @param startDate
     * @param endDate
     * @return
     */
    public static int getDaysGapOfDates(Date startDate, Date endDate) {
        int date = 0;
        if (startDate != null && endDate != null) {
            date = getDaysBetween(startDate, endDate);
        }
        return date;
    }

    /**
     * 计算两个日期之间的年份差距
     *
     * @param firstDate
     * @param secondDate
     * @return
     */

    public static int getYearGapOfDates(Date firstDate, Date secondDate) {
        if (firstDate == null || secondDate == null) {
            return 0;
        }
        Calendar helpCalendar = Calendar.getInstance();
        helpCalendar.setTime(firstDate);
        int firstYear = helpCalendar.get(Calendar.YEAR);
        helpCalendar.setTime(secondDate);
        int secondYear = helpCalendar.get(Calendar.YEAR);
        return secondYear - firstYear;
    }

    /**
     * 计算两个日期之间的月份差距
     *
     * @param firstDate
     * @param secondDate
     * @return
     */
    public static int getMonthGapOfDates(Date firstDate, Date secondDate) {
        if (firstDate == null || secondDate == null) {
            return 0;
        }

        return (int)((secondDate.getTime() - firstDate.getTime()) / ONE_DAY_MILLS / 30);

    }

    /**
     * 计算是否包含当前日期
     *
     * @return
     */
    public static boolean isContainCurrent(List<String> dates) {
        boolean flag = false;
        SimpleDateFormat fmt = new SimpleDateFormat(DATE_FORMAT_YMD);
        Date date = new Date();
        String dateStr = fmt.format(date);
        for (int i = 0; i < dates.size(); i++) {
            if (dateStr.equals(dates.get(i))) {
                flag = true;
            }
        }
        return flag;
    }

    /**
     * 从date开始计算time天后的日期
     *
     * @param time
     * @return
     * @throws ParseException
     */
    public static String getCalculateDateToString(String startDate, int time) throws ParseException {
        String resultDate = null;
        if (startDate != null && startDate.length() > 0) {
            Date date = formatStringToDate(startDate, DATE_FORMAT_YMD);
            Calendar c = Calendar.getInstance();
            c.setTime(date);
            c.set(Calendar.DAY_OF_YEAR, time);
            date = c.getTime();
            resultDate = formatDateToString(date, DATE_FORMAT_YMD);
        }
        return resultDate;
    }

    /**
     * 获取从某日期开始计算，指定的日期所在该年的第几周
     *
     * @param date
     * @param admitDate
     * @return
     * @throws ParseException
     * @throws ParseException @ @
     */
    public static int[] getYearAndWeeks(String date, String admitDate) throws ParseException {
        Calendar c = Calendar.getInstance();
        c.setTime(formatStringToDate(admitDate, DATE_FORMAT_YMD));
        int time = c.get(Calendar.DAY_OF_WEEK);
        return getWeekAndYear(date, 0, time);
    }

    /**
     * 获取指定日期refDate，前或后一周的所有日期
     *
     * @param refDate
     *            参考日期
     * @param weekOffset
     *            -1:上周 0:本周 1:下周
     * @param startDate
     *            哪天算一周的第一天
     * @return yyyy-MM-dd 格式的日期
     * @throws ParseException
     * @throws ParseException @ @
     */
    public static List<String> getWeekDaysAroundDate(String refDate, int weekOffset, String startDate)
        throws ParseException {
        // 以startDate为一周的第一天
        Calendar c = Calendar.getInstance();
        c.setTime(formatStringToDate(startDate, DATE_FORMAT_YMD));
        int firstDayOfWeek = c.get(Calendar.DAY_OF_WEEK);
        // 获取相应周
        int[] weekAndYear = getWeekAndYear(refDate, weekOffset, firstDayOfWeek);
        // 获取相应周的所有日期
        return getWeekDays(weekAndYear[1], weekAndYear[0], firstDayOfWeek);
    }

    /**
     * 根据时间点获取时间区间
     *
     * @param hours
     * @return
     */
    public static List<String[]> getTimePointsByHour(int[] hours) {
        List<String[]> hourPoints = new ArrayList<String[]>();
        String sbStart = ":00:00";
        String sbEnd = ":59:59";
        for (int i = 0; i < hours.length; i++) {
            String[] times = new String[2];
            times[0] = hours[i] + sbStart;
            times[1] = (hours[(i + 1 + hours.length) % hours.length] - 1) + sbEnd;
            hourPoints.add(times);
        }
        return hourPoints;
    }

    /**
     * 根据指定的日期，增加或者减少天数
     *
     * @param date
     * @param amount
     * @return
     */
    public static Date addDays(Date date, int amount) {
        return add(date, Calendar.DAY_OF_MONTH, amount);
    }

    /**
     * 根据指定的日期，类型，增加或减少数量
     *
     * @param date
     * @param calendarField
     * @param amount
     * @return
     */
    public static Date add(Date date, int calendarField, int amount) {
        if (date == null) {
            throw new IllegalArgumentException("The date must not be null");
        }
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        c.add(calendarField, amount);
        return c.getTime();
    }

    /**
     * 获取当前日期的最大日期 时间2014-12-21 23:59:59
     *
     * @return
     */
    public static Date getCurDateWithMaxTime() {
        Calendar c = Calendar.getInstance();
        c.set(Calendar.HOUR_OF_DAY, 23);
        c.set(Calendar.MINUTE, 59);
        c.set(Calendar.SECOND, 59);
        return c.getTime();
    }

    /**
     * 获取当前日期的最小日期时间 2014-12-21 00:00:00
     *
     * @return
     */
    public static Date getCurDateWithMinTime() {
        Calendar c = Calendar.getInstance();
        c.set(Calendar.HOUR_OF_DAY, 0);
        c.set(Calendar.MINUTE, 0);
        c.set(Calendar.SECOND, 0);
        c.set(Calendar.MILLISECOND, 0);
        return c.getTime();
    }

    /**
     * 获取任意日期的最大日期 时间2014-12-21 23:59:59
     *
     * @return
     */
    public static Date getDateWithMaxTime(Date date) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        c.set(Calendar.HOUR_OF_DAY, 23);
        c.set(Calendar.MINUTE, 59);
        c.set(Calendar.SECOND, 59);
        return c.getTime();
    }

    /*
     * 将时间转换为时间戳
     */
    public static long dateToStamp(String s) throws ParseException {
        String res;
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date date = simpleDateFormat.parse(s);
        long ts = date.getTime();
        res = String.valueOf(ts);
        return ts;
    }

    /*
     * 将时间戳转换为时间
     */
    public static Date stampToDate(String s) {
        String res;
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        long lt = new Long(s);
        Date date = new Date(lt);
        res = simpleDateFormat.format(date);
        return date;
    }

    /**
     * 获取任意日期的最小日期时间 2014-12-21 00:00:00
     *
     * @return
     */
    public static Date getDateWithMinTime(Date date) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        c.set(Calendar.HOUR_OF_DAY, 0);
        c.set(Calendar.MINUTE, 0);
        c.set(Calendar.SECOND, 0);
        c.set(Calendar.MILLISECOND, 0);
        return c.getTime();
    }



    /**
     * 日期修改天数，减一
     *
     * @param date
     * @param count
     * @return java.lang.String
     * @throws <AUTHOR>             whh
     * @date 2020/7/29 18:10
     */
    public static Date changeDateByday(Date date, Integer count) {

        Calendar calendar = Calendar.getInstance(); // 创建Calendar 的实例

        try {
            calendar.setTime(date);
            calendar.add(Calendar.DAY_OF_MONTH, count); // 当前时间减去一天，即一天前的时间
            // date转换string丢出
            Date time = calendar.getTime();
            return time;
        } catch (Exception e) {
            log.error("", e);
        }

        return null;
    }

    public static Date transferFormat(Date date) {
        String formatDate = DateUtil.formatDateToString(date, DateUtil.DATE_FORMAT_YMD);
        Date newDate = formatStringToDate(formatDate, DateUtil.DATE_FORMAT_YMD);
        return newDate;
    }

    /**
     * @MethodName compareDate
     * @Description 比较两个时间大小如果返回1说明时间1比时间2大，返回-1是小于，返回0说明等于
     * @param: dateTimeOne
     * @param: dateTimeTwo
     * @Return int
     * @Throws
     * <AUTHOR>
     * @Date 2020/4/22 2:24 下午
     */
    public static int compareDate(Date dateOne, Date dateTwo) {

        try {
            if (dateOne.getTime() > dateTwo.getTime()) {
                return 1;
            } else if (dateOne.getTime() < dateTwo.getTime()) {
                return -1;
            } else {
                return 0;
            }
        } catch (Exception exception) {
            exception.printStackTrace();
        }
        return 0;
    }

    /**
     * @MethodName compareDateString
     * @Description 比较两个日期大小如果返回1说明时间1比时间2大，返回-1是小于，返回0说明等于
     * @param: dateTimeOne
     * @param: dateTimeTwo
     * @Return int
     * @Throws
     * <AUTHOR>
     * @Date 2020/4/22 2:23 下午
     */
    public static int compareDateString(String dateTimeOne, String dateTimeTwo) {

        DateFormat df = new SimpleDateFormat("yyyy-MM-dd");
        try {
            Date dateOne = df.parse(dateTimeOne);
            Date dateTwo = df.parse(dateTimeTwo);
            if (dateOne.getTime() > dateTwo.getTime()) {
                return 1;
            } else if (dateOne.getTime() < dateTwo.getTime()) {
                return -1;
            } else {
                return 0;
            }
        } catch (Exception exception) {
            exception.printStackTrace();
        }
        return 0;
    }

    /**
     * @MethodName compareDateTimeString
     * @Description 比较两个时间大小如果返回1说明时间1比时间2大，返回-1是小于，返回0说明等于
     * @param: dateTimeOne
     * @param: dateTimeTwo
     * @Return int
     * @Throws
     * <AUTHOR>
     * @Date 2020/4/22 2:24 下午
     */
    public static int compareDateTimeString(String dateTimeOne, String dateTimeTwo) {

        DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            Date dateOne = df.parse(dateTimeOne);
            Date dateTwo = df.parse(dateTimeTwo);
            if (dateOne.getTime() > dateTwo.getTime()) {
                return 1;
            } else if (dateOne.getTime() < dateTwo.getTime()) {
                return -1;
            } else {
                return 0;
            }
        } catch (Exception exception) {
            exception.printStackTrace();
        }
        return 0;
    }

    public static int getYear(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return calendar.get(Calendar.YEAR);
    }

    public static int getMonth(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return calendar.get(Calendar.MONTH) + 1;
    }

    public static int getDay(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return calendar.get(Calendar.DAY_OF_MONTH);
    }

    public static int getHour(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return calendar.get(Calendar.HOUR_OF_DAY);
    }

    public static List<String> doDateByStatisticsType(int cycleNum, String startTime, String endTime) {

        Date endParse = null;
        try {
            endParse = new SimpleDateFormat(DateUtil.DATE_FORMAT_FULL).parse(endTime);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        Long endLong = endParse.getTime();

        SimpleDateFormat sdf = new SimpleDateFormat(DateUtil.DATE_FORMAT_FULL);// 这个是你要转成后的时间的格式
        endTime = sdf.format(new Date(endLong));

        int cycleForNum = 0;
        Date startParse = null;
        try {
            int daysBetweenNum = daysBetween(startTime, endTime);
            cycleForNum =
                daysBetweenNum % cycleNum == 0 ? (daysBetweenNum / cycleNum) : (daysBetweenNum / cycleNum) + 1;

            startParse = new SimpleDateFormat(DateUtil.DATE_FORMAT_FULL).parse(startTime);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        Long startLong = startParse.getTime();

        Long calculationLong = startLong;

        List<String> CycleTimeList = new ArrayList();
        for (int i = 1; i <= cycleForNum; i++) {

            String startStr = null;
            Long startStrLong = null;
            Long cycleLong = null;
            try {
                startStr = sdf.format(new Date(calculationLong));
                Date startStrParse = new SimpleDateFormat(DateUtil.DATE_FORMAT_FULL).parse(startStr);
                startStrLong = startStrParse.getTime();
                cycleLong = cycleNum * 86400000L;
            } catch (ParseException e) {
                e.printStackTrace();
            }
            calculationLong = startStrLong + cycleLong;
            String endStr = endTime;
            if (calculationLong <= endLong) {
                endStr = sdf.format(new Date(calculationLong));
            }

            CycleTimeList.add(startStr);
            CycleTimeList.add(endStr);
        }
        return CycleTimeList;
    }

    public static int daysBetween(String smdate, String bdate) throws ParseException {

        SimpleDateFormat sdf = new SimpleDateFormat(DateUtil.DATE_FORMAT_FULL);
        Calendar cal = Calendar.getInstance();
        cal.setTime(sdf.parse(smdate));
        long time1 = cal.getTimeInMillis();
        cal.setTime(sdf.parse(bdate));
        long time2 = cal.getTimeInMillis();
        long between_days = (time2 - time1) / (1000 * 3600 * 24);
        return Integer.parseInt(String.valueOf(between_days));

    }

    /*
     * 将时间转换为时间戳
     */
    public static long dateToStamp2(String s) throws ParseException {
        String res;
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date date = simpleDateFormat.parse(s);
        long ts = date.getTime();
        res = String.valueOf(ts);
        return ts;
    }

    /*
     * 将时间戳转换为时间
     */
    public static Date stampToDate2(String s) {
        String res;
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        long lt = new Long(s);
        Date date = new Date(lt);
        res = simpleDateFormat.format(date);
        return date;
    }

    /**
     * 把秒转为友好的展示 *时*分*秒
     * 
     * @param seconds
     * @return
     */
    public static String secondsFormatting(Long seconds) {
        Long hour = seconds / 60 / 60;
        Long minutes = seconds / 60 % 60;
        Long remainingSeconds = seconds % 60;
        return hour + "时" + minutes + "分" + remainingSeconds + "秒";
    }

    /**
     * 获取某个年份 某个月的 当月最小日期
     * 
     * @param year
     *            年份
     * @param month
     *            月份
     * @return
     */
    public static Date getBeginTime(int year, int month) {
        YearMonth yearMonth = YearMonth.of(year, month);
        LocalDate localDate = yearMonth.atDay(1);
        LocalDateTime startOfDay = localDate.atStartOfDay();
        ZonedDateTime zonedDateTime = startOfDay.atZone(ZoneId.of("Asia/Shanghai"));
        return Date.from(zonedDateTime.toInstant());
    }

    /**
     * 获取某个年份 某个月的 当月最大日期
     * 
     * @param year
     *            年份
     * @param month
     *            月份
     * @return
     */
    public static Date getEndTime(int year, int month) {
        YearMonth yearMonth = YearMonth.of(year, month);
        LocalDate endOfMonth = yearMonth.atEndOfMonth();
        LocalDateTime localDateTime = endOfMonth.atTime(23, 59, 59, 999);
        ZonedDateTime zonedDateTime = localDateTime.atZone(ZoneId.of("Asia/Shanghai"));
        return Date.from(zonedDateTime.toInstant());
    }

    public static void main(String[] args) throws ParseException {
        // System.out.println(DateUtil.getDateWithMaxTime(new Date()));
        // System.out.println(DateUtil.getDateWithMinTime(new Date()));
    }

}
