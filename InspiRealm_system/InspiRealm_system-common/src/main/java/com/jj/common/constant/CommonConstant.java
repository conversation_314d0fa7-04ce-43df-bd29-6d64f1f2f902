package com.jj.common.constant;

/**
 * 全局公共常量
 *
 * <AUTHOR>
 */
public interface CommonConstant {
    /**
     * 操作成功
     */
    String RETURN_WORK_SUCCESS = "操作成功";
    /**
     * 操作失败
     */
    String RETURN_WORK_FAIL = "操作失败";
    /**
     * 操作成功
     */
    String RETURN_SEARCH_SUCCESS = "查询成功";
    /**
     * 计算中,请稍后查看计算结果
     */
    String RETURN_RUNNING = "计算中,请稍后查看计算结果";
    /**
     * 操作失败,数据格式不正确
     */
    String RETURN_WORK_FAIL_DATA_FORMAT = "操作失败,数据格式不正确";

    /**
     * 操作失败,接口已停用
     */
    String RETURN_WORK_FAIL_DATA_STATUS = "操作失败,接口已停用";
    /**
     * 操作失败,数据不存在
     */
    String RETURN_WORK_FAIL_DATA_NOT_EXIST = "操作失败,数据不存在";

    /**
     * 操作失败,数据不存在
     */
    String RETURN_WORK_FAIL_DATA_EXIST = "操作失败,数据已存在";
    /**
     * 操作失败,版本号不存在
     */
    String RETURN_WORK_FAIL_VERSION = "操作失败,记录不存在或已被更新";
    /**
     * 项目版本号(banner使用)
     */
    String PROJECT_VERSION = "4.0.0";

    /**
     * token请求头名称
     */
    String TOKEN_HEADER = "Authorization";

    /**
     * The access token issued by the authorization server. This value is REQUIRED.
     */
    String ACCESS_TOKEN = "access_token";

    String BEARER_TYPE = "Bearer";

    /**
     * 标签 header key
     */
    String HEADER_LABEL = "x-label";

    /**
     * 标签 header 分隔符
     */
    String HEADER_LABEL_SPLIT = ",";

    /**
     * 标签或 名称
     */
    String LABEL_OR = "labelOr";

    /**
     * 标签且 名称
     */
    String LABEL_AND = "labelAnd";

    /**
     * 权重key
     */
    String WEIGHT_KEY = "weight";

    /**
     * 删除
     */
    Integer STATUS_DEL = 1;

    /**
     * 未删除
     */
    Integer STATUS_DEL_UN = 0;

    /**
     * 未删除
     */
    Integer INIT_VERSION = 0;

    /**
     * 正常
     */
    String STATUS_NORMAL = "0";

    /**
     * 锁定
     */
    String STATUS_LOCK = "9";

    /**
     * 目录
     */
    Integer CATALOG = -1;

    /**
     * 菜单
     */
    Integer MENU = 1;

    /**
     * 权限
     */
    Integer PERMISSION = 2;

    /**
     * 删除标记
     */
    String DEL_FLAG = "is_del";

    /**
     * 超级管理员用户名
     */
    String ADMIN_USER_NAME = "admin";

    /**
     * 公共日期格式
     */
    String MONTH_FORMAT = "yyyy-MM";
    String DATE_FORMAT = "yyyy-MM-dd";
    String DATETIME_FORMAT = "yyyy-MM-dd HH:mm:ss";
    String SIMPLE_MONTH_FORMAT = "yyyyMM";
    String SIMPLE_DATE_FORMAT = "yyyyMMdd";
    String SIMPLE_DATETIME_FORMAT = "yyyyMMddHHmmss";

    String DEF_USER_PASSWORD = "123456";

    String LOCK_KEY_PREFIX = "LOCK_KEY:";

    /**
     * 租户id参数
     */
    String TENANT_ID_PARAM = "tenantId";

    /**
     * 日志链路追踪id信息头
     */
    String TRACE_ID_HEADER = "x-traceId-header";
    /**
     * 日志链路追踪id日志标志
     */
    String LOG_TRACE_ID = "traceId";
    /**
     * 负载均衡策略-版本号 信息头
     */
    String Z_L_T_VERSION = "z-l-t-version";
    /**
     * 注册中心元数据 版本号
     */
    String METADATA_VERSION = "version";

    /**
     * 数据库类型 oracle
     */
    String DATABASE_ORACLE = "oracle";

    /**
     * 数据库类型 mysql
     */
    String DATABASE_MYSQL = "mysql";

    /**
     * JJ_SYSTEM_MENU获取
     */
    String SYSTEM_MENU = "system-meun";


    /**
     * appKey
     */
    String APP_KEY = "appKey";

    /**
     * appSecret
     */
    String APP_SECRET = "appSecret";

    /**
     * timestamp
     */
    String TIME_STAMP = "timestamp";

    /**
     * sign
     */
    String SIGN = "sign";

    /**
     * token
     */
    String TOKEN = "token";

    /**
     * 登录密码错误次数
     */
    Integer NUMBER_OF_LOGIN_RESTRICTIONS = 5;

}
