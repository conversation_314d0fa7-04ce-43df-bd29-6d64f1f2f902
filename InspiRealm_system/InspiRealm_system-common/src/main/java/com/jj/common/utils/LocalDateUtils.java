package com.jj.common.utils;

import com.jj.common.exception.BizException;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.util.ObjectUtils;

import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeFormatterBuilder;
import java.time.temporal.ChronoField;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

public class LocalDateUtils {

    // 默认日期格式
    public static final String DATE_DEFAULT_FORMAT = "yyyyMMdd";
    // 默认日期格式
    public static final String YYYY_MM_DD = "yyyy-MM-dd";

    // 默认日期时间格式
    public static final String DATETIME_DEFAULT_FORMAT = "yyyyMMdd HH:mm:ss";

    // 默认时间格式
    public static final String TIME_DEFAULT_FORMAT = "HH:mm:ss";

    // 时间戳格式
    public static final String TIME_STAMP_FORMAT = "yyyy-MM-dd'T'HH:mm:ss'Z'";

    // 获取当天的开始时间（00:00:00）
    public static Date getStartOfDay(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }

    // 获取当天的结束时间（23:59:59）
    public static Date getEndOfDay(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 999);
        return calendar.getTime();
    }
    /**
     * 取年龄
     *
     * @param birthday
     * @param now_date
     * @return
     */
    public static int getAge(String birthday, String now_date) throws Exception {
        LocalDate startDate = strDate2LocalDate(birthday);
        LocalDate endDate = strDate2LocalDate(now_date);
        return startDate.until(endDate).getYears();
    }

    /**
     * 日期格式化yyyyMMdd
     *
     * @param date
     * @return
     */
    public static Date formatDate(String date, DateStyle dateStyle) {
        if (date == null || dateStyle == null) {
            return null;
        }
        return formatDate(date, dateStyle.getValue());
    }

    /**
     * 添加分钟
     *
     * @param date
     * @param num
     * @return
     */
    public static Date addMinute(Date date, long num) {
        LocalDateTime localDate;
        if (date == null) {
            localDate = LocalDateTime.now();
        } else {
            localDate = date2LocalDateTime(date);
        }
        return localDateTime2Date(localDate.plusMinutes(num));
    }

    /**
     * 日期格式化yyyyMMdd
     *
     * @param date
     * @return
     */
    public static Date formatDate(String date, String format) {
        if (date.length() != format.length()) {
            System.err.println("日期格式错误: " + date + " 与 " + format + " 日期格式不一致");
            return null;
        }

        try {
            LocalDate localDate = LocalDate.parse(date, DateTimeFormatter.ofPattern(format));
            Instant instant = localDate.atStartOfDay().atZone(ZoneId.systemDefault()).toInstant();
            return Date.from(instant);
        } catch (Exception e) {
            e.printStackTrace();

        }
        return null;
    }

    /**
     * 日期格式化
     *
     * @param date
     * @return
     */
    public static String getDateFormat(Date date, DateStyle dateStyle) {
        return date2LocalDateTime(date).format(DateTimeFormatter.ofPattern(dateStyle.getValue()));
    }

    /**
     * 日期yyyyMM字符串，转为localDate
     * @param date
     * @return
     */
    public static LocalDate yyyyMMToLocalDate(String date) {
        DateTimeFormatter formatter = new DateTimeFormatterBuilder()
                .appendPattern("yyyyMM")
                .parseDefaulting(ChronoField.DAY_OF_MONTH, 1)
                .toFormatter();

        return LocalDate.parse(date,formatter);
    }

    /**
     * 日期yyyyMM字符串，转为Date
     * @param date
     * @return
     */
    public static Date yyyyMMToDate(String date) {
        return LocalDateUtils.localDate2Date(yyyyMMToLocalDate(date));
    }

    /**
     * 日期格式化yyyyMMdd
     *
     * @param date
     * @return
     */
    public static String getDateFormat(Date date) {
        return date2LocalDateTime(date).format(DateTimeFormatter.BASIC_ISO_DATE);
    }

    /**
     * 日期格式化yyyyMMdd HH:mm:ss
     *
     * @param date
     * @return
     */
    public static String getDateTimeFormat(Date date) {
        return date2LocalDateTime(date).format(DateTimeFormatter.ofPattern(DATETIME_DEFAULT_FORMAT));
    }

    /**
     * 时间格式化
     *
     * @param date
     * @return HH:mm:ss
     */
    public static String getTimeFormat(Date date) {
        return date2LocalDateTime(date).format(DateTimeFormatter.ofPattern(TIME_DEFAULT_FORMAT));
    }

    /**
     * 日期格式化
     *
     * @param date
     * @param formatStr 格式类型
     * @return
     */
    public static String getDateFormat(Date date, String formatStr) {
        if (formatStr != null && formatStr != "") {
            return date2LocalDateTime(date).format(DateTimeFormatter.ofPattern(formatStr));
        }
        return null;
    }

    /**
     * 日期格式化
     * 使用LocalDate进行过渡：strDate -> LocalDate -> Date
     *
     * @param date
     * @return
     */
    public static Date getDateFormat(String date) {
        return localDate2Date(strDate2LocalDate(date));
    }

    /**
     * 时间格式化
     *
     * @param date
     * @return
     */
    public static Date getDateTimeFormat(String date) {
        if (date.length() != 17) {
            System.err.println("日期格式错误: " + date + " 与 yyyyMMdd HH:mm:ss 日期格式不一致");
            return null;
        }

//        var reg = /^[1-9]\d{3}-(0[1-9]|1[0-2])-(0[1-9]|[1-2][0-9]|3[0-1])\s+(20|21|22|23|[0-1]\d):[0-5]\d:[0-5]\d$/;

        try {
            LocalDateTime localDateTime = LocalDateTime.parse(date, DateTimeFormatter.ofPattern(DATETIME_DEFAULT_FORMAT));
            Instant instant = localDateTime.atZone(ZoneId.systemDefault()).toInstant();
            return Date.from(instant);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 获取当前日期(yyyyMMdd)
     *
     * @return
     */
    public static Date getNowDate() {
        Instant instant = LocalDate.now().atStartOfDay().atZone(ZoneId.systemDefault()).toInstant();
        return Date.from(instant);
    }

    /**
     * 获取当前日期星期一日期
     *
     * @return date
     */
    public static Date getFirstDayOfWeek() {
        LocalDateTime localDateTime = LocalDateTime.now();
        int day = localDateTime.get(ChronoField.DAY_OF_WEEK);
        return localDateTime2Date(localDateTime.minusDays(day - 1));
    }

    /**
     * 获取当前日期星期日日期
     *
     * @return date
     */
    public static Date getLastDayOfWeek() {
        LocalDateTime localDateTime = LocalDateTime.now();
        int day = localDateTime.get(ChronoField.DAY_OF_WEEK);
        return localDateTime2Date(localDateTime.plusDays(7 - day));
    }

    /**
     * 获取指定日期星期一日期
     *
     * @param date 指定日期
     * @return date
     */
    public static Date getFirstDayOfWeek(Date date) {
        if (null == date) {
            return null;
        }

        LocalDateTime localDateTime = date2LocalDateTime(date);
        int day = localDateTime.get(ChronoField.DAY_OF_WEEK);
        return localDateTime2Date(localDateTime.minusDays(day - 1));
    }

    /**
     * 获取指定时间当年的第一天
     *
     * @param date 指定日期
     * @return date
     */
//    public static Date getFirstDayOfYear(Date date) {
//        if (null == date) {
//            return null;
//        }
//
//        LocalDateTime localDateTime = date2LocalDateTime(date);
//        int day = localDateTime.get(ChronoField.DAY_OF_YEAR);
//        return localDateTime2Date(localDateTime.minusDays(day - 1));
//    }

//    public static Date getFirstDayOfYear2(Date date) {
//        return localDate2Date(date2LocalDate(date).with(TemporalAdjusters.firstDayOfYear()));
//    }
//
//    public static Date getLastDayOfYear2(Date date) {
//        return localDate2Date(date2LocalDate(date).with(TemporalAdjusters.lastDayOfYear()));
//    }

    /**
     * demo: year=2021
     * return Fri Jan 01 00:00:00 CST 2021
     * @param year
     * @return
     */
    public static Date getFirstDayOfYear(int year) {
        LocalDateTime d = LocalDateTime.of(year, 1, 1, 0, 0, 0);
        Instant instant = d.atZone(ZoneId.systemDefault()).toInstant();
        return Date.from(instant);
    }

    /**
     * demo: year=2021
     * return Fri Dec 31 23:59:59 CST 2021
     * @param year
     * @return
     */
    public static Date getLastDayOfYear(int year) {
        LocalDateTime d = LocalDateTime.of(year, 12, 31, 23, 59, 59);
        Instant instant = d.atZone(ZoneId.systemDefault()).toInstant();
        return Date.from(instant);
    }

    public static Date getFirstDayOfYear(Date date) {
        Instant instant = date2LocalDateTime(date).with(TemporalAdjusters.firstDayOfYear()).atZone(ZoneId.systemDefault()).toInstant();
        return Date.from(instant);
    }

    public static Date getLastDayOfYear(Date date) {
        LocalDateTime d = date2LocalDateTime(date).with(TemporalAdjusters.lastDayOfYear());
        d = LocalDateTime.of(d.getYear(), d.getMonth(), d.getDayOfMonth(), 23, 59, 59);

        Instant instant = d.atZone(ZoneId.systemDefault()).toInstant();
        return Date.from(instant);
    }

    /**
     * 跟getLastDayOfYear做区分
     * 返回时间为2019-12-31 00:00:00
     *
     * @param date
     * @return
     */
    public static Date getLastDayOfYearStartTime(Date date) {
        LocalDateTime d = date2LocalDateTime(date).with(TemporalAdjusters.lastDayOfYear());
        d = LocalDateTime.of(d.getYear(), d.getMonth(), d.getDayOfMonth(), 00, 00, 00);

        Instant instant = d.atZone(ZoneId.systemDefault()).toInstant();
        return Date.from(instant);
    }

    /**
     * 获取指定日期星期日日期
     *
     * @param date 指定日期
     * @return date
     */
    public static Date getLastDayOfWeek(Date date) {
        if (null == date) {
            return null;
        }

        LocalDateTime localDateTime = date2LocalDateTime(date);
        int day = localDateTime.get(ChronoField.DAY_OF_WEEK);
        return localDateTime2Date(localDateTime.plusDays(7 - day));
    }

    /**
     * 获取当前月的第一天
     *
     * @return date
     */
    public static Date getFirstDayOfMonth() {
        LocalDateTime localDateTime = LocalDateTime.now().with(TemporalAdjusters.firstDayOfMonth());
        Instant instant = localDateTime.atZone(ZoneId.systemDefault()).toInstant();
        return Date.from(instant);
    }

    /**
     * 获取指定日期所属月份的第一天
     *
     * @param workdate 指定日期
     * @return
     * @throws Exception
     */
    public static String getFirstDayOfMonth(String workdate) throws Exception {
        if (workdate.length() != 8) {
            System.err.println("日期格式错误: " + workdate + " 与 yyyyMMdd 日期格式不一致");
            return null;
        }

        LocalDate localDate = LocalDate.parse(workdate, DateTimeFormatter.BASIC_ISO_DATE);
        return localDate.with(TemporalAdjusters.firstDayOfMonth()).format(DateTimeFormatter.BASIC_ISO_DATE);
    }

    /**
     * 获取当前月的最后一天
     *
     * @return
     */
    public static Date getLastDayOfMonth() {
        LocalDateTime localDateTime = LocalDateTime.now().with(TemporalAdjusters.lastDayOfMonth());
        Instant instant = localDateTime.atZone(ZoneId.systemDefault()).toInstant();
        return Date.from(instant);
    }

    /**
     * 获取指定月的第一天
     *
     * @param date
     * @return
     */
    public static Date getFirstDayOfMonth(Date date) {
        Instant instant = date2LocalDateTime(date).with(TemporalAdjusters.firstDayOfMonth()).atZone(ZoneId.systemDefault()).toInstant();
        return Date.from(instant);
    }

    /**
     * 获取指定月的最后一天
     *
     * @param date
     * @return
     */
    public static Date getLastDayOfMonth(Date date) {
        Instant instant = date2LocalDateTime(date).with(TemporalAdjusters.lastDayOfMonth()).atZone(ZoneId.systemDefault()).toInstant();
        return Date.from(instant);
    }

    public static Date getLastDayOfMonthEndTime(Date date) {
        LocalDateTime d = date2LocalDateTime(date).with(TemporalAdjusters.lastDayOfMonth());
        d = LocalDateTime.of(d.getYear(), d.getMonth(), d.getDayOfMonth(), 23, 59, 59);

        Instant instant = d.atZone(ZoneId.systemDefault()).toInstant();
        return Date.from(instant);
    }

    /**
     * 取某一天的最后一分钟
     * @param date
     * @return
     */
    public static Date getLastDateTimeOfOneDay(Date date) {
        LocalDateTime d = date2LocalDateTime(date);
        d = LocalDateTime.of(d.getYear(), d.getMonth(), d.getDayOfMonth(), 23, 59, 59);

        Instant instant = d.atZone(ZoneId.systemDefault()).toInstant();
        return Date.from(instant);
    }

    /**
     *  取某一天的第一分钟
     * @param date
     * @return
     */
    public static Date getFirstDateTimeOfOneDay(Date date) {
        LocalDateTime d = date2LocalDateTime(date);
        d = LocalDateTime.of(d.getYear(), d.getMonth(), d.getDayOfMonth(), 00, 00, 00);

        Instant instant = d.atZone(ZoneId.systemDefault()).toInstant();
        return Date.from(instant);
    }

    /**
     * 获取指定日期前一天
     *
     * @param date
     * @return
     */
    public static Date getDayBefore(Date date) {
        Instant instant = date2LocalDateTime(date).minusDays(1).atZone(ZoneId.systemDefault()).toInstant();
        return Date.from(instant);
    }

    /**
     * 获取指定日期后一天
     *
     * @param date
     * @return
     */
    public static Date getDayAfter(Date date) {
        if (date == null) {
            return null;
        }
        Instant instant = date2LocalDateTime(date).plusDays(1).atZone(ZoneId.systemDefault()).toInstant();
        return Date.from(instant);
    }

    /**
     * 获取当前年
     *
     * @return
     */
    public static int getNowYear() {
        return LocalDateTime.now().getYear();
    }

    /**
     * 获取当前月份
     *
     * @return
     */
    public static int getNowMonth() {
        return LocalDateTime.now().getMonthValue();
    }

    /**
     * 获取当月天数
     *
     * @return
     */
    public static int getNowMonthDay() {
        return LocalDate.now().lengthOfMonth();
    }

    /**
     * 获取时间段的每一天
     *
     * @param startDate 开始日期
     * @param endDate   结算日期
     * @return 日期列表
     */
    public static List<Date> getEveryDay(Date startDate, Date endDate) {
        if (startDate == null || endDate == null) {
            return null;
        }

        LocalDate startLocalDate = date2LocalDate(startDate);
        LocalDate endLocalDate = date2LocalDate(endDate);
        Long days = endLocalDate.toEpochDay() - startLocalDate.toEpochDay();

        List<Date> dates = new ArrayList<>();
        LocalDate tempLocalDate = null;
        for (int i = 0; i <= days; i++) {
            tempLocalDate = startLocalDate;
            if (tempLocalDate.toEpochDay() - endLocalDate.toEpochDay() <= 0) {
                dates.add(localDate2Date(startLocalDate));
                startLocalDate = tempLocalDate.plusDays(1);
            }
        }
        return dates;
    }

    /**
     * 获取提前多少个月
     *
     * @param monty
     * @return
     */
    public static Date minusMonths(int monty) {
        Instant instant = LocalDate.now().minusMonths(monty).atStartOfDay().atZone(ZoneId.systemDefault()).toInstant();
        return Date.from(instant);
    }

    /**
     * 获取提前多少个月
     *
     * @param monty
     * @return
     */
    public static Date minusMonths(LocalDate localDate, int monty) {
        Instant instant = localDate.minusMonths(monty).atStartOfDay().atZone(ZoneId.systemDefault()).toInstant();
        return Date.from(instant);
    }

    /**
     * 获取提前多少年
     *
     * @param yearsToSubtract
     * @return
     */
    public static Date minusYears(long yearsToSubtract) {
        Instant instant = LocalDate.now().minusYears(yearsToSubtract).atStartOfDay().atZone(ZoneId.systemDefault()).toInstant();
        return Date.from(instant);
    }

    /**
     * 计算两个日期之间的天数
     *
     * @param startDate
     * @param endDate
     * @return
     * @throws Exception
     */
    public static long getBetweenDays(String startDate, String endDate) throws Exception {
        LocalDate startLocalDate = strDate2LocalDate(startDate);
        LocalDate endLocalDate = strDate2LocalDate(endDate);
        return endLocalDate.toEpochDay() - startLocalDate.toEpochDay();
    }

    /**
     * 日期srcDate 加上 天数days 后的日期
     *
     * @param srcDate
     * @param days
     * @return
     * @throws Exception
     */
    public static String getDateAddDays(String srcDate, int days) throws Exception {
        return strDate2LocalDate(srcDate).plusDays(days).format(DateTimeFormatter.BASIC_ISO_DATE);
    }

    /**
     * 日期srcDate 加上 月数months 后的日期
     *
     * @param srcDate
     * @param months
     * @return
     * @throws Exception
     */
    public static String getDateAddMonths(String srcDate, int months) throws Exception {
        return strDate2LocalDate(srcDate).plusMonths(months).format(DateTimeFormatter.BASIC_ISO_DATE);
    }

    /**
     * 日期srcDate 加上 年数years 后的日期
     *
     * @param srcDate
     * @param years
     * @return
     * @throws Exception
     */
    public static String getDateAddYears(String srcDate, int years) {
        return strDate2LocalDate(srcDate).plusYears(years).format(DateTimeFormatter.BASIC_ISO_DATE);
    }

    /**
     * 通过身份证号码获取生日
     *
     * @param id_code
     * @return
     */
    public static String getBrithdayByIdCode(String id_code) {
        return id_code.substring(6, 14);
    }

    /**
     * LocalDate 转换成 Date
     *
     * @param localDate
     * @return
     */
    public static Date localDate2Date(LocalDate localDate) {
        if (localDate == null) {
            return null;
        }
        return localDateTime2Date(localDate.atStartOfDay());
    }

    /**
     * LocalDateTime 转换成 Date
     *
     * @param localDateTime
     * @return
     */
    public static Date localDateTime2Date(LocalDateTime localDateTime) {
        Instant instant = localDateTime.atZone(ZoneId.systemDefault()).toInstant();
        return Date.from(instant);
    }

    /**
     * Date 转换成 LocalDate
     *
     * @param date
     * @return
     */
    public static LocalDate date2LocalDate(Date date) {
        return date2LocalDateTime(date).toLocalDate();
    }

    /**
     * Date 转换成 LocalDateTime
     *
     * @param date
     * @return
     */
    public static LocalDateTime date2LocalDateTime(Date date) {
        return LocalDateTime.ofInstant(date.toInstant(), ZoneId.systemDefault());
    }

    /**
     * strDate 转换成 LocalDate
     *
     * @param strDate
     * @return
     */
    public static LocalDate strDate2LocalDate(String strDate) {
        LocalDate localDate = null;
        if (8 == strDate.length()) {
            localDate = LocalDate.parse(strDate, DateTimeFormatter.BASIC_ISO_DATE);
        } else if (10 == strDate.length()) {
            localDate = LocalDate.parse(strDate, DateTimeFormatter.ISO_LOCAL_DATE);
        } else {
            System.err.println(strDate + " 日期格式错误");
            return null;
        }

        return localDate.atStartOfDay().atZone(ZoneId.systemDefault()).toLocalDate();
    }

    public static Date getFirstDayOfMonth(String date, String format) {
        LocalDate localDate = LocalDate.parse(date, DateTimeFormatter.ofPattern(format));
        LocalDate first = localDate.with(TemporalAdjusters.firstDayOfMonth());
        return localDate2Date(first);
    }

    public static Date getLastDayOfMonth(String date, String format) {
        LocalDate localDate = LocalDate.parse(date, DateTimeFormatter.ofPattern(format));
        LocalDate last = localDate.with(TemporalAdjusters.lastDayOfMonth());
        return localDate2Date(last);
    }



    /**
     * 相差天数
     *
     * @param start 开始时间戳
     * @param end   结束时间戳
     * @return
     */
    public static int getIntervalDays(Long start, Long end) {
        int num = -1;
        if (start != null && end != null) {
            long time = Math.abs(end - start);
            num = (int) (time / (24 * 60 * 60 * 1000));
        }
        return num;
    }

    public static int getEndOfQuarter(Date date) {
        LocalDate localDate = date2LocalDate(date);
        Month month = localDate.getMonth();
        Month firstMonthOfQuarter = month.firstMonthOfQuarter();
        int quarter = firstMonthOfQuarter.getValue() + 2;
        return quarter;
    }

    public static Date getEndOfQuarterDate(Date date) {
        LocalDate localDate = date2LocalDate(date);
        Month month = localDate.getMonth();
        Month firstMonthOfQuarter = month.firstMonthOfQuarter();
        Month endMonthOfQuarter = Month.of(firstMonthOfQuarter.getValue() + 2);

        localDate = LocalDate.of(localDate.getYear(), endMonthOfQuarter, endMonthOfQuarter.length(localDate.isLeapYear()));

        return localDate2Date(localDate);
    }

    /**
     * 增加或者减少月份
     *
     * @param date
     * @param i
     * @return
     */
    public static Date addMonths(Date date, int i) {

        LocalDate localDate = date2LocalDate(date);

        localDate = localDate.plusMonths(i);


        return localDate2Date(localDate);

    }

    public static Date addDays(Date date, int i) {

        LocalDate localDate = date2LocalDate(date);

        localDate = localDate.plusDays(i);


        return localDate2Date(localDate);

    }

    /**
     * 在指定的日期加几天
     * @param date
     * @param plusDays
     * @return
     */
    public static Date plusDays(Date date,long plusDays) {
        Instant instant = date2LocalDateTime(date).plusDays(plusDays).atZone(ZoneId.systemDefault()).toInstant();
        return Date.from(instant);
    }

    public static boolean isEffectiveDate(Long time) {
        Long setTime= Long.valueOf(1000*60*30);//1秒*60*30
        Date date= new Date();
        return date.getTime()-time<=setTime && date.getTime()>time;
    }


}

