package com.jj.common.utils;

import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;


@Component
public class SpringContextUtils implements ApplicationContextAware {
    private static ApplicationContext applicationContext;


    /**
     * 获取所有的beanName
     *
     * @return
     */
    public static List<String> getBeanNames() {

        String[] names = applicationContext.getBeanDefinitionNames();

        return Arrays.asList(names);

    }

//    /**
//     * 获取当前的生产环境 是否是生产环境
//     *
//     * @return
//     */
//    public static boolean isProduct() {
//
//        String[] pros = applicationContext.getEnvironment().getActiveProfiles();
//
//        if (ObjectUtils.isEmpty(pros)) {
//            return true;
//        } else if ("prod".equalsIgnoreCase(pros[0])) {
//
//            return true;
//        } else {
//            return false;
//        }
//    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext)
            throws BeansException {
        SpringContextUtils.applicationContext = applicationContext;
    }

    public static Object getBean(String name) {
        return applicationContext.getBean(name);
    }

    public static <T> T getBean(String name, Class<T> requiredType) {


        return applicationContext.getBean(name, requiredType);
    }


    public static <T> T getBean(Class<T> requiredType) {

        return applicationContext.getBean(requiredType);
    }

    public static boolean containsBean(String name) {
        return applicationContext.containsBean(name);
    }

    public static boolean isSingleton(String name) {
        return applicationContext.isSingleton(name);
    }

    public static <T> List<T> getBeans(Class<T> requiredType) {
        List<T> list = new ArrayList<>();
        Map<String, T> map = applicationContext.getBeansOfType(requiredType);
        for (Map.Entry<String, T> entry : map.entrySet()) {
            list.add(entry.getValue());
        }
        return list;
    }

    public static Class<? extends Object> getType(String name) {
        return applicationContext.getType(name);
    }

//    /**
//     * 获取对应注解的值
//     *
//     * @param pcName 导入的key
//     * @return
//     */
//    public static List<Object> getMatchImportantComponeBean(String pcName) {
//
//
//        List<Object> beans = new ArrayList<>(0);
//        Map<String, Object> objectMap = applicationContext.getBeansWithAnnotation(ImportCompone.class);
//
//        for (Map.Entry<String, Object> objectEntry : objectMap.entrySet()) {
//            Object ob = objectEntry.getValue();
//            if (ObjectUtils.isNotEmpty(ob.getClass().getAnnotation(ImportCompone.class)) &&
//                    StringUtils.equals(ob.getClass().getAnnotation(ImportCompone.class).pcName(),
//                            pcName)) {
//                beans.add(ob);
//            }
//
//        }
//
//        return beans;
//
//    }

//    /**
//     * 获取被{@link com.servyou.betri.biz.annotation.ImportCompone#isMaster()}为true修饰的DTo
//     * 且其对应的注解
//     *
//     * @param pcName
//     * @return
//     */
//    public static Object getMatchImportantComponeMasterBean(String pcName) {
//
//
//        Map<String, Object> objectMap = applicationContext.getBeansWithAnnotation(ImportCompone.class);
//
//        for (Map.Entry<String, Object> objectEntry : objectMap.entrySet()) {
//            Object ob = objectEntry.getValue();
//            if (ObjectUtils.isNotEmpty(ob.getClass().getAnnotation(ImportCompone.class)) &&
//                    StringUtils.equals(ob.getClass().getAnnotation(ImportCompone.class).pcName(),
//                            pcName) && ob.getClass().getAnnotation(ImportCompone.class).isMaster()) {
//                return ob;
//            }
//
//        }
//
//        return null;
//    }

}
