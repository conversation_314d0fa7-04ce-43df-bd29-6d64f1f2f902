package com.jj.common.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * 统一API响应结果
 *
 * <AUTHOR>
 * @date 2024-07-29
 */
@ApiModel(description = "统一API响应结果")
public class JsonResult<T> implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 响应码
     */
    @ApiModelProperty(value = "响应码", example = "200")
    private Integer code;

    /**
     * 响应消息
     */
    @ApiModelProperty(value = "响应消息", example = "操作成功")
    private String message;

    /**
     * 响应数据
     */
    @ApiModelProperty(value = "响应数据")
    private T data;

    /**
     * 时间戳
     */
    @ApiModelProperty(value = "时间戳", example = "1690617600000")
    private Long timestamp;

    public JsonResult() {
        this.timestamp = System.currentTimeMillis();
    }

    public JsonResult(Integer code, String message) {
        this();
        this.code = code;
        this.message = message;
    }

    public JsonResult(Integer code, String message, T data) {
        this(code, message);
        this.data = data;
    }

    /**
     * 成功响应
     */
    public static <T> JsonResult<T> success() {
        return new JsonResult<>(200, "操作成功");
    }

    /**
     * 成功响应（带数据）
     */
    public static <T> JsonResult<T> success(T data) {
        return new JsonResult<>(200, "操作成功", data);
    }

    /**
     * 成功响应（自定义消息）
     */
    public static <T> JsonResult<T> success(String message) {
        return new JsonResult<>(200, message);
    }

    /**
     * 成功响应（自定义消息和数据）
     */
    public static <T> JsonResult<T> success(String message, T data) {
        return new JsonResult<>(200, message, data);
    }

    /**
     * 错误响应
     */
    public static <T> JsonResult<T> error() {
        return new JsonResult<>(500, "操作失败");
    }

    /**
     * 错误响应（自定义消息）
     */
    public static <T> JsonResult<T> error(String message) {
        return new JsonResult<>(500, message);
    }

    /**
     * 错误响应（自定义码和消息）
     */
    public static <T> JsonResult<T> error(Integer code, String message) {
        return new JsonResult<>(code, message);
    }

    /**
     * 判断是否成功
     */
    public boolean isSuccess() {
        return this.code != null && this.code == 200;
    }

    // Getter and Setter methods
    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }

    public Long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(Long timestamp) {
        this.timestamp = timestamp;
    }

    @Override
    public String toString() {
        return "JsonResult{" +
                "code=" + code +
                ", message='" + message + '\'' +
                ", data=" + data +
                ", timestamp=" + timestamp +
                '}';
    }
}
