package com.jj.common.enums;

import lombok.Getter;

@Getter
public enum  ErroEnum {

    LOGIN_ADMIN_UNAUTHORIZED_ERROR(403, "权限不足，请联系管理员"),
    LOGIN_ADMIN_TOKEN_EMPTY_ERROR(403, "token不能为空"),
    LOGIN_ADMIN_AUTHORITY_ERROR(401, "系统登陆失效，请重新登录。"),
    VIP_LEVEL_AUTHORITY_ERROR(403, "该功能需会员等级为%s及以上用户使用,请联系管理员变更。"),
    TIME_STAMP_TIME_OUT(500, "时间戳大于30分钟"),
    NOT_BLANK_APP_KEY(500, "appKey不可为空"),
    NOT_BLANK_APP_SECRET(500, "appSecret不可为空"),
    NOT_BLANK_TIME_STAMP(500, "timestamp不可为空"),
    NOT_BLANK_SIGN(500, "Sign不可为空"),
    SIGN_ERR(500, "验签失败"),
        ;

    private int code;

    private String msg;

    ErroEnum(int code, String msg){
        this.code = code;
        this.msg = msg;
    }

}
