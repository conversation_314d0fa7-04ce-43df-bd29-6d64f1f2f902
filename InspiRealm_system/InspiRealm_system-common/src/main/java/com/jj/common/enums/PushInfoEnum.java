package com.jj.common.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @Classname PushInfoEnum
 * @Created by cmx
 */
@Getter
public enum PushInfoEnum {

    APPROVE(1,"点赞","%s,点赞了:%s"),
    COLLECT(2,"收藏","%s,收藏了:%s"),
    COMMENT(3,"评论","%s,评论了你"),
    FOCUS(4,"关注","%s,关注了你"),
    ;

    PushInfoEnum(Integer code, String msg, String content) {
        this.code = code;
        this.msg = msg;
        this.content = content;
    }


    private Integer code;

    private String msg;

    private String content;

}
