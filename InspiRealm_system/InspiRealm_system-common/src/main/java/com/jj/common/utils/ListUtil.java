package com.jj.common.utils;

import cn.hutool.core.util.ObjectUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2021/3/13 14:41
 */
public class ListUtil {
    public static <T> List<List<T>> groupList(List<T> source, int n) {
        if (null == source || source.size() == 0 || n <= 0) {
            return new ArrayList<List<T>>();
        }
        List<List<T>> result = new ArrayList<List<T>>();

        int sourceSize = source.size();
        int size = (source.size() / n) + 1;
        for (int i = 0; i < size; i++) {
            List<T> subset = new ArrayList<T>();
            for (int j = i * n; j < (i + 1) * n; j++) {
                if (j < sourceSize) {
                    subset.add(source.get(j));
                }
            }
            result.add(subset);
        }
        return result;
    }

    /**
     * Description: Java8 Stream分割list集合
     * 
     * @param list
     *            集合数据
     * @param splitSize
     *            几个分割一组
     * @return 集合分割后的集合
     */
    public static <T> List<List<T>> splitList(List<T> list, int splitSize) {
        // 判断集合是否为空
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        // 计算分割后的大小
        int maxSize = (list.size() + splitSize - 1) / splitSize;
        // 开始分割
        return Stream.iterate(0, n -> n + 1).limit(maxSize).parallel()
            .map(a -> list.parallelStream().skip(a * splitSize).limit(splitSize).collect(Collectors.toList()))
            .filter(b -> !b.isEmpty()).collect(Collectors.toList());
    }

    /**
     * stream根据关键字去重
     * 
     * @param keyExtractor
     * @param <T>
     * @return
     */
    public static <T> Predicate<T> distinctByKey(Function<? super T, ?> keyExtractor) {
        ConcurrentHashMap<Object, Boolean> map = new ConcurrentHashMap<>();
        return t -> map.putIfAbsent(keyExtractor.apply(t), Boolean.TRUE) == null;
    }

    /**
     * List<Map>根据map字段排正序
     * 
     * @param list
     * @param feild
     * @return
     */
    public static List<Map<String, Object>> sortByFeildAsc(List<Map<String, Object>> list, String feild) {

        return sortByFeild(list, feild, null);
    }

    /**
     * List<Map>根据map字段排倒序
     *
     * @param list
     * @param feild
     * @return
     */
    public static List<Map<String, Object>> sortByFeildDesc(List<Map<String, Object>> list, String feild) {

        return sortByFeild(list, feild, "desc");
    }

    /**
     * List<Map>根据map字段排序
     *
     * @param list
     * @param feild
     *            排序字段
     * @param sortTyp
     *            排序方式 desc-倒序 asc-正序
     * @return
     */
    private static List<Map<String, Object>> sortByFeild(List<Map<String, Object>> list, String feild, String sortTyp) {
        if (ObjectUtil.isNotEmpty(list)) {
            list.sort((m1, m2) -> {
                if (StringUtils.equals(sortTyp, "desc")) {
                    return String.valueOf(m2.get(feild)).compareTo(String.valueOf(m1.get(feild)));
                } else {
                    return String.valueOf(m1.get(feild)).compareTo(String.valueOf(m2.get(feild)));
                }
            });
            // 或者 Collections类里面的sort方法也是list.sort()与上面一样
            // Collections.sort(list, (m1, m2)->
            // String.valueOf(m1.get(feild)).compareTo(String.valueOf(m2.get(feild)))); // lamuda排序
        }

        return list;
    }

    /**
     * 抽取list<Map> 指定key对应的所有值
     * 
     * @param list
     * @param key
     * @return
     */
    public static List<Object> extractListFeild(List<Map<String, Object>> list, String key) {

        List<Object> objects = new ArrayList<>();
        for (Map<String, Object> objectMap : list) {
            if (ObjectUtil.isNotEmpty(objectMap.get(key))) {
                objects.add(objectMap.get(key));
            }

        }
        return objects;
    }
    // public static <T> List<List<T>> groupList(List<T> source, int n) {
    // List<List<T>> result = new ArrayList<List<T>>();
    // int remainder = source.size() % n; // (先计算出余数)
    // int number = source.size() / n; // 然后是商
    // int offset = 0;// 偏移量
    // for (int i = 0; i < n; i++) {
    // List<T> value = null;
    // if (remainder > 0) {
    // value = source.subList(i * number + offset, (i + 1) * number + offset + 1);
    // remainder--;
    // offset++;
    // } else {
    // value = source.subList(i * number + offset, (i + 1) * number + offset);
    // }
    // result.add(value);
    // }
    // return result;
    // }
}
