package com.jj.common.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;

@SuppressWarnings("all")
@Slf4j
public class UserInfoUtil {

    /**
     * 获取用户id
     * @return
     */
    public static String getUserId() {
        return getValue("userId");
    }

    /**
     * 获取uid
     * @return
     */
    public static String getUid() {
        return getValue("uid");
    }
    /**
     * 获取权限集合
     */
    public static String getPermissions() {
        return getValue("permissions");
    }

    /**
     * 获取角色集合
     */
    public static String getRoles() {
        return getValue("roles");
    }

    /**
     * 获取部门Id
     */
    public static String getDeptId() {
        return getValue("deptId");
    }

    /**
     * 获取用户账号
     */
    public static String getUserName() {
        return getValue("userName");
    }

    /**
     * 获取用户集团
     */
    public static String getGroupId() {
        return getValue("groupId");
    }


    /**
     * 获取token
     *
     * @return
     */
    public static String getToken() {
        return getValue("token");
    }

    /**
     * 获取请求头信息
     */
    private static String getValue(String key) {
        try {
            HttpServletRequest request =
                ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
            return request.getAttribute(key) != null ? String.valueOf(request.getAttribute(key)) : "";
        } catch (Exception e) {
            return "";
        }
    }

}
