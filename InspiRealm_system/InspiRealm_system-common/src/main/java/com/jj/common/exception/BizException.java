package com.jj.common.exception;


import com.jj.common.enums.ErroEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;

/**
 * 自定义异常
 */
@Data
@AllArgsConstructor
@Slf4j
public class BizException extends RuntimeException {

    private static final long serialVersionUID = 8465805212923957316L;

    private static final String PLACHOLDER_REGEX = "%s";

    Object data;

    private String msg;

    private int code = 500;

    private boolean success;

    private int messageCode = 500;

    private String message;

    public BizException(Throwable e) {
        super("系统异常，请联系管理员", e);

        this.msg = "系统异常，请联系管理员";

    }

    public BizException(String msg) {
        super(msg);
        this.msg = msg;

        this.success = false;
        this.message = msg;

    }

    public BizException(String msg, Object... params) {
        super(msg);
        String defMsg = params == null ? "" : Arrays.toString(params);
        msg = msg == null ? defMsg : msg;
        msg = String.format(msg, params);
        this.msg = msg;

        this.success = false;
        this.message = msg;
    }



    public BizException(String msg, Throwable e) {
        super(msg, e);
        this.msg = msg;

        this.success = false;
        this.message = msg;
    }

    public BizException(String msg, int code) {
        super(msg);
        this.msg = msg;
        this.code = code;

        this.success = false;
        this.message = msg;
        this.messageCode = code;
    }

    public BizException(String msg, int code, Object data) {
        super(msg);
        this.msg = msg;
        this.code = code;
        this.data = data;

        this.success = false;
        this.message = msg;
        this.messageCode = code;

    }


    public BizException(String msg, int code, Throwable e) {
        super(msg, e);
        this.msg = msg;
        this.code = code;

        this.success = false;
        this.message = msg;
        this.messageCode = code;

    }


    public BizException(ErroEnum erroEnum){
        super(erroEnum.getMsg());
        this.msg = erroEnum.getMsg();
        this.code = erroEnum.getCode();
        this.success = false;
        this.message = erroEnum.getMsg();
        this.messageCode = erroEnum.getCode();
    }



}
