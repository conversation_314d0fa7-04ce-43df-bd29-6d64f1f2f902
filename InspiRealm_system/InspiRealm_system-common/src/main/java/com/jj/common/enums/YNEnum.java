package com.jj.common.enums;


public enum YNEnum {
    Y("Y", "是"),
    N("N", "否"),
    U("U", "修改"),
    D("D", "删除"),;

    private String type;
    private String value;

    YNEnum(String type, String value) {
        this.type = type;
        this.value = value;
    }

    public static YNEnum getByType(String type) {
        YNEnum[] var1 = values();
        for (YNEnum ynEnum : var1) {
            if (ynEnum.type.equals(type)) {
                return ynEnum;
            }
        }
        return null;
    }

    public static YNEnum getByValue(String value) {
        YNEnum[] var1 = values();
        int var2 = var1.length;

        for (int var3 = 0; var3 < var2; ++var3) {
            YNEnum ynEnum = var1[var3];
            if (ynEnum.value.equals(value)) {
                return ynEnum;
            }
        }

        return null;
    }

    public String getType() {
        return this.type;
    }
}
