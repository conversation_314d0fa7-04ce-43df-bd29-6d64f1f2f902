package com.jj.common.utils;

import com.jj.common.constant.Constants;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.xml.sax.InputSource;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import java.io.File;
import java.io.StringReader;
import java.io.UnsupportedEncodingException;
import java.nio.ByteBuffer;
import java.nio.CharBuffer;
import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * String工具类
 */
@Slf4j
public class StringUtil {
    /**
     * 首字母大写
     *
     * @param str
     * @return
     */
    public static String captureName(String str) {
        // 进行字母的ascii编码前移，效率要高于截取字符串进行转换的操作
        char[] cs = str.toCharArray();
        cs[0] -= 32;
        return String.valueOf(cs);
    }

    public static boolean ishttp(String link)
    {
        return StringUtils.startsWithAny(link, Constants.HTTP, Constants.HTTPS);
    }
    /**
     * 忽略下划线后是否全大写
     *
     * @return
     */
    public static boolean beUpperCase(String param) {
        param = Pattern.compile("[\\d]").matcher(param).replaceAll("");
        Pattern pattern = Pattern.compile("[A-Z]*");
        Matcher mc = pattern.matcher(param.replace("_", ""));
        return mc.matches();
    }

    /**
     * 大写转换为_小写 若全部为大写 直接转换为小写,不添加下划线
     * 
     * @param param
     * @return
     */
    public static String camel4Underline(String param) {
        if (beUpperCase(param)) {
            param = param.toLowerCase();
        }
        Pattern pattern = Pattern.compile("[A-Z]");
        if (param == null || param.equals("")) {
            return "";
        }
        StringBuilder builder = new StringBuilder(param);
        Matcher mc = pattern.matcher(param);
        int i = 0;
        while (mc.find()) {
            builder.replace(mc.start() + i, mc.end() + i, "_" + mc.group().toLowerCase());
            i++;
        }

        if ('_' == builder.charAt(0)) {
            builder.deleteCharAt(0);
        }
        return builder.toString();
    }

    /**
     * _字母转换成驼峰
     *
     * @param line
     * @param firstIsUpperCase
     * @return
     */
    public static String underline2Camel(String line, boolean... firstIsUpperCase) {
        String str = "";

        if (StringUtils.isBlank(line)) {
            return str;
        } else {
            StringBuilder sb = new StringBuilder();
            String[] strArr;
            // 不包含下划线，且第二个参数是空的
            if (!line.contains("_") && firstIsUpperCase.length == 0) {
                sb.append(line.substring(0, 1).toLowerCase()).append(line.substring(1));
                str = sb.toString();
            } else if (!line.contains("_") && firstIsUpperCase.length != 0) {
                if (!firstIsUpperCase[0]) {
                    sb.append(line.substring(0, 1).toLowerCase()).append(line.substring(1));
                    str = sb.toString();
                } else {
                    sb.append(line.substring(0, 1).toUpperCase()).append(line.substring(1));
                    str = sb.toString();
                }
            } else if (line.contains("_") && firstIsUpperCase.length == 0) {
                strArr = line.split("_");
                for (String s : strArr) {
                    sb.append(s.substring(0, 1).toUpperCase()).append(s.substring(1));
                }
                str = sb.toString();
                str = str.substring(0, 1).toLowerCase() + str.substring(1);
            } else if (line.contains("_") && firstIsUpperCase.length != 0) {
                strArr = line.split("_");
                for (String s : strArr) {
                    sb.append(s.substring(0, 1).toUpperCase()).append(s.substring(1));
                }
                if (!firstIsUpperCase[0]) {
                    str = sb.toString();
                    str = str.substring(0, 1).toLowerCase() + str.substring(1);
                } else {
                    str = sb.toString();
                }
            }
        }
        return str;
    }

    /**
     * 去除字符串中的null域
     *
     * @param string
     * @return
     * @throws UnsupportedEncodingException
     */
    public static String trimnull(String string) {
        try {
            ArrayList<Byte> list = new ArrayList<Byte>();
            byte[] bytes = string.getBytes("UTF-8");
            for (int i = 0; bytes != null && i < bytes.length; i++) {
                if (0 != bytes[i]) {
                    list.add(bytes[i]);
                }
            }
            byte[] newbytes = new byte[list.size()];
            for (int i = 0; i < list.size(); i++) {
                newbytes[i] = (Byte)list.get(i);
            }
            String str = new String(newbytes, "UTF-8");
            return str;
        } catch (UnsupportedEncodingException e) {
            log.error("", e);
        }
        return "";
    }

    public static boolean isXmlDocument(String rtnMsg) {

        boolean flag = true;
        try {
            DocumentBuilderFactory documentBuilderFactory = DocumentBuilderFactory.newInstance();
            DocumentBuilder builder = documentBuilderFactory.newDocumentBuilder();
            builder.parse(new InputSource(new StringReader(rtnMsg)));
        } catch (Exception e) {
            flag = false;
        }
        return flag;
    }

    public static byte[] getBytes(char[] chars) {
        Charset cs = Charset.forName("UTF-8");
        CharBuffer cb = CharBuffer.allocate(chars.length);
        cb.put(chars);
        cb.flip();
        ByteBuffer bb = cs.encode(cb);
        return bb.array();
    }

    public static char[] getChars(byte[] bytes) {
        Charset cs = Charset.forName("UTF-8");
        ByteBuffer bb = ByteBuffer.allocate(bytes.length);
        bb.put(bytes).flip();
        CharBuffer cb = cs.decode(bb);
        return cb.array();
    }

    public static byte[] charToByte(char c) {
        byte[] b = new byte[2];
        b[0] = (byte)((c & 0xFF00) >> 8);
        b[1] = (byte)(c & 0xFF);
        return b;
    }

    public static char byteToChar(byte[] b) {
        int hi = (b[0] & 0xFF) << 8;
        int lo = b[1] & 0xFF;
        return (char)(hi | lo);
    }

    /**
     * 数据库查询出的path 转换为当前系统path
     * 
     * @param filePath
     * @return
     */
    public static String parsePath(String filePath) {
        filePath = filePath.replaceAll("\\\\", "/");
        String[] split = filePath.split("/");
        String f = "";
        for (String s : split) {
            if (!f.equals("")) {
                if (!s.equals("")) {
                    f += File.separator;
                }
            }
            f += s;
        }
        if (filePath.startsWith("/")) {
            f = File.separator + f;
        }
        return f;
    }

    /**
     * 根据指定长度分割数组
     * 
     * @param array
     * @param num
     * @return
     */
    public static List<List<String>> splitArr(String[] array, int num) {
        int count = array.length % num == 0 ? array.length / num : array.length / num + 1;
        List<List<String>> arrayList = new ArrayList<>();
        for (int i = 0; i < count; i++) {
            int index = i * num;
            List<String> list = new ArrayList<>();
            int j = 0;
            while (j < num && index < array.length) {
                list.add(array[index++]);
                j++;
            }
            arrayList.add(list);
        }
        return arrayList;
    }
}
