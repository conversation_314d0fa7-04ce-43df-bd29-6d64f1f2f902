package com.jj.common.enums;


public enum ContentTypeEnum {


    DEFAULT("default","application/octet-stream"),
    A001(".*", "application/octet-stream"),
    A002(".001", "application/x-001"),
    A003(".301", "application/x-301"),
    <PERSON>004(".323", "text/h323"),
    <PERSON>005(".906", "application/x-906"),
    <PERSON>006(".907", "drawing/907"),
    <PERSON>007(".a11", "application/x-a11"),
    A008(".acp", "audio/x-mei-aac"),
    A009(".ai", "application/postscript"),
    A010(".aif", "audio/aiff"),
    A011(".aifc", "audio/aiff"),
    A012(".aiff", "audio/aiff"),
    A013(".anv", "application/x-anv"),
    A014(".asa", "text/asa"),
    A015(".asf", "video/x-ms-asf"),
    A016(".asp", "text/asp"),
    A017(".asx", "video/x-ms-asf"),
    A018(".au", "audio/basic"),
    A019(".avi", "video/avi"),
    A020(".awf", "application/vnd.adobe.workflow"),
    A021(".biz", "text/xml"),
    A022(".bmp", "application/x-bmp"),
    A023(".bot", "application/x-bot"),
    A024(".c4t", "application/x-c4t"),
    A025(".c90", "application/x-c90"),
    A026(".cal", "application/x-cals"),
    A027(".cat", "application/s-pki.seccat"),
    A028(".cdf", "application/x-netcdf"),
    A029(".cdr", "application/x-cdr"),
    A030(".cel", "application/x-cel"),
    A031(".cer", "application/x-x509-ca-cert"),
    A032(".cg4", "application/x-g4"),
    A033(".cgm", "application/x-cgm"),
    A034(".cit", "application/x-cit"),
    A035(".class", "java/*"),
    A036(".cml", "text/xml"),
    A037(".cmp", "application/x-cmp"),
    A038(".cmx", "application/x-cmx"),
    A039(".cot", "application/x-cot"),
    A040(".crl", "application/pkix-crl"),
    A041(".crt", "application/x-x509-ca-cert"),
    A042(".csi", "application/x-csi"),
    A043(".css", "text/css"),
    A044(".cut", "application/x-cut"),
    A045(".dbf", "application/x-dbf"),
    A046(".dbm", "application/x-dbm"),
    A047(".dbx", "application/x-dbx"),
    A048(".dcd", "text/xml"),
    A049(".dcx", "application/x-dcx"),
    A050(".der", "application/x-x509-ca-cert"),
    A051(".dgn", "application/x-dgn"),
    A052(".dib", "application/x-dib"),
    A053(".dll", "application/x-msdownload"),
    A054(".doc", "application/msword"),
    A055(".dot", "application/msword"),
    A056(".drw", "application/x-drw"),
    A057(".dtd", "text/xml"),
    A058(".dwf", "Model/vnd.dwf"),
    A059(".dwg", "application/x-dwg"),
    A060(".dxb", "application/x-dxb"),
    A061(".dxf", "application/x-dxf"),
    A062(".edn", "application/vnd.adobe.edn"),
    A063(".emf", "application/x-emf"),
    A064(".eml", "message/rfc822"),
    A065(".ent", "text/xml"),
    A066(".epi", "application/x-epi"),
    A067(".eps", "application/x-ps"),
    A068(".etd", "application/x-ebx"),
    A069(".exe", "application/x-msdownload"),
    A070(".fax", "image/fax"),
    A071(".fdf", "application/vnd.fdf"),
    A072(".fif", "application/fractals"),
    A073(".fo", "text/xml"),
    A074(".frm", "application/x-frm"),
    A075(".g4", "application/x-g4"),
    A076(".gbr", "application/x-gbr"),
    A077(".gcd", "application/x-gcd"),
    A078(".gif", "image/gif"),
    A079(".gl2", "application/x-gl2"),
    A080(".gp4", "application/x-gp4"),
    A081(".hgl", "application/x-hgl"),
    A082(".hmr", "application/x-hmr"),
    A083(".hpg", "application/x-hpgl"),
    A084(".hpl", "application/x-hpl"),
    A085(".hqx", "application/mac-binhex40"),
    A086(".hrf", "application/x-hrf"),
    A087(".hta", "application/hta"),
    A088(".htc", "text/x-component"),
    A089(".htm", "text/html"),
    A090(".html", "text/html"),
    A091(".htt", "text/webviewhtml"),
    A092(".htx", "text/html"),
    A093(".icb", "application/x-icb"),
    A094(".ico", "image/x-icon"),
    A095(".iff", "application/x-iff"),
    A096(".ig4", "application/x-g4"),
    A097(".igs", "application/x-igs"),
    A098(".iii", "application/x-iphone"),
    A099(".img", "application/x-img"),
    A100(".ins", "application/x-internet-signup"),
    A101(".isp", "application/x-internet-signup"),
    A102(".IVF", "video/x-ivf"),
    A103(".java", "java/*"),
    A104(".jfif", "image/jpeg"),
    A105(".jpe", "image/jpeg"),
    A106(".jpeg", "image/jpeg"),
    A107(".jpg", "image/jpeg"),
    A108(".js", "application/x-javascript"),
    A109(".jsp", "text/html"),
    A110(".la1", "audio/x-liquid-file"),
    A111(".lar", "application/x-laplayer-reg"),
    A112(".latex", "application/x-latex"),
    A113(".lavs", "audio/x-liquid-secure"),
    A114(".lbm", "application/x-lbm"),
    A115(".lmsff", "audio/x-la-lms"),
    A116(".ls", "application/x-javascript"),
    A117(".ltr", "application/x-ltr"),
    A118(".m1v", "video/x-mpeg"),
    A119(".m2v", "video/x-mpeg"),
    A120(".m3u", "audio/mpegurl"),
    A121(".m4e", "video/mpeg4"),
    A122(".mac", "application/x-mac"),
    A123(".man", "application/x-troff-man"),
    A124(".math", "text/xml"),
    A125(".mdb", "application/x-mdb"),
    A126(".mfp", "application/x-shockwave-flash"),
    A127(".mht", "message/rfc822"),
    A128(".mhtml", "message/rfc822"),
    A129(".mi", "application/x-mi"),
    A130(".mid", "audio/mid"),
    A131(".midi", "audio/mid"),
    A132(".mil", "application/x-mil"),
    A133(".mml", "text/xml"),
    A134(".mnd", "audio/x-musicnet-download"),
    A135(".mns", "audio/x-musicnet-stream"),
    A136(".mocha", "application/x-javascript"),
    A137(".movie", "video/x-sgi-movie"),
    A138(".mp1", "audio/mp1"),
    A139(".mp2", "audio/mp2"),
    A140(".mp2v", "video/mpeg"),
    A141(".mp3", "audio/mp3"),
    A142(".mp4", "video/mp4"),
    A143(".mpa", "video/x-mpg"),
    A144(".mpd", "application/-project"),
    A145(".mpe", "video/x-mpeg"),
    A146(".mpeg", "video/mpg"),
    A147(".mpg", "video/mpg"),
    A148(".mpga", "audio/rn-mpeg"),
    A149(".mpp", "application/-project"),
    A150(".mps", "video/x-mpeg"),
    A151(".mpt", "application/-project"),
    A152(".mpv", "video/mpg"),
    A153(".mpv2", "video/mpeg"),
    A154(".mpw", "application/s-project"),
    A155(".mpx", "application/-project"),
    A156(".mtx", "text/xml"),
    A157(".mxp", "application/x-mmxp"),
    A158(".net", "image/pnetvue"),
    A159(".nrf", "application/x-nrf"),
    A160(".nws", "message/rfc822"),
    A161(".odc", "text/x-ms-odc"),
    A162(".out", "application/x-out"),
    A163(".p10", "application/pkcs10"),
    A164(".p12", "application/x-pkcs12"),
    A165(".p7b", "application/x-pkcs7-certificates"),
    A166(".p7c", "application/pkcs7-mime"),
    A167(".p7m", "application/pkcs7-mime"),
    A168(".p7r", "application/x-pkcs7-certreqresp"),
    A169(".p7s", "application/pkcs7-signature"),
    A170(".pc5", "application/x-pc5"),
    A171(".pci", "application/x-pci"),
    A172(".pcl", "application/x-pcl"),
    A173(".pcx", "application/x-pcx"),
    A174(".pdf", "application/pdf"),
    A175(".pdx", "application/vnd.adobe.pdx"),
    A176(".pfx", "application/x-pkcs12"),
    A177(".pgl", "application/x-pgl"),
    A178(".pic", "application/x-pic"),
    A179(".pko", "application-pki.pko"),
    A180(".pl", "application/x-perl"),
    A181(".plg", "text/html"),
    A182(".pls", "audio/scpls"),
    A183(".plt", "application/x-plt"),
    A184(".png", "image/png"),
    A185(".pot", "applications-powerpoint"),
    A186(".ppa", "application/vs-powerpoint"),
    A187(".ppm", "application/x-ppm"),
    A188(".pps", "application-powerpoint"),
    A189(".ppt", "applications-powerpoint"),
    A190(".pr", "application/x-pr"),
    A191(".prf", "application/pics-rules"),
    A192(".prn", "application/x-prn"),
    A193(".prt", "application/x-prt"),
    A194(".ps", "application/postscript"),
    A195(".ptn", "application/x-ptn"),
    A196(".pwz", "application/powerpoint"),
    A197(".r3t", "text/vnd.rn-realtext3d"),
    A198(".ra", "audio/vnd.rn-realaudio"),
    A199(".rlc", "application/x-rlc"),
    A200(".rle", "application/x-rle"),
    A201(".rm", "application/vnd.rn-realmedia"),
    A202(".rmf", "application/vnd.adobe.rmf"),
    A203(".rmi", "audio/mid"),
    A204(".rmj", "application/vnd.rn-realsystem-rmj"),
    A205(".rmm", "audio/x-pn-realaudio"),
    A206(".rmp", "application/vnd.rn-rn_music_package"),
    A207(".rms", "application/vnd.rn-realmedia-secure"),
    A208(".rmvb", "application/vnd.rn-realmedia-vbr"),
    A209(".rmx", "application/vnd.rn-realsystem-rmx"),
    A210(".rnx", "application/vnd.rn-realplayer"),
    A211(".rp", "image/vnd.rn-realpix"),
    A212(".rpm", "audio/x-pn-realaudio-plugin"),
    A213(".rsml", "application/vnd.rn-rsml"),
    A214(".rt", "text/vnd.rn-realtext"),
    A215(".rtf", "application/x-rtf"),
    A216(".rv", "video/vnd.rn-realvideo"),
    A217(".sam", "application/x-sam"),
    A218(".sat", "application/x-sat"),
    A219(".sdp", "application/sdp"),
    A220(".sdw", "application/x-sdw"),
    A221(".sit", "application/x-stuffit"),
    A222(".slb", "application/x-slb"),
    A223(".sld", "application/x-sld"),
    A224(".slk", "drawing/x-slk"),
    A225(".smi", "application/smil"),
    AA226(".smil", "application/smil"),
    A227(".smk", "application/x-smk"),
    v228(".snd", "audio/basic"),
    A229(".sol", "text/plain"),
    A230(".sor", "text/plain"),
    A231(".spc", "application/x-pkcs7-certificates"),
    A232(".spl", "application/futuresplash"),
    A233(".spp", "text/xml"),
    A234(".ssm", "application/streamingmedia"),
    A235(".sst", "application-pki.certstore"),
    A236(".stl", "application/-pki.stl"),
    A237(".stm", "text/html"),
    A238(".sty", "application/x-sty"),
    A239(".svg", "text/xml"),
    A240(".swf", "application/x-shockwave-flash"),
    A241(".tdf", "application/x-tdf"),
    A242(".tg4", "application/x-tg4"),
    A243(".tga", "application/x-tga"),
    A244(".tif", "image/tiff"),
    A245(".tiff", "image/tiff"),
    A246(".tld", "text/xml"),
    A247(".top", "drawing/x-top"),
    A248(".torrent", "application/x-bittorrent"),
    A249(".tsd", "text/xml"),
    A250(".txt", "text/plain"),
    A251(".uin", "application/x-icq"),
    A252(".uls", "text/iuls"),
    A253(".vcf", "text/x-vcard"),
    A254(".vda", "application/x-vda"),
    A255(".vdx", "application/vnd.visio"),
    A256(".vml", "text/xml"),
    A257(".vpg", "application/x-vpeg005"),
    A258(".vsd", "application/x-vsd"),
    A259(".vss", "application/vnd.visio"),
    A260(".vst", "application/x-vst"),
    A261(".vsw", "application/vnd.visio"),
    A262(".vsx", "application/vnd.visio"),
    A263(".vtx", "application/vnd.visio"),
    A264(".vxml", "text/xml"),
    A265(".wav", "audio/wav"),
    A266(".wax", "audio/x-ms-wax"),
    A267(".wb1", "application/x-wb1"),
    A268(".wb2", "application/x-wb2"),
    A269(".wb3", "application/x-wb3"),
    A270(".wbmp", "image/vnd.wap.wbmp"),
    A271(".wiz", "application/msword"),
    A272(".wk3", "application/x-wk3"),
    A273(".wk4", "application/x-wk4"),
    A274(".wkq", "application/x-wkq"),
    A275(".wks", "application/x-wks"),
    A276(".wm", "video/x-ms-wm"),
    A277(".wma", "audio/x-ms-wma"),
    A278(".wmd", "application/x-ms-wmd"),
    A279(".wmf", "application/x-wmf"),
    A280(".wml", "text/vnd.wap.wml"),
    A281(".wmv", "video/x-ms-wmv"),
    A282(".wmx", "video/x-ms-wmx"),
    A283(".wmz", "application/x-ms-wmz"),
    A284(".wp6", "application/x-wp6"),
    A285(".wpd", "application/x-wpd"),
    A286(".wpg", "application/x-wpg"),
    A287(".wpl", "application/-wpl"),
    A288(".wq1", "application/x-wq1"),
    A289(".wr1", "application/x-wr1"),
    A290(".wri", "application/x-wri"),
    A291(".wrk", "application/x-wrk"),
    A292(".ws", "application/x-ws"),
    A293(".ws2", "application/x-ws"),
    A294(".wsc", "text/scriptlet"),
    A295(".wsdl", "text/xml"),
    A296(".wvx", "video/x-ms-wvx"),
    A297(".xdp", "application/vnd.adobe.xdp"),
    A298(".xdr", "text/xml"),
    A299(".xfd", "application/vnd.adobe.xfd"),
    A300(".xfdf", "application/vnd.adobe.xfdf"),
    A301(".xhtml", "text/html"),
    A302(".xls", "application/x-xls"),
    A303(".xlw", "application/x-xlw"),
    A304(".xml", "text/xml"),
    A305(".xpl", "audio/scpls"),
    A306(".xq", "text/xml"),
    A307(".xql", "text/xml"),
    A308(".xquery", "text/xml"),
    A309(".xsd", "text/xml"),
    A310(".xsl", "text/xml"),
    A311(".xslt", "text/xml"),
    A312(".xwd", "application/x-xwd"),
    A313(".x_b", "application/x-x_b"),
    A314(".x_t", "application/x-x_t"),
    A315(".ram", "audio/x-pn-realaudio"),
    A316(".ras", "application/x-ras"),
    A317(".rat", "application/rat-file"),
    A318(".rdf", "text/xml"),
    A319(".rec", "application/vnd.rn-recording"),
    A320(".red", "application/x-red"),
    A321(".rgb", "application/x-rgb"),
    A322(".rjs", "application/vnd.rn-realsystem-rjs"),
    A323(".rjt", "application/vnd.rn-realsystem-rjt"),
    ;

    
    private String key;
    
    private String value;
    
    ContentTypeEnum(String key , String value){
        this.key = key;
        this.value = value;
    }

    public static ContentTypeEnum getContentTypeEnumByKey(String key){

        ContentTypeEnum[] values = values();
        for (ContentTypeEnum value : values){

            if (value.key.equals(key)){
                return value;
            }
        }
        return null;
    }

    public String getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }
}
