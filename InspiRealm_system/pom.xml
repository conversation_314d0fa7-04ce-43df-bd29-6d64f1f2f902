<?xml version="1.0" encoding="UTF-8"?>

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.jj</groupId>
    <artifactId>InspiRealm_system</artifactId>
    <packaging>pom</packaging>
    <version>1.0-SNAPSHOT</version>
    <modules>
        <module>InspiRealm_system-main</module>
        <module>InspiRealm_system-common</module>
        <module>InspiRealm_system-dao</module>
        <module>InspiRealm_system-web</module>
        <module>InspiRealm_system-shared</module>
        <module>InspiRealm_system-integration</module>
        <module>InspiRealm_system-core</module>
    </modules>

    <name>InspiRealm_system</name>
    <!-- FIXME change it to the project's website -->
    <url>http://maven.apache.org/</url>
    <inceptionYear>2001</inceptionYear>


    <!-- global config -->
    <properties>
        <facade.version>1.0-SNAPSHOT</facade.version>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
        <java.version>1.8</java.version>
        <springboot.version>2.3.12.RELEASE</springboot.version>
        <spring-boot-maven-plugin.version>2.6.2</spring-boot-maven-plugin.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <!--定义lombok的jar包版本号-->
        <lombok.version>1.18.10</lombok.version>
        <commons-beanutils.version>1.9.4</commons-beanutils.version>
        <commons-configuration2.version>2.7</commons-configuration2.version>
        <commons-configuration.version>1.10</commons-configuration.version>
        <commons-io.version>2.6</commons-io.version>
        <hutool.version>5.8.25</hutool.version>
        <!--mybatis-plus的jar包版本号-->
        <mybatis.plus.version>3.4.0</mybatis.plus.version>
        <velocity-engine-core.version>2.1</velocity-engine-core.version>
        <!--数据库连接jar包版本号-->
        <mysql-connector.version>8.0.33</mysql-connector.version>
        <druid.version>1.1.10</druid.version>
        <swagger.version>2.9.2</swagger.version>
        <swagger.ui.version>2.9.2</swagger.ui.version>
        <swagger.bootstrap.ui.version>1.9.1</swagger.bootstrap.ui.version>
        <poi.version>4.1.2</poi.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-mail</artifactId>
                <version>2.3.12.RELEASE</version>
            </dependency>
            <dependency>
                <groupId>technology.tabula</groupId>
                <artifactId>tabula</artifactId>
                <version>1.0.5</version>
                <exclusions>
                    <exclusion>
                        <artifactId>slf4j-simple</artifactId>
                        <groupId>org.slf4j</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-jdbc</artifactId>
                <version>${springboot.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-data-mongodb</artifactId>
                <version>${springboot.version}</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-all</artifactId>
                <version>4.1.20.Final</version>
            </dependency>
            <dependency>
                <groupId>com.github.javaparser</groupId>
                <artifactId>javaparser-core</artifactId>
                <version>3.25.1</version>
            </dependency>
            <dependency>
                <groupId>commons-fileupload</groupId>
                <artifactId>commons-fileupload</artifactId>
                <version>1.4</version>
            </dependency>
            <dependency>
                <groupId>io.minio</groupId>
                <artifactId>minio</artifactId>
                <version>8.2.1</version>
                <scope>compile</scope>
            </dependency>
            <!-- excel工具 -->
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>${poi.version}</version>
            </dependency>
            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>dysmsapi20170525</artifactId>
                <version>2.0.24</version>
            </dependency>
            <!--json解析器-->
            <dependency>
                <groupId>com.jayway.jsonpath</groupId>
                <artifactId>json-path</artifactId>
                <version>2.4.0</version>
            </dependency>
            <dependency>
                <groupId>com.github.jsqlparser</groupId>
                <artifactId>jsqlparser</artifactId>
                <version>4.6</version>
            </dependency>
            <!--规则引擎-->
            <dependency>
                <groupId>com.googlecode.aviator</groupId>
                <artifactId>aviator</artifactId>
                <version>5.3.3</version>
            </dependency>
            <!-- Spring Boot Validation Starter (包含JSR-303/JSR-380支持) -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-validation</artifactId>
                <version>${springboot.version}</version>
            </dependency>

            <dependency>
                <groupId>p6spy</groupId>
                <artifactId>p6spy</artifactId>
                <version>3.9.1</version>
            </dependency>
            <dependency>
                <groupId>commons-configuration</groupId>
                <artifactId>commons-configuration</artifactId>
                <version>${commons-configuration.version}</version>
            </dependency>

            <dependency>
                <groupId>commons-beanutils</groupId>
                <artifactId>commons-beanutils</artifactId>
                <version>${commons-beanutils.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${springboot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.alibaba.spring</groupId>
                <artifactId>spring-context-support</artifactId>
                <version>1.0.11</version>
            </dependency>

            <dependency>
                <groupId>com.jj</groupId>
                <artifactId>InspiRealm_system-integration</artifactId>
                <version>${facade.version}</version>
            </dependency>

            <dependency>
                <groupId>com.jj</groupId>
                <artifactId>InspiRealm_system-core</artifactId>
                <version>${facade.version}</version>
            </dependency>

            <dependency>
                <groupId>com.jj</groupId>
                <artifactId>InspiRealm_system-shared</artifactId>
                <version>${facade.version}</version>
            </dependency>

            <dependency>
                <groupId>com.jj</groupId>
                <artifactId>InspiRealm_system-dao</artifactId>
                <version>${facade.version}</version>
            </dependency>

            <dependency>
                <groupId>com.jj</groupId>
                <artifactId>InspiRealm_system-web</artifactId>
                <version>${facade.version}</version>
            </dependency>

            <dependency>
                <groupId>com.jj</groupId>
                <artifactId>InspiRealm_system-common</artifactId>
                <version>${facade.version}</version>
            </dependency>

            <!--设置日志jar包-->
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
            </dependency>
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>slf4j-api</artifactId>
                <version>1.7.30</version>
            </dependency>
            <!--引入mysql连接依赖包-->
            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>${mysql-connector.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.velocity</groupId>
                <artifactId>velocity-engine-core</artifactId>
                <version>${velocity-engine-core.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid</artifactId>
                <version>${druid.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid-spring-boot-starter</artifactId>
                <version>${druid.version}</version>
            </dependency>
            <!-- mp 依赖 -->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatis.plus.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpclient</artifactId>
                <version>4.5.13</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-web</artifactId>
                <version>${springboot.version}</version>
            </dependency>
            <!--aop依赖导入-->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-aop</artifactId>
                <version>${springboot.version}</version>
            </dependency>
            <!--打包不参与，也就是打包去掉tomcat-->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-tomcat</artifactId>
                <scope>provided</scope>
            </dependency>

            <!--springboot集成redis的依赖-->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-data-redis</artifactId>
                <version>2.3.5.RELEASE</version>
            </dependency>

            <!-- Swagger2 API接口调试工具-->
            <dependency>
                <groupId>io.springfox</groupId>
                <artifactId>springfox-swagger2</artifactId>
                <version>${swagger.version}</version>
            </dependency>
            <!-- 引入swagger-bootstrap-ui包/doc.html-->
            <dependency>
                <groupId>com.github.xiaoymin</groupId>
                <artifactId>swagger-bootstrap-ui</artifactId>
                <version>${swagger.bootstrap.ui.version}</version>
            </dependency>

            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
            </dependency>

            <!--jackson-->
            <dependency>
                <groupId>com.fasterxml.jackson.datatype</groupId>
                <artifactId>jackson-datatype-jsr310</artifactId>
                <version>2.9.7</version>
            </dependency>

            <!--hutool-->
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.version}</version>
            </dependency>

            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>${commons-io.version}</version>
            </dependency>
        </dependencies>

    </dependencyManagement>


    <!-- 构件管理 -->
    <distributionManagement>
        <!-- release仓库 -->
        <repository>
            <id>releases</id>
            <name>Releases</name>
            <url>http://maven.jjxmz.cn/nexus/content/repositories/releases/</url>
        </repository>
        <!-- snapshot仓库 版本尾缀为-SNAPSHOT的组件会从该地址上传或下载 -->
        <snapshotRepository>
            <id>snapshots</id>
            <name>Snapshots</name>
            <url>http://maven.jjxmz.cn/nexus/content/repositories/snapshots/</url>
        </snapshotRepository>
    </distributionManagement>


    <!--  &lt;!&ndash; 构建管理 &ndash;&gt;-->
    <!--  <build>-->
    <!--    &lt;!&ndash; 插件 &ndash;&gt;-->
    <!--    <plugins>-->
    <!--      &lt;!&ndash; 编译插件 &ndash;&gt;-->
    <!--      <plugin>-->
    <!--        <groupId>org.apache.maven.plugins</groupId>-->
    <!--        <artifactId>maven-compiler-plugin</artifactId>-->
    <!--        <version>3.3</version>-->
    <!--        <configuration>-->
    <!--          <compilerVersion>${java.version}</compilerVersion>-->
    <!--          <source>${java.version}</source>-->
    <!--          <target>${java.version}</target>-->
    <!--          <encoding>UTF-8</encoding>-->
    <!--          &lt;!&ndash; prevents endPosTable exception for maven compile &ndash;&gt;-->
    <!--          <useIncrementalCompilation>false</useIncrementalCompilation>-->
    <!--        </configuration>-->
    <!--      </plugin>-->
    <!--      &lt;!&ndash; 打包源码插件,打包完成后会生成一份源码.jar &ndash;&gt;-->
    <!--      <plugin>-->
    <!--        <groupId>org.apache.maven.plugins</groupId>-->
    <!--        <artifactId>maven-source-plugin</artifactId>-->
    <!--        <version>2.4</version>-->
    <!--        <executions>-->
    <!--          <execution>-->
    <!--            <id>attach-sources</id>-->
    <!--            <goals>-->
    <!--              <goal>jar</goal>-->
    <!--            </goals>-->
    <!--          </execution>-->
    <!--        </executions>-->
    <!--      </plugin>-->
    <!--      &lt;!&ndash;-->
    <!--          pom精简插件:-->
    <!--          将pom.xml压缩为flattened-pom.xml,然后在install和deploy阶段使用flattened-pom.xml替代pom.xml.-->
    <!--          具体压缩策略如下:-->
    <!--          1.和构建有关的元素会被删除;-->
    <!--          2.和开发有关的元素默认会被删除;-->
    <!--          3.只包含构件的使用者必须的一些信息;-->
    <!--          4.变量会被解析;-->
    <!--          5.上级关系会被解析,然后被压缩删除;-->
    <!--          6.构建时实际使用的profile会被评估,视情况处理;-->
    <!--          7.由JDK或者OS驱动的profile会被保留,需要时可以动态地控制依赖.-->
    <!--          更多请查看http://www.mojohaus.org/flatten-maven-plugin/plugin-info.html-->
    <!--      &ndash;&gt;-->
    <!--      <plugin>-->
    <!--        <groupId>org.codehaus.mojo</groupId>-->
    <!--        <artifactId>flatten-maven-plugin</artifactId>-->
    <!--        <version>1.0.0</version>-->
    <!--        <executions>-->
    <!--          <execution>-->
    <!--            <phase>process-resources</phase>-->
    <!--            <goals>-->
    <!--              <goal>flatten</goal>-->
    <!--            </goals>-->
    <!--          </execution>-->
    <!--          <execution>-->
    <!--            <id>flatten.clean</id>-->
    <!--            <phase>clean</phase>-->
    <!--            <goals>-->
    <!--              <goal>clean</goal>-->
    <!--            </goals>-->
    <!--          </execution>-->
    <!--        </executions>-->
    <!--      </plugin>-->
    <!--      <plugin>-->
    <!--        <groupId>org.apache.maven.plugins</groupId>-->
    <!--        <artifactId>maven-archetype-plugin</artifactId>-->
    <!--        <version>3.0.1</version>-->
    <!--      </plugin>-->
    <!--    </plugins>-->
    <!--  </build>-->
    <!-- 构建管理 -->
    <build>
        <!-- 插件 -->
        <plugins>
            <!-- 编译插件 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.3</version>
                <configuration>
                    <compilerVersion>${java.version}</compilerVersion>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <encoding>UTF-8</encoding>
                    <!-- prevents endPosTable exception for maven compile -->
                    <useIncrementalCompilation>false</useIncrementalCompilation>
                </configuration>
            </plugin>
            <!-- 打包源码插件,打包完成后会生成一份源码.jar -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <version>2.4</version>
                <executions>
                    <execution>
                        <id>attach-sources</id>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <!--
                pom精简插件:
                将pom.xml压缩为flattened-pom.xml,然后在install和deploy阶段使用flattened-pom.xml替代pom.xml.
                具体压缩策略如下:
                1.和构建有关的元素会被删除;
                2.和开发有关的元素默认会被删除;
                3.只包含构件的使用者必须的一些信息;
                4.变量会被解析;
                5.上级关系会被解析,然后被压缩删除;
                6.构建时实际使用的profile会被评估,视情况处理;
                7.由JDK或者OS驱动的profile会被保留,需要时可以动态地控制依赖.
                更多请查看http://www.mojohaus.org/flatten-maven-plugin/plugin-info.html
            -->
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
                <version>1.0.0</version>
                <executions>
                    <execution>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>flatten</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>flatten.clean</id>
                        <phase>clean</phase>
                        <goals>
                            <goal>clean</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-archetype-plugin</artifactId>
                <version>3.0.1</version>
            </plugin>

        </plugins>
    </build>

</project>
