import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import javax.validation.ValidatorFactory;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.Set;

/**
 * Bean Validation测试类
 * 用于验证@NotBlank等注解是否正常工作
 */
public class ValidationTest {
    
    public static class TestDTO {
        @NotBlank(message = "用户名不能为空")
        @Size(max = 50, message = "用户名不能超过50个字符")
        private String username;
        
        @NotBlank(message = "密码不能为空")
        private String password;
        
        public TestDTO(String username, String password) {
            this.username = username;
            this.password = password;
        }
        
        public String getUsername() { return username; }
        public String getPassword() { return password; }
    }
    
    public static void main(String[] args) {
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        
        // 测试1: 空用户名
        TestDTO test1 = new TestDTO("", "123456");
        Set<ConstraintViolation<TestDTO>> violations1 = validator.validate(test1);
        System.out.println("测试1 - 空用户名:");
        for (ConstraintViolation<TestDTO> violation : violations1) {
            System.out.println("  " + violation.getMessage());
        }
        
        // 测试2: 空密码
        TestDTO test2 = new TestDTO("admin", "");
        Set<ConstraintViolation<TestDTO>> violations2 = validator.validate(test2);
        System.out.println("测试2 - 空密码:");
        for (ConstraintViolation<TestDTO> violation : violations2) {
            System.out.println("  " + violation.getMessage());
        }
        
        // 测试3: 正常数据
        TestDTO test3 = new TestDTO("admin", "123456");
        Set<ConstraintViolation<TestDTO>> violations3 = validator.validate(test3);
        System.out.println("测试3 - 正常数据:");
        if (violations3.isEmpty()) {
            System.out.println("  验证通过！");
        } else {
            for (ConstraintViolation<TestDTO> violation : violations3) {
                System.out.println("  " + violation.getMessage());
            }
        }
        
        factory.close();
    }
}
