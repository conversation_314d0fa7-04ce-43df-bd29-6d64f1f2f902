package com.jj.dao.model.user;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.util.Date;

/**
 * Web端用户实体类
 *
 * <AUTHOR>
 * @date 2025-01-05
 */
@Data
@TableName("web_user")
public class WebUser {

    /**
     * 用户ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户名
     */
    @TableField("username")
    private String username;

    /**
     * 邮箱地址
     */
    @TableField("email")
    private String email;

    /**
     * 密码哈希值
     */
    @TableField("password_hash")
    private String passwordHash;

    /**
     * 用户昵称
     */
    @TableField("nickname")
    private String nickname;

    /**
     * 头像URL
     */
    @TableField("avatar_url")
    private String avatarUrl;

    /**
     * 手机号
     */
    @TableField("phone")
    private String phone;

    /**
     * 性别 0:未知 1:男 2:女
     */
    @TableField("gender")
    private Integer gender;

    /**
     * 生日
     */
    @TableField("birthday")
    private Date birthday;

    /**
     * 国家
     */
    @TableField("country")
    private String country;

    /**
     * 省份
     */
    @TableField("province")
    private String province;

    /**
     * 城市
     */
    @TableField("city")
    private String city;

    /**
     * 用户类型 1:普通用户 2:VIP用户 3:企业用户
     */
    @TableField("user_type")
    private Integer userType;

    /**
     * 状态 0:禁用 1:正常 2:待验证
     */
    @TableField("status")
    private Integer status;

    /**
     * 邮箱验证状态 0:未验证 1:已验证
     */
    @TableField("email_verified")
    private Integer emailVerified;

    /**
     * 最后登录时间
     */
    @TableField("last_login_time")
    private Date lastLoginTime;

    /**
     * 最后登录IP
     */
    @TableField("last_login_ip")
    private String lastLoginIp;

    /**
     * 登录次数
     */
    @TableField("login_count")
    private Integer loginCount;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /**
     * 逻辑删除 0:未删除 1:已删除
     */
    @TableLogic
    @TableField("del")
    private Integer del;

    /**
     * 乐观锁版本号
     */
    @Version
    @TableField("version")
    private Integer version;
}