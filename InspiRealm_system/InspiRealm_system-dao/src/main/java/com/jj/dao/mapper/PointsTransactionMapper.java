package com.jj.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jj.dao.model.points.PointsTransaction;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 积分流水Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-07-29
 */
@Mapper
public interface PointsTransactionMapper extends BaseMapper<PointsTransaction> {
    
    /**
     * 分页查询用户积分流水
     * 
     * @param page 分页参数
     * @param userId 用户ID
     * @param transactionType 交易类型
     * @return 积分流水分页数据
     */
    IPage<PointsTransaction> selectUserTransactions(Page<PointsTransaction> page, 
                                                   @Param("userId") Long userId, 
                                                   @Param("transactionType") Integer transactionType);
    
    /**
     * 查询用户积分流水列表
     * 
     * @param userId 用户ID
     * @param transactionType 交易类型
     * @param limit 限制条数
     * @return 积分流水列表
     */
    List<PointsTransaction> selectUserTransactionsList(@Param("userId") Long userId, 
                                                      @Param("transactionType") Integer transactionType, 
                                                      @Param("limit") Integer limit);
    
    /**
     * 统计用户积分流水
     * 
     * @param userId 用户ID
     * @param transactionType 交易类型
     * @return 流水总数
     */
    Long countUserTransactions(@Param("userId") Long userId, 
                              @Param("transactionType") Integer transactionType);
}
