package com.jj.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jj.dao.model.image.ProcessedImage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 处理后图片Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-07-29
 */
@Mapper
public interface ProcessedImageMapper extends BaseMapper<ProcessedImage> {
    
    /**
     * 根据任务ID查询图片
     * 
     * @param taskId 任务ID
     * @return 图片信息
     */
    ProcessedImage selectByTaskId(@Param("taskId") Long taskId);
    
    /**
     * 分页查询用户图片
     * 
     * @param page 分页参数
     * @param userId 用户ID
     * @param isPublic 是否公开
     * @return 图片分页数据
     */
    IPage<ProcessedImage> selectUserImages(Page<ProcessedImage> page, 
                                          @Param("userId") Long userId, 
                                          @Param("isPublic") Integer isPublic);
    
    /**
     * 分页查询公开图片
     * 
     * @param page 分页参数
     * @param processType 处理类型
     * @param aspectRatio 图片比例
     * @return 图片分页数据
     */
    IPage<ProcessedImage> selectPublicImages(Page<ProcessedImage> page, 
                                            @Param("processType") String processType, 
                                            @Param("aspectRatio") String aspectRatio);
    
    /**
     * 查询用户图片列表
     * 
     * @param userId 用户ID
     * @param isPublic 是否公开
     * @param limit 限制条数
     * @return 图片列表
     */
    List<ProcessedImage> selectUserImagesList(@Param("userId") Long userId, 
                                             @Param("isPublic") Integer isPublic, 
                                             @Param("limit") Integer limit);
    
    /**
     * 更新图片公开状态
     * 
     * @param id 图片ID
     * @param isPublic 是否公开
     * @param userId 用户ID（权限验证）
     * @return 更新行数
     */
    int updateImagePublicStatus(@Param("id") Long id, 
                               @Param("isPublic") Integer isPublic, 
                               @Param("userId") Long userId);
    
    /**
     * 增加图片查看数
     * 
     * @param id 图片ID
     * @return 更新行数
     */
    int incrementViewCount(@Param("id") Long id);
    
    /**
     * 增加图片点赞数
     * 
     * @param id 图片ID
     * @return 更新行数
     */
    int incrementLikeCount(@Param("id") Long id);
    
    /**
     * 减少图片点赞数
     * 
     * @param id 图片ID
     * @return 更新行数
     */
    int decrementLikeCount(@Param("id") Long id);
    
    /**
     * 统计用户图片数量
     *
     * @param userId 用户ID
     * @param isPublic 是否公开
     * @return 图片数量
     */
    Long countUserImages(@Param("userId") Long userId, @Param("isPublic") Integer isPublic);

    /**
     * 根据任务ID删除图片（逻辑删除）
     *
     * @param taskId 任务ID
     * @return 删除行数
     */
    int deleteByTaskId(@Param("taskId") Long taskId);

    /**
     * 根据任务ID列表查询图片
     *
     * @param taskIds 任务ID列表
     * @return 图片列表
     */
    List<ProcessedImage> selectByTaskIds(@Param("taskIds") List<Long> taskIds);

    /**
     * 软删除图片
     *
     * @param id 图片ID
     * @return 更新行数
     */
    int softDeleteImage(@Param("id") Long id);
}
