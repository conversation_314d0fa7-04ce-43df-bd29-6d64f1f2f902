package com.jj.dao.model.image;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.util.Date;

/**
 * 图片处理任务实体类
 * 
 * <AUTHOR>
 * @date 2024-07-29
 */
@Data
@TableName("image_process_task")
public class ImageProcessTask {
    
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    /**
     * 用户ID
     */
    @TableField("user_id")
    private Long userId;
    
    /**
     * 原始图片URL
     */
    @TableField("original_image_url")
    private String originalImageUrl;
    
    /**
     * 处理类型
     */
    @TableField("process_type")
    private String processType;


    /**
     * 任务编号
     */
    @TableField("task_no")  // 可以存储在process_params中
    private String taskNo;

    /**
     * 提示词
     */
    @TableField("prompt")  // 可以存储在process_params中
    private String prompt;

    /**
     * 原始图片Base64
     */
    @TableField("original_image_base64")  // 可以存储在original_image_url中
    private String originalImageBase64;

    /**
     * 宽高比
     */
    @TableField("aspect_ratio")  // 可以存储在process_params中
    private String aspectRatio;

    /**
     * 文本内容
     */
    @TableField("text_content")  // 可以存储在process_params中
    private String textContent;

    /**
     * 样式类型
     */
    @TableField("style_type")
    private String styleType;

    /**
     * 积分消耗
     */
    @TableField("points_cost")  // 可以存储在process_params中
    private Integer pointsCost;

    /**
     * 进度
     */
    @TableField("progress")  // 可以通过status计算
    private Integer progress;
    
    /**
     * 任务状态 1:待处理 2:处理中 3:处理成功 4:处理失败
     */
    @TableField("status")
    private Integer status;

    /**
     * 错误信息
     */
    @TableField("error_message")
    private String errorMessage;
    
    /**
     * 开始处理时间
     */
    @TableField("start_time")
    private Date startTime;

    /**
     * 结束时间（兼容字段）
     */
    @TableField("end_time")
    private Date endTime;
    
    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;
    
    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
    
    /**
     * 逻辑删除 0:未删除 1:已删除
     */
    @TableLogic
    @TableField("del")
    private Integer del;
    
    /**
     * 乐观锁版本号
     */
    @Version
    @TableField("version")
    private Integer version;
}
