package com.jj.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jj.dao.model.admin.AdminUser;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;

/**
 * 管理员用户Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-07-29
 */
@Mapper
public interface AdminUserMapper extends BaseMapper<AdminUser> {
    
    /**
     * 根据用户名查询管理员
     * 
     * @param username 用户名
     * @return 管理员信息
     */
    AdminUser selectByUsername(@Param("username") String username);
    
    /**
     * 更新最后登录信息
     *
     * @param id 管理员ID
     * @param lastLoginTime 最后登录时间
     * @param lastLoginIp 登录IP
     * @return 更新行数
     */
    int updateLastLoginInfo(@Param("id") Long id,
                           @Param("lastLoginTime") Date lastLoginTime,
                           @Param("lastLoginIp") String lastLoginIp);
}
