package com.jj.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jj.dao.model.user.WebUser;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * Web端用户Mapper接口
 *
 * <AUTHOR>
 * @date 2025-01-05
 */
@Mapper
public interface WebUserMapper extends BaseMapper<WebUser> {

    /**
     * 根据邮箱查询用户信息
     *
     * @param email 邮箱地址
     * @return 用户信息
     */
    WebUser selectByEmail(@Param("email") String email);

    /**
     * 根据用户名查询用户信息
     *
     * @param username 用户名
     * @return 用户信息
     */
    WebUser selectByUsername(@Param("username") String username);

    /**
     * 检查邮箱是否已存在
     *
     * @param email 邮箱地址
     * @return 是否存在
     */
    boolean existsByEmail(@Param("email") String email);

    /**
     * 检查用户名是否已存在
     *
     * @param username 用户名
     * @return 是否存在
     */
    boolean existsByUsername(@Param("username") String username);

    /**
     * 更新用户最后登录信息
     *
     * @param userId 用户ID
     * @param loginIp 登录IP
     * @return 更新行数
     */
    int updateLastLoginInfo(@Param("userId") Long userId, @Param("loginIp") String loginIp);
}