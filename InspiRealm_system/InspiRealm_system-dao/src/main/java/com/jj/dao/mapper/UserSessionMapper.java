package com.jj.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jj.dao.model.user.UserSession;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户会话Mapper接口
 *
 * <AUTHOR>
 * @date 2025-01-05
 */
@Mapper
public interface UserSessionMapper extends BaseMapper<UserSession> {

    /**
     * 根据访问令牌查询会话
     *
     * @param accessToken 访问令牌
     * @return 会话信息
     */
    UserSession selectByAccessToken(@Param("accessToken") String accessToken);

    /**
     * 根据刷新令牌查询会话
     *
     * @param refreshToken 刷新令牌
     * @return 会话信息
     */
    UserSession selectByRefreshToken(@Param("refreshToken") String refreshToken);

    /**
     * 根据会话ID查询会话
     *
     * @param sessionId 会话ID
     * @return 会话信息
     */
    UserSession selectBySessionId(@Param("sessionId") String sessionId);

    /**
     * 查询用户的活跃会话列表
     *
     * @param userId 用户ID
     * @return 会话列表
     */
    List<UserSession> selectActiveSessionsByUserId(@Param("userId") Long userId);

    /**
     * 删除用户的所有会话
     *
     * @param userId 用户ID
     * @return 删除行数
     */
    int deleteByUserId(@Param("userId") Long userId);

    /**
     * 删除过期的会话
     *
     * @return 删除行数
     */
    int deleteExpiredSessions();

    /**
     * 更新会话最后活跃时间
     *
     * @param sessionId 会话ID
     * @return 更新行数
     */
    int updateLastActiveTime(@Param("sessionId") String sessionId);

    /**
     * 使会话失效
     *
     * @param sessionId 会话ID
     * @return 更新行数
     */
    int invalidateSession(@Param("sessionId") String sessionId);
}