package com.jj.dao.model.admin;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.util.Date;

/**
 * 管理员用户实体类
 * 
 * <AUTHOR>
 * @date 2024-07-29
 */
@Data
@TableName("admin_user")
public class AdminUser {
    
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    /**
     * 用户名
     */
    @TableField("username")
    private String username;
    
    /**
     * 密码（加密后）
     */
    @TableField("password")
    private String password;
    
    /**
     * 真实姓名
     */
    @TableField("real_name")
    private String realName;
    
    /**
     * 邮箱
     */
    @TableField("email")
    private String email;
    
    /**
     * 手机号
     */
    @TableField("phone")
    private String phone;
    
    /**
     * 角色 1:超级管理员 2:普通管理员
     */
    @TableField("role")
    private Integer role;
    
    /**
     * 状态 0:禁用 1:正常
     */
    @TableField("status")
    private Integer status;
    
    /**
     * 最后登录时间
     */
    @TableField("last_login_time")
    private Date lastLoginTime;
    
    /**
     * 最后登录IP
     */
    @TableField("last_login_ip")
    private String lastLoginIp;
    
    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;
    
    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
    
    /**
     * 逻辑删除 0:未删除 1:已删除
     */
    @TableLogic
    @TableField("del")
    private Integer del;
    
    /**
     * 乐观锁版本号
     */
    @Version
    @TableField("version")
    private Integer version;
}
