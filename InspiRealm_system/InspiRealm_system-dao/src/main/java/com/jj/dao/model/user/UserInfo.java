package com.jj.dao.model.user;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.util.Date;

/**
 * 用户基础信息实体类
 *
 * <AUTHOR>
 * @date 2024-07-29
 */
@Data
@TableName("user_info")
public class UserInfo {
    
    /**
     * 用户ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    /**
     * 微信OpenID
     */
    @TableField("open_id")
    private String openId;
    
    /**
     * 微信UnionID
     */
    @TableField("union_id")
    private String unionId;
    
    /**
     * 用户昵称
     */
    @TableField("nickname")
    private String nickname;
    
    /**
     * 头像URL
     */
    @TableField("avatar_url")
    private String avatarUrl;
    
    /**
     * 性别 0:未知 1:男 2:女
     */
    @TableField("gender")
    private Integer gender;
    
    /**
     * 国家
     */
    @TableField("country")
    private String country;
    
    /**
     * 省份
     */
    @TableField("province")
    private String province;
    
    /**
     * 城市
     */
    @TableField("city")
    private String city;
    
    /**
     * 手机号
     */
    @TableField("phone")
    private String phone;
    
    /**
     * 邮箱
     */
    @TableField("email")
    private String email;
    
    /**
     * 状态 0:禁用 1:正常
     */
    @TableField("status")
    private Integer status;
    
    /**
     * 最后登录时间
     */
    @TableField("last_login_time")
    private Date lastLoginTime;
    
    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;
    
    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
    
    /**
     * 逻辑删除 0:未删除 1:已删除
     */
    @TableLogic
    @TableField("del")
    private Integer del;
    
    /**
     * 乐观锁版本号
     */
    @Version
    @TableField("version")
    private Integer version;
}
