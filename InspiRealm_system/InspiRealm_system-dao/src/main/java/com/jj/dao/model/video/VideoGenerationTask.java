package com.jj.dao.model.video;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.util.Date;

/**
 * 视频生成任务实体类
 *
 * <AUTHOR>
 * @date 2025-01-05
 */
@Data
@TableName("video_generation_task")
public class VideoGenerationTask {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 任务编号
     */
    @TableField("task_no")
    private String taskNo;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 外部任务ID（来自视频生成API）
     */
    @TableField("external_task_id")
    private String externalTaskId;

    /**
     * 生成提示词
     */
    @TableField("prompt")
    private String prompt;

    /**
     * 增强后的提示词
     */
    @TableField("enhanced_prompt")
    private String enhancedPrompt;

    /**
     * 使用的模型（固定为veo3）
     */
    @TableField("model")
    private String model;

    /**
     * 是否增强提示词
     */
    @TableField("enhance_prompt")
    private Integer enhancePrompt;

    /**
     * 是否启用超分辨率
     */
    @TableField("enable_upsample")
    private Integer enableUpsample;

    /**
     * 输入图片信息（JSON格式）
     */
    @TableField("input_images")
    private String inputImages;

    /**
     * 视频比例
     */
    @TableField("aspect_ratio")
    private String aspectRatio;

    /**
     * 视频时长（秒）
     */
    @TableField("duration")
    private Integer duration;

    /**
     * 状态 1:待处理 2:处理中 3:成功 4:失败 5:取消
     */
    @TableField("status")
    private Integer status;

    /**
     * 进度百分比
     */
    @TableField("progress")
    private Integer progress;

    /**
     * 错误信息
     */
    @TableField("error_message")
    private String errorMessage;

    /**
     * 生成的视频URL
     */
    @TableField("video_url")
    private String videoUrl;

    /**
     * 视频媒体ID
     */
    @TableField("video_media_id")
    private String videoMediaId;

    /**
     * 缩略图URL
     */
    @TableField("thumbnail_url")
    private String thumbnailUrl;

    /**
     * 文件大小（字节）
     */
    @TableField("file_size")
    private Long fileSize;

    /**
     * 视频宽度
     */
    @TableField("video_width")
    private Integer videoWidth;

    /**
     * 视频高度
     */
    @TableField("video_height")
    private Integer videoHeight;

    /**
     * 视频格式
     */
    @TableField("video_format")
    private String videoFormat;

    /**
     * 积分消耗
     */
    @TableField("points_cost")
    private Integer pointsCost;

    /**
     * 开始时间
     */
    @TableField("start_time")
    private Date startTime;

    /**
     * 结束时间
     */
    @TableField("end_time")
    private Date endTime;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /**
     * 逻辑删除
     */
    @TableLogic
    @TableField("del")
    private Integer del;

    /**
     * 乐观锁版本号
     */
    @Version
    @TableField("version")
    private Integer version;
}