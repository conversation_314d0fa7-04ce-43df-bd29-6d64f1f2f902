package com.jj.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jj.dao.model.image.ImageProcessTask;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 图片处理任务Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-07-29
 */
@Mapper
public interface ImageProcessTaskMapper extends BaseMapper<ImageProcessTask> {
    
    /**
     * 根据任务编号查询任务
     * 
     * @param taskNo 任务编号
     * @return 任务信息
     */
    ImageProcessTask selectByTaskNo(@Param("taskNo") String taskNo);
    
    /**
     * 分页查询用户任务
     *
     * @param page 分页参数
     * @param userId 用户ID
     * @param status 任务状态
     * @param processType 处理类型
     * @return 任务分页数据
     */
    IPage<ImageProcessTask> selectUserTasks(Page<ImageProcessTask> page,
                                           @Param("userId") Long userId,
                                           @Param("status") Integer status,
                                           @Param("processType") String processType);
    
    /**
     * 查询用户任务列表
     * 
     * @param userId 用户ID
     * @param status 任务状态
     * @param limit 限制条数
     * @return 任务列表
     */
    List<ImageProcessTask> selectUserTasksList(@Param("userId") Long userId, 
                                              @Param("status") Integer status, 
                                              @Param("limit") Integer limit);
    
    /**
     * 更新任务状态
     * 
     * @param id 任务ID
     * @param status 状态
     * @param progress 进度
     * @param errorMessage 错误信息
     * @return 更新行数
     */
    int updateTaskStatus(@Param("id") Long id, 
                        @Param("status") Integer status, 
                        @Param("progress") Integer progress, 
                        @Param("errorMessage") String errorMessage);
    
    /**
     * 更新任务开始时间
     * 
     * @param id 任务ID
     * @return 更新行数
     */
    int updateTaskStartTime(@Param("id") Long id);
    
    /**
     * 更新任务结束时间
     * 
     * @param id 任务ID
     * @return 更新行数
     */
    int updateTaskEndTime(@Param("id") Long id);
    
    /**
     * 统计用户任务数量
     *
     * @param userId 用户ID
     * @param status 任务状态
     * @return 任务数量
     */
    Long countUserTasks(@Param("userId") Long userId, @Param("status") Integer status);

    /**
     * 根据任务编号删除任务（逻辑删除）
     *
     * @param taskNo 任务编号
     * @return 删除行数
     */
    int deleteByTaskNo(@Param("taskNo") String taskNo);

    /**
     * 查询所有处理中的任务
     *
     * @return 处理中的任务列表
     */
    List<ImageProcessTask> selectProcessingTasks();

    /**
     * 查询指定天数前创建的任务（用于清理）
     *
     * @param days 天数
     * @return 老任务列表
     */
    List<ImageProcessTask> selectOldTasks(@Param("days") Integer days);

    /**
     * 软删除任务
     *
     * @param id 任务ID
     * @return 更新行数
     */
    int softDeleteTask(@Param("id") Long id);
}
