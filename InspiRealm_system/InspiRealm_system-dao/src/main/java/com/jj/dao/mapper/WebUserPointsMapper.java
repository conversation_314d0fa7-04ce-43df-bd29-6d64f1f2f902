package com.jj.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jj.dao.model.points.WebUserPoints;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * Web端用户积分Mapper接口
 *
 * <AUTHOR>
 * @date 2025-01-05
 */
@Mapper
public interface WebUserPointsMapper extends BaseMapper<WebUserPoints> {

    /**
     * 根据用户ID查询积分信息
     *
     * @param userId 用户ID
     * @return 积分信息
     */
    WebUserPoints selectByUserId(@Param("userId") Long userId);

    /**
     * 增加用户积分
     *
     * @param userId 用户ID
     * @param points 积分数量
     * @return 更新行数
     */
    int addPoints(@Param("userId") Long userId, @Param("points") Integer points);

    /**
     * 消费用户积分
     *
     * @param userId 用户ID
     * @param points 积分数量
     * @return 更新行数
     */
    int consumePoints(@Param("userId") Long userId, @Param("points") Integer points);

    /**
     * 锁定用户积分
     *
     * @param userId 用户ID
     * @param points 积分数量
     * @return 更新行数
     */
    int lockPoints(@Param("userId") Long userId, @Param("points") Integer points);

    /**
     * 解锁用户积分
     *
     * @param userId 用户ID
     * @param points 积分数量
     * @return 更新行数
     */
    int unlockPoints(@Param("userId") Long userId, @Param("points") Integer points);

    /**
     * 检查用户积分是否足够
     *
     * @param userId 用户ID
     * @param points 需要的积分数量
     * @return 是否足够
     */
    boolean hasEnoughPoints(@Param("userId") Long userId, @Param("points") Integer points);

    /**
     * 确认消费积分（从锁定积分转为已消费积分）
     *
     * @param userId 用户ID
     * @param points 积分数量
     * @return 更新行数
     */
    int confirmConsumePoints(@Param("userId") Long userId, @Param("points") Integer points);
}