package com.jj.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jj.dao.model.video.VideoGenerationTask;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 视频生成任务Mapper接口
 *
 * <AUTHOR>
 * @date 2025-01-05
 */
@Mapper
public interface VideoGenerationTaskMapper extends BaseMapper<VideoGenerationTask> {

    /**
     * 根据任务编号查询任务
     *
     * @param taskNo 任务编号
     * @return 任务信息
     */
    VideoGenerationTask selectByTaskNo(@Param("taskNo") String taskNo);

    /**
     * 根据外部任务ID查询任务
     *
     * @param externalTaskId 外部任务ID
     * @return 任务信息
     */
    VideoGenerationTask selectByExternalTaskId(@Param("externalTaskId") String externalTaskId);

    /**
     * 分页查询用户的视频生成任务
     *
     * @param page 分页参数
     * @param userId 用户ID
     * @param status 任务状态（可选）
     * @return 任务列表
     */
    IPage<VideoGenerationTask> selectUserTasksPage(Page<VideoGenerationTask> page,
                                                   @Param("userId") Long userId,
                                                   @Param("status") Integer status);

    /**
     * 查询需要轮询状态的任务
     *
     * @param statuses 状态列表
     * @param limit 限制数量
     * @return 任务列表
     */
    List<VideoGenerationTask> selectTasksForPolling(@Param("statuses") List<Integer> statuses,
                                                    @Param("limit") Integer limit);

    /**
     * 更新任务状态和进度
     *
     * @param id 任务ID
     * @param status 状态
     * @param progress 进度
     * @param errorMessage 错误信息
     * @return 更新行数
     */
    int updateTaskStatus(@Param("id") Long id,
                        @Param("status") Integer status,
                        @Param("progress") Integer progress,
                        @Param("errorMessage") String errorMessage);

    /**
     * 更新任务完成信息
     *
     * @param id 任务ID
     * @param videoUrl 视频URL
     * @param thumbnailUrl 缩略图URL
     * @param fileSize 文件大小
     * @param videoWidth 视频宽度
     * @param videoHeight 视频高度
     * @return 更新行数
     */
    int updateTaskResult(@Param("id") Long id,
                        @Param("videoUrl") String videoUrl,
                        @Param("thumbnailUrl") String thumbnailUrl,
                        @Param("fileSize") Long fileSize,
                        @Param("videoWidth") Integer videoWidth,
                        @Param("videoHeight") Integer videoHeight);

    /**
     * 查询所有处理中的任务
     *
     * @return 处理中的任务列表
     */
    List<VideoGenerationTask> selectProcessingTasks();
}