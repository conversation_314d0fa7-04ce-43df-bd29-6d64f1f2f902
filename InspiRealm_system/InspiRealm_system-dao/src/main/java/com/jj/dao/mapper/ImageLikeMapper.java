package com.jj.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jj.dao.model.square.ImageLike;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 图片点赞记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-07-29
 */
@Mapper
public interface ImageLikeMapper extends BaseMapper<ImageLike> {
    
    /**
     * 查询用户对图片的点赞记录
     * 
     * @param imageId 图片ID
     * @param userId 用户ID
     * @return 点赞记录
     */
    ImageLike selectByImageIdAndUserId(@Param("imageId") Long imageId, @Param("userId") Long userId);
    
    /**
     * 更新或插入点赞记录
     * 
     * @param imageId 图片ID
     * @param userId 用户ID
     * @param likeStatus 点赞状态
     * @return 影响行数
     */
    int upsertLikeRecord(@Param("imageId") Long imageId, 
                        @Param("userId") Long userId, 
                        @Param("likeStatus") Integer likeStatus);
    
    /**
     * 批量查询用户对图片的点赞状态
     * 
     * @param imageIds 图片ID列表
     * @param userId 用户ID
     * @return 点赞记录列表
     */
    List<ImageLike> selectByImageIdsAndUserId(@Param("imageIds") List<Long> imageIds, 
                                             @Param("userId") Long userId);
    
    /**
     * 统计图片的点赞数
     * 
     * @param imageId 图片ID
     * @return 点赞数
     */
    Long countLikesByImageId(@Param("imageId") Long imageId);
}
