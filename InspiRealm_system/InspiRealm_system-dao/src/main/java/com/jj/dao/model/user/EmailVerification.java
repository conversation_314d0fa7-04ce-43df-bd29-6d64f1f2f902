package com.jj.dao.model.user;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.util.Date;

/**
 * 邮箱验证实体类
 *
 * <AUTHOR>
 * @date 2025-01-05
 */
@Data
@TableName("email_verification")
public class EmailVerification {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 邮箱地址
     */
    @TableField("email")
    private String email;

    /**
     * 验证码
     */
    @TableField("verification_code")
    private String verificationCode;

    /**
     * 验证令牌
     */
    @TableField("verification_token")
    private String verificationToken;

    /**
     * 验证类型 1:注册验证 2:密码重置 3:邮箱变更
     */
    @TableField("verification_type")
    private Integer verificationType;

    /**
     * 用户ID（可选）
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 状态 0:未验证 1:已验证 2:已过期
     */
    @TableField("status")
    private Integer status;

    /**
     * 尝试次数
     */
    @TableField("attempts")
    private Integer attempts;

    /**
     * 过期时间
     */
    @TableField("expires_at")
    private Date expiresAt;

    /**
     * 验证时间
     */
    @TableField("verified_at")
    private Date verifiedAt;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
}