package com.jj.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jj.dao.model.points.UserPoints;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 用户积分Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-07-29
 */
@Mapper
public interface UserPointsMapper extends BaseMapper<UserPoints> {
    
    /**
     * 根据用户ID查询积分信息
     * 
     * @param userId 用户ID
     * @return 积分信息
     */
    UserPoints selectByUserId(@Param("userId") Long userId);
    
    /**
     * 根据用户ID查询积分信息（加锁）
     * 
     * @param userId 用户ID
     * @return 积分信息
     */
    UserPoints selectByUserIdForUpdate(@Param("userId") Long userId);
    
    /**
     * 初始化用户积分
     * 
     * @param userId 用户ID
     * @param initialPoints 初始积分
     * @return 插入行数
     */
    int insertUserPoints(@Param("userId") Long userId, @Param("initialPoints") Integer initialPoints);
}
