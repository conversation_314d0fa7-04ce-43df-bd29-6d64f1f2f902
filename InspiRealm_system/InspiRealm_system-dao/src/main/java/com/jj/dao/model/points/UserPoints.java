package com.jj.dao.model.points;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.util.Date;

/**
 * 用户积分实体类
 * 
 * <AUTHOR>
 * @date 2024-07-29
 */
@Data
@TableName("user_points")
public class UserPoints {
    
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    /**
     * 用户ID
     */
    @TableField("user_id")
    private Long userId;
    
    /**
     * 总积分
     */
    @TableField("total_points")
    private Integer totalPoints;
    
    /**
     * 可用积分
     */
    @TableField("available_points")
    private Integer availablePoints;
    
    /**
     * 锁定积分
     */
    @TableField("locked_points")
    private Integer lockedPoints;
    
    /**
     * 已消费积分
     */
    @TableField("consumed_points")
    private Integer consumedPoints;
    
    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;
    
    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
    
    /**
     * 逻辑删除 0:未删除 1:已删除
     */
    @TableLogic
    @TableField("del")
    private Integer del;
    
    /**
     * 乐观锁版本号
     */
    @Version
    @TableField("version")
    private Integer version;
}
