package com.jj.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jj.dao.model.user.UserInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 用户基础信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-07-29
 */
@Mapper
public interface UserInfoMapper extends BaseMapper<UserInfo> {
    
    /**
     * 根据微信OpenID查询用户信息
     * 
     * @param openId 微信OpenID
     * @return 用户信息
     */
    UserInfo selectByOpenId(@Param("openId") String openId);
    
    /**
     * 更新用户最后登录时间
     * 
     * @param userId 用户ID
     * @return 更新行数
     */
    int updateLastLoginTime(@Param("userId") Long userId);
}
