package com.jj.dao.model.square;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.util.Date;

/**
 * 图片点赞实体类
 * 
 * <AUTHOR>
 * @date 2024-07-29
 */
@Data
@TableName("image_like")
public class ImageLike {
    
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    /**
     * 图片ID
     */
    @TableField("image_id")
    private Long imageId;
    
    /**
     * 用户ID
     */
    @TableField("user_id")
    private Long userId;
    
    /**
     * 点赞状态 0:取消点赞 1:点赞
     */
    @TableField("like_status")
    private Integer likeStatus;
    
    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;
    
    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
    
    /**
     * 逻辑删除 0:未删除 1:已删除
     */
    @TableLogic
    @TableField("del")
    private Integer del;
}
