package com.jj.dao.model.points;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.util.Date;

/**
 * 积分交易记录实体类
 * 
 * <AUTHOR>
 * @date 2024-07-29
 */
@Data
@TableName("points_transaction")
public class PointsTransaction {
    
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    /**
     * 用户ID
     */
    @TableField("user_id")
    private Long userId;
    
    /**
     * 交易类型 1:收入 2:支出
     */
    @TableField("transaction_type")
    private Integer transactionType;
    
    /**
     * 积分数量
     */
    @TableField("points_amount")
    private Integer pointsAmount;
    
    /**
     * 业务类型
     */
    @TableField("business_type")
    private String businessType;
    
    /**
     * 业务ID
     */
    @TableField("business_id")
    private String businessId;
    
    /**
     * 交易描述
     */
    @TableField("description")
    private String description;

    /**
     * 交易前积分
     */
    @TableField("before_points")
    private Integer beforePoints;

    /**
     * 交易后积分
     */
    @TableField("after_points")
    private Integer afterPoints;
    
    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;
    
    /**
     * 逻辑删除 0:未删除 1:已删除
     */
    @TableLogic
    @TableField("del")
    private Integer del;
}
