package com.jj.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jj.dao.model.user.EmailVerification;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 邮箱验证Mapper接口
 *
 * <AUTHOR>
 * @date 2025-01-05
 */
@Mapper
public interface EmailVerificationMapper extends BaseMapper<EmailVerification> {

    /**
     * 根据邮箱和验证类型查询最新的验证记录
     *
     * @param email 邮箱地址
     * @param verificationType 验证类型
     * @return 验证记录
     */
    EmailVerification selectLatestByEmailAndType(@Param("email") String email,
                                               @Param("verificationType") Integer verificationType);

    /**
     * 根据邮箱、验证码和类型查询验证记录
     *
     * @param email 邮箱地址
     * @param verificationCode 验证码
     * @param verificationType 验证类型
     * @return 验证记录
     */
    EmailVerification selectByEmailAndCodeAndType(@Param("email") String email,
                                                @Param("verificationCode") String verificationCode,
                                                @Param("verificationType") Integer verificationType);

    /**
     * 根据验证令牌查询验证记录
     *
     * @param verificationToken 验证令牌
     * @return 验证记录
     */
    EmailVerification selectByVerificationToken(@Param("verificationToken") String verificationToken);

    /**
     * 查询邮箱在指定时间内的验证记录数量
     *
     * @param email 邮箱地址
     * @param verificationType 验证类型
     * @param minutes 时间范围（分钟）
     * @return 记录数量
     */
    int countRecentVerifications(@Param("email") String email,
                               @Param("verificationType") Integer verificationType,
                               @Param("minutes") Integer minutes);

    /**
     * 删除邮箱指定类型的验证记录
     *
     * @param email 邮箱地址
     * @param verificationType 验证类型
     * @return 删除行数
     */
    int deleteByEmailAndType(@Param("email") String email,
                           @Param("verificationType") Integer verificationType);

    /**
     * 删除过期的验证记录
     *
     * @return 删除行数
     */
    int deleteExpiredVerifications();

    /**
     * 增加验证尝试次数
     *
     * @param id 验证记录ID
     * @return 更新行数
     */
    int incrementAttempts(@Param("id") Long id);

    /**
     * 标记验证记录为已使用
     *
     * @param id 验证记录ID
     * @return 更新行数
     */
    int markAsUsed(@Param("id") Long id);
}