package com.jj.dao.model.image;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.util.Date;

/**
 * 处理后图片实体类
 * 
 * <AUTHOR>
 * @date 2024-07-29
 */
@Data
@TableName("processed_image")
public class ProcessedImage {
    
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    /**
     * 用户ID
     */
    @TableField("user_id")
    private Long userId;
    
    /**
     * 任务ID
     */
    @TableField("task_id")
    private Long taskId;
    
    /**
     * 原始图片URL
     */
    @TableField("original_image_url")
    private String originalImageUrl;


    /**
     * 图片URL（兼容字段）
     */
    @TableField("image_url")
    private String imageUrl;

    /**
     * 文件名
     */
    @TableField("file_name")
    private String fileName;
    
    /**
     * 缩略图URL
     */
    @TableField("thumbnail_url")
    private String thumbnailUrl;
    
    /**
     * 处理类型
     */
    @TableField("process_type")
    private String processType;

    /**
     * 图片宽度（兼容字段）
     */
    @TableField("image_width")
    private Integer imageWidth;

    /**
     * 图片高度（兼容字段）
     */
    @TableField("image_height")
    private Integer imageHeight;
    
    /**
     * 文件大小（字节）
     */
    @TableField("file_size")
    private Long fileSize;


    /**
     * 图片格式（兼容字段）
     */
    @TableField("image_format")
    private String imageFormat;

    /**
     * 宽高比
     */
    @TableField("aspect_ratio")  // 计算字段，可以通过width/height计算
    private String aspectRatio;
    
    /**
     * 是否公开 0:私有 1:公开
     */
    @TableField("is_public")
    private Integer isPublic;
    
    /**
     * 点赞数
     */
    @TableField("like_count")
    private Integer likeCount;
    
    /**
     * 浏览数
     */
    @TableField("view_count")
    private Integer viewCount;
    
    /**
     * 审核状态 0:待审核 1:审核通过 2:审核拒绝
     */
    @TableField("audit_status")
    private Integer auditStatus;
    
    /**
     * 审核备注
     */
    @TableField("audit_remark")
    private String auditRemark;
    
    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;
    
    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
    
    /**
     * 逻辑删除 0:未删除 1:已删除
     */
    @TableLogic
    @TableField("del")
    private Integer del;
    
    /**
     * 乐观锁版本号
     */
    @Version
    @TableField("version")
    private Integer version;
}
