<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jj.dao.mapper.UserInfoMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.jj.dao.model.user.UserInfo">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="open_id" property="openId" jdbcType="VARCHAR"/>
        <result column="union_id" property="unionId" jdbcType="VARCHAR"/>
        <result column="nickname" property="nickname" jdbcType="VARCHAR"/>
        <result column="avatar_url" property="avatarUrl" jdbcType="VARCHAR"/>
        <result column="gender" property="gender" jdbcType="TINYINT"/>
        <result column="country" property="country" jdbcType="VARCHAR"/>
        <result column="province" property="province" jdbcType="VARCHAR"/>
        <result column="city" property="city" jdbcType="VARCHAR"/>
        <result column="phone" property="phone" jdbcType="VARCHAR"/>
        <result column="email" property="email" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="TINYINT"/>
        <result column="last_login_time" property="lastLoginTime" jdbcType="TIMESTAMP"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="del" property="del" jdbcType="TINYINT"/>
        <result column="version" property="version" jdbcType="INTEGER"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, open_id, union_id, nickname, avatar_url, gender, country, province, city,
        phone, email, status, last_login_time, create_time, update_time, del, version
    </sql>

    <!-- 根据微信OpenID查询用户信息 -->
    <select id="selectByOpenId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM user_info
        WHERE open_id = #{openId}
        AND del = 0
    </select>

    <!-- 更新用户最后登录时间 -->
    <update id="updateLastLoginTime">
        UPDATE user_info
        SET last_login_time = NOW(),
            update_time = NOW()
        WHERE id = #{userId}
        AND del = 0
    </update>

</mapper>
