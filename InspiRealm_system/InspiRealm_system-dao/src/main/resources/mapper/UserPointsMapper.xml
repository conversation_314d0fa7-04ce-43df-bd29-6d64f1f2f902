<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jj.dao.mapper.UserPointsMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.jj.dao.model.points.UserPoints">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="user_id" property="userId" jdbcType="BIGINT"/>
        <result column="total_points" property="totalPoints" jdbcType="INTEGER"/>
        <result column="available_points" property="availablePoints" jdbcType="INTEGER"/>
        <result column="locked_points" property="lockedPoints" jdbcType="INTEGER"/>
        <result column="consumed_points" property="consumedPoints" jdbcType="INTEGER"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="del" property="del" jdbcType="TINYINT"/>
        <result column="version" property="version" jdbcType="INTEGER"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, user_id, total_points, available_points, locked_points, consumed_points,
        create_time, update_time, del, version
    </sql>

    <!-- 根据用户ID查询积分信息 -->
    <select id="selectByUserId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM user_points
        WHERE user_id = #{userId}
        AND del = 0
    </select>

    <!-- 根据用户ID查询积分信息（加锁） -->
    <select id="selectByUserIdForUpdate" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM user_points
        WHERE user_id = #{userId}
        AND del = 0
        FOR UPDATE
    </select>

    <!-- 初始化用户积分 -->
    <insert id="insertUserPoints">
        INSERT INTO user_points (user_id, total_points, available_points, locked_points, consumed_points, create_time, update_time)
        VALUES (#{userId}, #{initialPoints}, #{initialPoints}, 0, 0, NOW(), NOW())
    </insert>

</mapper>
