<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jj.dao.mapper.VideoGenerationTaskMapper">

    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="com.jj.dao.model.video.VideoGenerationTask">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="task_no" property="taskNo" jdbcType="VARCHAR"/>
        <result column="user_id" property="userId" jdbcType="BIGINT"/>
        <result column="prompt" property="prompt" jdbcType="LONGVARCHAR"/>
        <result column="enhance_prompt" property="enhancePrompt" jdbcType="TINYINT"/>
        <result column="enable_upsample" property="enableUpsample" jdbcType="TINYINT"/>
        <result column="input_images" property="inputImages" jdbcType="LONGVARCHAR"/>
        <result column="status" property="status" jdbcType="TINYINT"/>
        <result column="progress" property="progress" jdbcType="TINYINT"/>
        <result column="external_task_id" property="externalTaskId" jdbcType="VARCHAR"/>
        <result column="video_url" property="videoUrl" jdbcType="VARCHAR"/>
        <result column="thumbnail_url" property="thumbnailUrl" jdbcType="VARCHAR"/>
        <result column="duration" property="duration" jdbcType="INTEGER"/>
        <result column="file_size" property="fileSize" jdbcType="BIGINT"/>
        <result column="video_width" property="videoWidth" jdbcType="INTEGER"/>
        <result column="video_height" property="videoHeight" jdbcType="INTEGER"/>
        <result column="points_cost" property="pointsCost" jdbcType="INTEGER"/>
        <result column="error_message" property="errorMessage" jdbcType="LONGVARCHAR"/>
        <result column="start_time" property="startTime" jdbcType="TIMESTAMP"/>
        <result column="end_time" property="endTime" jdbcType="TIMESTAMP"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="del" property="del" jdbcType="TINYINT"/>
        <result column="version" property="version" jdbcType="INTEGER"/>
    </resultMap>

    <!-- 根据任务编号查询 -->
    <select id="selectByTaskNo" resultMap="BaseResultMap">
        SELECT * FROM video_generation_task
        WHERE task_no = #{taskNo} AND del = 0
    </select>

    <!-- 根据外部任务ID查询 -->
    <select id="selectByExternalTaskId" resultMap="BaseResultMap">
        SELECT * FROM video_generation_task
        WHERE external_task_id = #{externalTaskId} AND del = 0
    </select>

    <!-- 分页查询用户任务 -->
    <select id="selectUserTasksPage" resultMap="BaseResultMap">
        SELECT * FROM video_generation_task
        WHERE user_id = #{userId} AND del = 0
        <if test="status != null">
            AND status = #{status}
        </if>
        ORDER BY create_time DESC
    </select>

    <!-- 查询需要轮询的任务 -->
    <select id="selectTasksForPolling" resultMap="BaseResultMap">
        SELECT * FROM video_generation_task
        WHERE status IN
        <foreach collection="statuses" item="status" open="(" close=")" separator=",">
            #{status}
        </foreach>
        AND del = 0
        ORDER BY create_time ASC
        <if test="limit != null">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 查询处理中的任务 -->
    <select id="selectProcessingTasks" resultMap="BaseResultMap">
        SELECT * FROM video_generation_task
        WHERE status = 2 AND del = 0
        ORDER BY create_time ASC
    </select>

    <!-- 更新任务状态和进度 -->
    <update id="updateTaskStatus">
        UPDATE video_generation_task
        SET status = #{status},
            progress = #{progress},
            <if test="errorMessage != null">
                error_message = #{errorMessage},
            </if>
            update_time = NOW()
        WHERE id = #{id}
    </update>

    <!-- 更新任务完成信息 -->
    <update id="updateTaskResult">
        UPDATE video_generation_task
        SET video_url = #{videoUrl},
            <if test="thumbnailUrl != null">
                thumbnail_url = #{thumbnailUrl},
            </if>
            <if test="fileSize != null">
                file_size = #{fileSize},
            </if>
            <if test="videoWidth != null">
                video_width = #{videoWidth},
            </if>
            <if test="videoHeight != null">
                video_height = #{videoHeight},
            </if>
            update_time = NOW()
        WHERE id = #{id}
    </update>

</mapper>