<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jj.dao.mapper.ImageProcessTaskMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.jj.dao.model.image.ImageProcessTask">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="task_no" property="taskNo" jdbcType="VARCHAR"/>
        <result column="user_id" property="userId" jdbcType="BIGINT"/>
        <result column="prompt" property="prompt" jdbcType="LONGVARCHAR"/>
        <result column="original_image_url" property="originalImageUrl" jdbcType="VARCHAR"/>
        <result column="original_image_base64" property="originalImageBase64" jdbcType="LONGVARCHAR"/>
        <result column="aspect_ratio" property="aspectRatio" jdbcType="VARCHAR"/>
        <result column="process_type" property="processType" jdbcType="VARCHAR"/>
        <result column="text_content" property="textContent" jdbcType="VARCHAR"/>
        <result column="style_type" property="styleType" jdbcType="VARCHAR"/>
        <result column="points_cost" property="pointsCost" jdbcType="INTEGER"/>
        <result column="status" property="status" jdbcType="TINYINT"/>
        <result column="progress" property="progress" jdbcType="INTEGER"/>
        <result column="error_message" property="errorMessage" jdbcType="LONGVARCHAR"/>
        <result column="start_time" property="startTime" jdbcType="TIMESTAMP"/>
        <result column="end_time" property="endTime" jdbcType="TIMESTAMP"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="del" property="del" jdbcType="TINYINT"/>
        <result column="version" property="version" jdbcType="INTEGER"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, task_no, user_id, prompt, original_image_url, original_image_base64,
        aspect_ratio, process_type, text_content, style_type, points_cost, status,
        progress, error_message, start_time, end_time, create_time, update_time, del, version
    </sql>

    <!-- 根据任务编号查询任务 -->
    <select id="selectByTaskNo" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM image_process_task
        WHERE task_no = #{taskNo}
        AND del = 0
    </select>

    <!-- 分页查询用户任务 -->
    <select id="selectUserTasks" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM image_process_task
        WHERE user_id = #{userId}
        AND del = 0
        <if test="status != null">
            AND status = #{status}
        </if>
        <if test="processType != null and processType != ''">
            AND process_type = #{processType}
        </if>
        ORDER BY create_time DESC
    </select>

    <!-- 查询用户任务列表 -->
    <select id="selectUserTasksList" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM image_process_task
        WHERE user_id = #{userId}
        AND del = 0
        <if test="status != null">
            AND status = #{status}
        </if>
        ORDER BY create_time DESC
        <if test="limit != null">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 更新任务状态 -->
    <update id="updateTaskStatus">
        UPDATE image_process_task
        SET status = #{status},
            progress = #{progress},
            error_message = #{errorMessage},
            update_time = NOW()
        WHERE id = #{id}
        AND del = 0
    </update>

    <!-- 更新任务开始时间 -->
    <update id="updateTaskStartTime">
        UPDATE image_process_task
        SET start_time = NOW(),
            update_time = NOW()
        WHERE id = #{id}
        AND del = 0
    </update>

    <!-- 更新任务结束时间 -->
    <update id="updateTaskEndTime">
        UPDATE image_process_task
        SET end_time = NOW(),
            update_time = NOW()
        WHERE id = #{id}
        AND del = 0
    </update>

    <!-- 统计用户任务数量 -->
    <select id="countUserTasks" resultType="java.lang.Long">
        SELECT COUNT(1)
        FROM image_process_task
        WHERE user_id = #{userId}
        AND del = 0
        <if test="status != null">
            AND status = #{status}
        </if>
    </select>

    <!-- 根据任务编号删除任务（逻辑删除） -->
    <update id="deleteByTaskNo">
        UPDATE image_process_task
        SET del = 1,
            update_time = NOW()
        WHERE task_no = #{taskNo}
        AND del = 0
    </update>

    <!-- 查询所有处理中的任务 -->
    <select id="selectProcessingTasks" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM image_process_task
        WHERE status IN (1, 2)
        AND del = 0
        ORDER BY create_time ASC
    </select>

    <!-- 查询指定天数前创建的任务（用于清理） -->
    <select id="selectOldTasks" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM image_process_task
        WHERE create_time &lt; DATE_SUB(NOW(), INTERVAL #{days} DAY)
        AND del = 0
        ORDER BY create_time ASC
    </select>

    <!-- 软删除任务 -->
    <update id="softDeleteTask">
        UPDATE image_process_task
        SET del = 1,
            update_time = NOW()
        WHERE id = #{id}
        AND del = 0
    </update>

</mapper>
