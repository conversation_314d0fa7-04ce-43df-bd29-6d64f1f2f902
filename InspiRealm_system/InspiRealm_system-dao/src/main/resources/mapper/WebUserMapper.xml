<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jj.dao.mapper.WebUserMapper">

    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="com.jj.dao.model.user.WebUser">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="username" property="username" jdbcType="VARCHAR"/>
        <result column="email" property="email" jdbcType="VARCHAR"/>
        <result column="password_hash" property="passwordHash" jdbcType="VARCHAR"/>
        <result column="nickname" property="nickname" jdbcType="VARCHAR"/>
        <result column="avatar_url" property="avatarUrl" jdbcType="VARCHAR"/>
        <result column="phone" property="phone" jdbcType="VARCHAR"/>
        <result column="gender" property="gender" jdbcType="TINYINT"/>
        <result column="birthday" property="birthday" jdbcType="DATE"/>
        <result column="country" property="country" jdbcType="VARCHAR"/>
        <result column="province" property="province" jdbcType="VARCHAR"/>
        <result column="city" property="city" jdbcType="VARCHAR"/>
        <result column="user_type" property="userType" jdbcType="TINYINT"/>
        <result column="status" property="status" jdbcType="TINYINT"/>
        <result column="email_verified" property="emailVerified" jdbcType="TINYINT"/>
        <result column="last_login_time" property="lastLoginTime" jdbcType="TIMESTAMP"/>
        <result column="last_login_ip" property="lastLoginIp" jdbcType="VARCHAR"/>
        <result column="login_count" property="loginCount" jdbcType="INTEGER"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="del" property="del" jdbcType="TINYINT"/>
        <result column="version" property="version" jdbcType="INTEGER"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, username, email, password_hash, nickname, avatar_url, phone, gender, birthday,
        country, province, city, user_type, status, email_verified, last_login_time,
        last_login_ip, login_count, create_time, update_time, del, version
    </sql>

    <!-- 根据邮箱查询用户信息 -->
    <select id="selectByEmail" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM web_user
        WHERE email = #{email} AND del = 0
    </select>

    <!-- 根据用户名查询用户信息 -->
    <select id="selectByUsername" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM web_user
        WHERE username = #{username} AND del = 0
    </select>

    <!-- 检查邮箱是否已存在 -->
    <select id="existsByEmail" resultType="boolean">
        SELECT COUNT(*) > 0
        FROM web_user
        WHERE email = #{email} AND del = 0
    </select>

    <!-- 检查用户名是否已存在 -->
    <select id="existsByUsername" resultType="boolean">
        SELECT COUNT(*) > 0
        FROM web_user
        WHERE username = #{username} AND del = 0
    </select>

    <!-- 更新最后登录信息 -->
    <update id="updateLastLoginInfo">
        UPDATE web_user
        SET last_login_time = NOW(),
            last_login_ip = #{loginIp},
            login_count = login_count + 1,
            update_time = NOW()
        WHERE id = #{userId}
    </update>

    <!-- 更新邮箱验证状态 -->
    <update id="updateEmailVerified">
        UPDATE web_user
        SET email_verified = #{emailVerified},
            update_time = NOW()
        WHERE id = #{id}
    </update>

    <!-- 更新用户状态 -->
    <update id="updateUserStatus">
        UPDATE web_user
        SET status = #{status},
            update_time = NOW()
        WHERE id = #{id}
    </update>

    <!-- 更新密码 -->
    <update id="updatePassword">
        UPDATE web_user
        SET password_hash = #{passwordHash},
            update_time = NOW()
        WHERE id = #{id}
    </update>

</mapper>
