<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jj.dao.mapper.ImageLikeMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.jj.dao.model.square.ImageLike">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="image_id" property="imageId" jdbcType="BIGINT"/>
        <result column="user_id" property="userId" jdbcType="BIGINT"/>
        <result column="like_status" property="likeStatus" jdbcType="TINYINT"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="del" property="del" jdbcType="TINYINT"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, image_id, user_id, like_status, create_time, update_time, del
    </sql>

    <!-- 查询用户对图片的点赞记录 -->
    <select id="selectByImageIdAndUserId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM image_like
        WHERE image_id = #{imageId}
        AND user_id = #{userId}
        AND del = 0
    </select>

    <!-- 更新或插入点赞记录 -->
    <insert id="upsertLikeRecord">
        INSERT INTO image_like (image_id, user_id, like_status, create_time, update_time, del)
        VALUES (#{imageId}, #{userId}, #{likeStatus}, NOW(), NOW(), 0)
        ON DUPLICATE KEY UPDATE
        like_status = #{likeStatus},
        update_time = NOW()
    </insert>

    <!-- 批量查询用户对图片的点赞状态 -->
    <select id="selectByImageIdsAndUserId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM image_like
        WHERE image_id IN
        <foreach collection="imageIds" item="imageId" open="(" separator="," close=")">
            #{imageId}
        </foreach>
        AND user_id = #{userId}
        AND like_status = 1
        AND del = 0
    </select>

    <!-- 统计图片的点赞数 -->
    <select id="countLikesByImageId" resultType="java.lang.Long">
        SELECT COUNT(1)
        FROM image_like
        WHERE image_id = #{imageId}
        AND like_status = 1
        AND del = 0
    </select>

</mapper>
