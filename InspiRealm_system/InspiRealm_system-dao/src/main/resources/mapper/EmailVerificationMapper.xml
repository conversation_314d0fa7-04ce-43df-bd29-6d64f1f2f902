<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jj.dao.mapper.EmailVerificationMapper">

    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="com.jj.dao.model.user.EmailVerification">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="email" property="email" jdbcType="VARCHAR"/>
        <result column="verification_code" property="verificationCode" jdbcType="VARCHAR"/>
        <result column="verification_token" property="verificationToken" jdbcType="VARCHAR"/>
        <result column="verification_type" property="verificationType" jdbcType="TINYINT"/>
        <result column="user_id" property="userId" jdbcType="BIGINT"/>
        <result column="status" property="status" jdbcType="TINYINT"/>
        <result column="attempts" property="attempts" jdbcType="INTEGER"/>
        <result column="expires_at" property="expiresAt" jdbcType="TIMESTAMP"/>
        <result column="verified_at" property="verifiedAt" jdbcType="TIMESTAMP"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, email, verification_code, verification_token, verification_type, user_id,
        status, attempts, expires_at, verified_at, create_time, update_time
    </sql>

    <!-- 根据邮箱和验证类型查询最新的验证记录 -->
    <select id="selectLatestByEmailAndType" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM email_verification
        WHERE email = #{email} AND verification_type = #{verificationType}
        ORDER BY create_time DESC
        LIMIT 1
    </select>

    <!-- 根据邮箱、验证码和类型查询验证记录 -->
    <select id="selectByEmailAndCodeAndType" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM email_verification
        WHERE email = #{email} 
        AND verification_code = #{verificationCode} 
        AND verification_type = #{verificationType}
        AND status = 0
        AND expires_at &gt; NOW()
    </select>

    <!-- 根据验证令牌查询验证记录 -->
    <select id="selectByVerificationToken" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM email_verification
        WHERE verification_token = #{verificationToken}
        AND status = 0
        AND expires_at &gt; NOW()
    </select>

    <!-- 查询邮箱在指定时间内的验证记录数量 -->
    <select id="countRecentVerifications" resultType="int">
        SELECT COUNT(*)
        FROM email_verification
        WHERE email = #{email} 
        AND verification_type = #{verificationType}
        AND create_time &gt; DATE_SUB(NOW(), INTERVAL #{minutes} MINUTE)
    </select>

    <!-- 删除邮箱指定类型的验证记录 -->
    <delete id="deleteByEmailAndType">
        DELETE FROM email_verification
        WHERE email = #{email} AND verification_type = #{verificationType}
    </delete>

    <!-- 删除过期的验证记录 -->
    <delete id="deleteExpiredVerifications">
        DELETE FROM email_verification
        WHERE expires_at &lt; NOW()
    </delete>

    <!-- 增加验证尝试次数 -->
    <update id="incrementAttempts">
        UPDATE email_verification
        SET attempts = attempts + 1,
            update_time = NOW()
        WHERE id = #{id}
    </update>

    <!-- 标记验证记录为已使用 -->
    <update id="markAsUsed">
        UPDATE email_verification
        SET status = 1,
            verified_at = NOW(),
            update_time = NOW()
        WHERE id = #{id}
    </update>

</mapper>
