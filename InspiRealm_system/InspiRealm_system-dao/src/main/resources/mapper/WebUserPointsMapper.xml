<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jj.dao.mapper.WebUserPointsMapper">

    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="com.jj.dao.model.points.WebUserPoints">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="user_id" property="userId" jdbcType="BIGINT"/>
        <result column="total_points" property="totalPoints" jdbcType="INTEGER"/>
        <result column="available_points" property="availablePoints" jdbcType="INTEGER"/>
        <result column="locked_points" property="lockedPoints" jdbcType="INTEGER"/>
        <result column="consumed_points" property="consumedPoints" jdbcType="INTEGER"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="del" property="del" jdbcType="TINYINT"/>
        <result column="version" property="version" jdbcType="INTEGER"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, user_id, total_points, available_points, locked_points, consumed_points,
        create_time, update_time, del, version
    </sql>

    <!-- 根据用户ID查询积分信息 -->
    <select id="selectByUserId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM web_user_points
        WHERE user_id = #{userId} AND del = 0
    </select>

    <!-- 增加用户积分 -->
    <update id="addPoints">
        UPDATE web_user_points
        SET total_points = total_points + #{points},
            available_points = available_points + #{points},
            update_time = NOW(),
            version = version + 1
        WHERE user_id = #{userId} AND del = 0
    </update>

    <!-- 消费用户积分 -->
    <update id="consumePoints">
        UPDATE web_user_points
        SET available_points = available_points - #{points},
            consumed_points = consumed_points + #{points},
            update_time = NOW(),
            version = version + 1
        WHERE user_id = #{userId} 
        AND available_points &gt;= #{points}
        AND del = 0
    </update>

    <!-- 锁定用户积分 -->
    <update id="lockPoints">
        UPDATE web_user_points
        SET available_points = available_points - #{points},
            locked_points = locked_points + #{points},
            update_time = NOW(),
            version = version + 1
        WHERE user_id = #{userId}
        AND available_points &gt;= #{points}
        AND del = 0
    </update>

    <!-- 解锁用户积分 -->
    <update id="unlockPoints">
        UPDATE web_user_points
        SET available_points = available_points + #{points},
            locked_points = locked_points - #{points},
            update_time = NOW(),
            version = version + 1
        WHERE user_id = #{userId}
        AND locked_points &gt;= #{points}
        AND del = 0
    </update>

    <!-- 检查用户积分是否足够 -->
    <select id="hasEnoughPoints" resultType="boolean">
        SELECT COUNT(*) &gt; 0
        FROM web_user_points
        WHERE user_id = #{userId}
        AND available_points &gt;= #{points}
        AND del = 0
    </select>

    <!-- 确认消费积分（从锁定积分转为已消费积分） -->
    <update id="confirmConsumePoints">
        UPDATE web_user_points
        SET locked_points = locked_points - #{points},
            consumed_points = consumed_points + #{points},
            update_time = NOW(),
            version = version + 1
        WHERE user_id = #{userId}
        AND locked_points &gt;= #{points}
        AND del = 0
    </update>

</mapper>
