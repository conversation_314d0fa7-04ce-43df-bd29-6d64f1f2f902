<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jj.dao.mapper.AdminUserMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.jj.dao.model.admin.AdminUser">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="username" property="username" jdbcType="VARCHAR"/>
        <result column="password" property="password" jdbcType="VARCHAR"/>
        <result column="real_name" property="realName" jdbcType="VARCHAR"/>
        <result column="email" property="email" jdbcType="VARCHAR"/>
        <result column="phone" property="phone" jdbcType="VARCHAR"/>
        <result column="role" property="role" jdbcType="TINYINT"/>
        <result column="status" property="status" jdbcType="TINYINT"/>
        <result column="last_login_time" property="lastLoginTime" jdbcType="TIMESTAMP"/>
        <result column="last_login_ip" property="lastLoginIp" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="del" property="del" jdbcType="TINYINT"/>
        <result column="version" property="version" jdbcType="INTEGER"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, username, password, real_name, email, phone, role, status,
        last_login_time, last_login_ip, create_time, update_time, del, version
    </sql>

    <!-- 根据用户名查询管理员 -->
    <select id="selectByUsername" resultMap="BaseResultMap">
        SELECT
        id, username, password, real_name, email, phone, role, status,
        CASE WHEN last_login_time IS NULL THEN NULL ELSE last_login_time END as last_login_time,
        last_login_ip, create_time, update_time, del, version
        FROM admin_user
        WHERE username = #{username}
        AND del = 0
    </select>

    <!-- 更新最后登录信息 -->
    <update id="updateLastLoginInfo">
        UPDATE admin_user
        SET last_login_time = #{lastLoginTime,jdbcType=TIMESTAMP},
            last_login_ip = #{lastLoginIp,jdbcType=VARCHAR},
            update_time = NOW()
        WHERE id = #{id}
        AND del = 0
    </update>

</mapper>
