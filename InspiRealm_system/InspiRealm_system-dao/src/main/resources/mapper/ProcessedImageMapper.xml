<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jj.dao.mapper.ProcessedImageMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.jj.dao.model.image.ProcessedImage">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="task_id" property="taskId" jdbcType="BIGINT"/>
        <result column="user_id" property="userId" jdbcType="BIGINT"/>
        <result column="image_url" property="imageUrl" jdbcType="VARCHAR"/>
        <result column="thumbnail_url" property="thumbnailUrl" jdbcType="VARCHAR"/>
        <result column="original_image_url" property="originalImageUrl" jdbcType="VARCHAR"/>
        <result column="file_name" property="fileName" jdbcType="VARCHAR"/>
        <result column="file_size" property="fileSize" jdbcType="BIGINT"/>
        <result column="image_width" property="imageWidth" jdbcType="INTEGER"/>
        <result column="image_height" property="imageHeight" jdbcType="INTEGER"/>
        <result column="image_format" property="imageFormat" jdbcType="VARCHAR"/>
        <result column="aspect_ratio" property="aspectRatio" jdbcType="VARCHAR"/>
        <result column="process_type" property="processType" jdbcType="VARCHAR"/>
        <result column="is_public" property="isPublic" jdbcType="TINYINT"/>
        <result column="like_count" property="likeCount" jdbcType="INTEGER"/>
        <result column="view_count" property="viewCount" jdbcType="INTEGER"/>
        <result column="audit_status" property="auditStatus" jdbcType="TINYINT"/>
        <result column="audit_remark" property="auditRemark" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="del" property="del" jdbcType="TINYINT"/>
        <result column="version" property="version" jdbcType="INTEGER"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, task_id, user_id, image_url, thumbnail_url, original_image_url,
        file_name, file_size, image_width, image_height, image_format,
        aspect_ratio, process_type, is_public, like_count, view_count,
        audit_status, audit_remark, create_time, update_time, del, version
    </sql>

    <!-- 根据任务ID查询图片 -->
    <select id="selectByTaskId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM processed_image
        WHERE task_id = #{taskId}
        AND del = 0
    </select>

    <!-- 分页查询用户图片 -->
    <select id="selectUserImages" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM processed_image
        WHERE user_id = #{userId}
        AND del = 0
        <if test="isPublic != null">
            AND is_public = #{isPublic}
        </if>
        ORDER BY create_time DESC
    </select>

    <!-- 分页查询公开图片 -->
    <select id="selectPublicImages" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM processed_image
        WHERE is_public = 1
        AND audit_status = 1
        AND del = 0
        <if test="processType != null and processType != ''">
            AND process_type = #{processType}
        </if>
        <if test="aspectRatio != null and aspectRatio != ''">
            AND aspect_ratio = #{aspectRatio}
        </if>
        ORDER BY create_time DESC
    </select>

    <!-- 查询用户图片列表 -->
    <select id="selectUserImagesList" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM processed_image
        WHERE user_id = #{userId}
        AND del = 0
        <if test="isPublic != null">
            AND is_public = #{isPublic}
        </if>
        ORDER BY create_time DESC
        <if test="limit != null">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 更新图片公开状态 -->
    <update id="updateImagePublicStatus">
        UPDATE processed_image
        SET is_public = #{isPublic},
            update_time = NOW()
        WHERE id = #{id}
        AND user_id = #{userId}
        AND del = 0
    </update>

    <!-- 增加图片查看数 -->
    <update id="incrementViewCount">
        UPDATE processed_image
        SET view_count = view_count + 1,
            update_time = NOW()
        WHERE id = #{id}
        AND del = 0
    </update>

    <!-- 增加图片点赞数 -->
    <update id="incrementLikeCount">
        UPDATE processed_image
        SET like_count = like_count + 1,
            update_time = NOW()
        WHERE id = #{id}
        AND del = 0
    </update>

    <!-- 减少图片点赞数 -->
    <update id="decrementLikeCount">
        UPDATE processed_image
        SET like_count = like_count - 1,
            update_time = NOW()
        WHERE id = #{id}
        AND like_count > 0
        AND del = 0
    </update>

    <!-- 统计用户图片数量 -->
    <select id="countUserImages" resultType="java.lang.Long">
        SELECT COUNT(1)
        FROM processed_image
        WHERE user_id = #{userId}
        AND del = 0
        <if test="isPublic != null">
            AND is_public = #{isPublic}
        </if>
    </select>

    <!-- 根据任务ID删除图片（逻辑删除） -->
    <update id="deleteByTaskId">
        UPDATE processed_image
        SET del = 1,
            update_time = NOW()
        WHERE task_id = #{taskId}
        AND del = 0
    </update>

    <!-- 根据任务ID列表查询图片 -->
    <select id="selectByTaskIds" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM processed_image
        WHERE task_id IN
        <foreach collection="taskIds" item="taskId" open="(" separator="," close=")">
            #{taskId}
        </foreach>
        AND del = 0
    </select>

    <!-- 软删除图片 -->
    <update id="softDeleteImage">
        UPDATE processed_image
        SET del = 1,
            update_time = NOW()
        WHERE id = #{id}
        AND del = 0
    </update>

</mapper>
