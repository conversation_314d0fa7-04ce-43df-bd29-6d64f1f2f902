<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jj.dao.mapper.UserSessionMapper">

    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="com.jj.dao.model.user.UserSession">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="user_id" property="userId" jdbcType="BIGINT"/>
        <result column="session_id" property="sessionId" jdbcType="VARCHAR"/>
        <result column="access_token" property="accessToken" jdbcType="VARCHAR"/>
        <result column="refresh_token" property="refreshToken" jdbcType="VARCHAR"/>
        <result column="device_type" property="deviceType" jdbcType="VARCHAR"/>
        <result column="device_info" property="deviceInfo" jdbcType="VARCHAR"/>
        <result column="ip_address" property="ipAddress" jdbcType="VARCHAR"/>
        <result column="user_agent" property="userAgent" jdbcType="VARCHAR"/>
        <result column="login_time" property="loginTime" jdbcType="TIMESTAMP"/>
        <result column="last_active_time" property="lastActiveTime" jdbcType="TIMESTAMP"/>
        <result column="expires_at" property="expiresAt" jdbcType="TIMESTAMP"/>
        <result column="status" property="status" jdbcType="TINYINT"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, user_id, session_id, access_token, refresh_token, device_type, device_info,
        ip_address, user_agent, login_time, last_active_time, expires_at, status,
        create_time, update_time
    </sql>

    <!-- 根据访问令牌查询会话 -->
    <select id="selectByAccessToken" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM user_session
        WHERE access_token = #{accessToken}
        AND status = 1
        AND expires_at &gt; NOW()
    </select>

    <!-- 根据刷新令牌查询会话 -->
    <select id="selectByRefreshToken" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM user_session
        WHERE refresh_token = #{refreshToken}
        AND status = 1
        AND expires_at &gt; NOW()
    </select>

    <!-- 根据会话ID查询会话 -->
    <select id="selectBySessionId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM user_session
        WHERE session_id = #{sessionId}
        AND status = 1
        AND expires_at &gt; NOW()
    </select>

    <!-- 查询用户的活跃会话列表 -->
    <select id="selectActiveSessionsByUserId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM user_session
        WHERE user_id = #{userId}
        AND status = 1
        AND expires_at &gt; NOW()
        ORDER BY last_active_time DESC
    </select>

    <!-- 更新最后活跃时间 -->
    <update id="updateLastActiveTime">
        UPDATE user_session
        SET last_active_time = NOW(),
            update_time = NOW()
        WHERE session_id = #{sessionId}
    </update>

    <!-- 更新会话状态 -->
    <update id="updateSessionStatus">
        UPDATE user_session
        SET status = #{status},
            update_time = NOW()
        WHERE id = #{id}
    </update>

    <!-- 根据用户ID失效所有会话 -->
    <update id="invalidateUserSessions">
        UPDATE user_session
        SET status = 0,
            update_time = NOW()
        WHERE user_id = #{userId}
        AND status = 1
    </update>

    <!-- 根据访问令牌失效会话 -->
    <update id="invalidateByAccessToken">
        UPDATE user_session
        SET status = 0,
            update_time = NOW()
        WHERE access_token = #{accessToken}
    </update>

    <!-- 清理过期会话 -->
    <delete id="deleteExpiredSessions">
        DELETE FROM user_session
        WHERE expires_at &lt; NOW()
        OR status = 0
    </delete>

    <!-- 更新Token信息 -->
    <update id="updateTokens">
        UPDATE user_session
        SET access_token = #{accessToken},
            refresh_token = #{refreshToken},
            expires_at = #{expiresAt},
            last_active_time = NOW(),
            update_time = NOW()
        WHERE id = #{id}
    </update>

</mapper>
