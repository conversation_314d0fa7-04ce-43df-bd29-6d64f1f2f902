<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jj.dao.mapper.PointsTransactionMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.jj.dao.model.points.PointsTransaction">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="user_id" property="userId" jdbcType="BIGINT"/>
        <result column="transaction_type" property="transactionType" jdbcType="TINYINT"/>
        <result column="points_amount" property="pointsAmount" jdbcType="INTEGER"/>
        <result column="before_points" property="beforePoints" jdbcType="INTEGER"/>
        <result column="after_points" property="afterPoints" jdbcType="INTEGER"/>
        <result column="business_type" property="businessType" jdbcType="VARCHAR"/>
        <result column="business_id" property="businessId" jdbcType="VARCHAR"/>
        <result column="description" property="description" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="del" property="del" jdbcType="TINYINT"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, user_id, transaction_type, points_amount, before_points, after_points,
        business_type, business_id, description, create_time, del
    </sql>

    <!-- 分页查询用户积分流水 -->
    <select id="selectUserTransactions" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM points_transaction
        WHERE user_id = #{userId}
        AND del = 0
        <if test="transactionType != null">
            AND transaction_type = #{transactionType}
        </if>
        ORDER BY create_time DESC
    </select>

    <!-- 查询用户积分流水列表 -->
    <select id="selectUserTransactionsList" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM points_transaction
        WHERE user_id = #{userId}
        AND del = 0
        <if test="transactionType != null">
            AND transaction_type = #{transactionType}
        </if>
        ORDER BY create_time DESC
        <if test="limit != null">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 统计用户积分流水 -->
    <select id="countUserTransactions" resultType="java.lang.Long">
        SELECT COUNT(1)
        FROM points_transaction
        WHERE user_id = #{userId}
        AND del = 0
        <if test="transactionType != null">
            AND transaction_type = #{transactionType}
        </if>
    </select>

</mapper>
