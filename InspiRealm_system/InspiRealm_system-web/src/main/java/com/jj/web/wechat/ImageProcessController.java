package com.jj.web.wechat;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jj.common.response.JsonResult;
import com.jj.core.annotation.WeChatAuth;
import com.jj.core.auth.UserContext;
import com.jj.core.domain.dto.image.*;
import com.jj.core.service.image.ImageProcessService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;

/**
 * 图片处理控制器
 * 
 * <AUTHOR>
 * @date 2024-07-29
 */
@Api(tags = "微信小程序端-图片处理")
@RestController
@RequestMapping("/api/wechat/image")
@Validated
@WeChatAuth
public class ImageProcessController {
    
    private static final Logger logger = LoggerFactory.getLogger(ImageProcessController.class);
    
    @Autowired
    private ImageProcessService imageProcessService;
    
    /**
     * 创建图片处理任务
     * 
     * @param reqDTO 处理请求
     * @return 处理响应
     */
    @ApiOperation(value = "创建图片处理任务", notes = "提交图片处理请求，消耗积分，返回任务编号")
    @PostMapping("/process")
    public JsonResult<ImageProcessResDTO> processImage(@Valid @RequestBody ImageProcessReqDTO reqDTO) {
        Long userId = UserContext.getCurrentWeChatUserId();
        logger.info("创建图片处理任务请求，userId: {}, reqDTO: {}", userId, reqDTO);
        
        try {
            ImageProcessResDTO resDTO = imageProcessService.createProcessTask(userId, reqDTO);
            logger.info("创建图片处理任务成功，userId: {}, taskNo: {}", userId, resDTO.getTaskNo());
            return JsonResult.success("任务创建成功", resDTO);
            
        } catch (Exception e) {
            logger.error("创建图片处理任务失败，userId: {}", userId, e);
            return JsonResult.error(e.getMessage());
        }
    }
    
    /**
     * 查询任务状态
     * 
     * @param taskNo 任务编号
     * @return 任务状态
     */
    @ApiOperation(value = "查询任务状态", notes = "根据任务编号查询图片处理任务的状态和结果")
    @GetMapping("/task/{taskNo}")
    public JsonResult<TaskStatusDTO> getTaskStatus(
            @ApiParam(value = "任务编号", required = true) @PathVariable @NotBlank String taskNo) {
        
        Long userId = UserContext.getCurrentWeChatUserId();
        logger.info("查询任务状态请求，userId: {}, taskNo: {}", userId, taskNo);
        
        try {
            TaskStatusDTO statusDTO = imageProcessService.getTaskStatus(taskNo, userId);
            logger.info("查询任务状态成功，userId: {}, taskNo: {}, status: {}", userId, taskNo, statusDTO.getStatus());
            return JsonResult.success(statusDTO);
            
        } catch (Exception e) {
            logger.error("查询任务状态失败，userId: {}, taskNo: {}", userId, taskNo, e);
            return JsonResult.error(e.getMessage());
        }
    }
    
    /**
     * 查询我的图片
     * 
     * @param page 页码
     * @param size 每页大小
     * @param isPublic 是否公开
     * @return 图片分页数据
     */
    @ApiOperation(value = "查询我的图片", notes = "分页查询当前用户的处理后图片")
    @GetMapping("/my-images")
    public JsonResult<IPage<ProcessedImageDTO>> getMyImages(
            @ApiParam(value = "页码", example = "1") @RequestParam(defaultValue = "1") @Min(1) Integer page,
            @ApiParam(value = "每页大小", example = "10") @RequestParam(defaultValue = "10") @Min(1) @Max(100) Integer size,
            @ApiParam(value = "是否公开 0:私有 1:公开") @RequestParam(required = false) Integer isPublic) {
        
        Long userId = UserContext.getCurrentWeChatUserId();
        logger.info("查询我的图片请求，userId: {}, page: {}, size: {}, isPublic: {}", userId, page, size, isPublic);
        
        try {
            IPage<ProcessedImageDTO> imagePage = imageProcessService.getUserImages(userId, page, size, isPublic);
            logger.info("查询我的图片成功，userId: {}, total: {}", userId, imagePage.getTotal());
            return JsonResult.success(imagePage);
            
        } catch (Exception e) {
            logger.error("查询我的图片失败，userId: {}", userId, e);
            return JsonResult.error(e.getMessage());
        }
    }

    /**
     * 查询我的任务
     *
     * @param page 页码
     * @param size 每页大小
     * @param status 任务状态
     * @param processType 处理类型
     * @return 任务分页数据
     */
    @ApiOperation(value = "查询我的任务", notes = "分页查询当前用户的所有处理任务")
    @GetMapping("/my-tasks")
    public JsonResult<IPage<TaskInfoDTO>> getMyTasks(
            @ApiParam(value = "页码", example = "1") @RequestParam(defaultValue = "1") @Min(1) Integer page,
            @ApiParam(value = "每页大小", example = "10") @RequestParam(defaultValue = "10") @Min(1) @Max(100) Integer size,
            @ApiParam(value = "任务状态 1:待处理 2:处理中 3:处理成功 4:处理失败 5:已取消") @RequestParam(required = false) Integer status,
            @ApiParam(value = "处理类型") @RequestParam(required = false) String processType) {

        Long userId = UserContext.getCurrentWeChatUserId();
        logger.info("查询我的任务请求，userId: {}, page: {}, size: {}, status: {}, processType: {}", userId, page, size, status, processType);

        try {
            IPage<TaskInfoDTO> taskPage = imageProcessService.getUserTasks(userId, page, size, status, processType);
            logger.info("查询我的任务成功，userId: {}, total: {}", userId, taskPage.getTotal());
            return JsonResult.success(taskPage);

        } catch (Exception e) {
            logger.error("查询我的任务失败，userId: {}", userId, e);
            return JsonResult.error(e.getMessage());
        }
    }

    /**
     * 设置图片公开状态
     *
     * @param imageId 图片ID
     * @param reqDTO 请求参数
     * @return 操作结果
     */
    @ApiOperation(value = "设置图片公开状态", notes = "设置图片是否公开到广场")
    @PostMapping("/{imageId}/public")
    public JsonResult<Void> setImagePublicStatus(
            @ApiParam(value = "图片ID", required = true) @PathVariable @Min(1) Long imageId,
            @Valid @RequestBody SetImagePublicReqDTO reqDTO) {
        
        Long userId = UserContext.getCurrentWeChatUserId();
        logger.info("设置图片公开状态请求，userId: {}, imageId: {}, isPublic: {}", userId, imageId, reqDTO.getIsPublic());
        
        try {
            imageProcessService.setImagePublicStatus(imageId, reqDTO.getIsPublic(), userId);
            
            String message = reqDTO.getIsPublic() == 1 ? "图片已公开到广场" : "图片已设为私有";
            logger.info("设置图片公开状态成功，userId: {}, imageId: {}, isPublic: {}", userId, imageId, reqDTO.getIsPublic());
            return JsonResult.success(message);
            
        } catch (Exception e) {
            logger.error("设置图片公开状态失败，userId: {}, imageId: {}", userId, imageId, e);
            return JsonResult.error(e.getMessage());
        }
    }

    /**
     * 润色提示词
     *
     * @param reqDTO 润色请求
     * @return 润色后的提示词
     */
    @ApiOperation(value = "润色提示词", notes = "使用AI优化提示词，避免违规内容")
    @PostMapping("/polish-prompt")
    public JsonResult<String> polishPrompt(@Valid @RequestBody PolishPromptReqDTO reqDTO) {
        Long userId = UserContext.getCurrentWeChatUserId();
        logger.info("润色提示词请求，userId: {}, originalPrompt: {}", userId, reqDTO.getOriginalPrompt());

        try {
            String polishedPrompt = imageProcessService.polishPrompt(reqDTO.getOriginalPrompt(), reqDTO.getImageBase64());
            logger.info("润色提示词成功，userId: {}, polishedPrompt: {}", userId, polishedPrompt);
            return JsonResult.success("润色成功", polishedPrompt);

        } catch (Exception e) {
            logger.error("润色提示词失败，userId: {}, originalPrompt: {}, hasImage: {}",
                userId, reqDTO.getOriginalPrompt(), (reqDTO.getImageBase64() != null && !reqDTO.getImageBase64().isEmpty()), e);
            return JsonResult.error(e.getMessage());
        }
    }



    /**
     * 重新处理任务
     *
     * @param taskNo 任务编号
     * @return 新任务信息
     */
    @ApiOperation(value = "重新处理任务", notes = "基于失败的任务重新创建处理任务")
    @PostMapping("/task/{taskNo}/retry")
    public JsonResult<ImageProcessResDTO> retryTask(
            @ApiParam(value = "任务编号", required = true) @PathVariable @NotBlank String taskNo) {

        Long userId = UserContext.getCurrentWeChatUserId();
        logger.info("重新处理任务请求，userId: {}, taskNo: {}", userId, taskNo);

        try {
            ImageProcessResDTO resDTO = imageProcessService.retryTask(taskNo, userId);
            logger.info("重新处理任务成功，userId: {}, originalTaskNo: {}, newTaskNo: {}",
                userId, taskNo, resDTO.getTaskNo());
            return JsonResult.success("任务重新提交成功", resDTO);

        } catch (Exception e) {
            logger.error("重新处理任务失败，userId: {}, taskNo: {}", userId, taskNo, e);
            return JsonResult.error(e.getMessage());
        }
    }

    /**
     * 删除图片
     *
     * @param imageId 图片ID
     * @return 操作结果
     */
    @ApiOperation(value = "删除图片", notes = "删除指定的图片")
    @DeleteMapping("/{imageId}")
    public JsonResult<Void> deleteImage(
            @ApiParam(value = "图片ID", required = true) @PathVariable @Min(1) Long imageId) {

        Long userId = UserContext.getCurrentWeChatUserId();
        logger.info("删除图片请求，userId: {}, imageId: {}", userId, imageId);

        try {
            imageProcessService.deleteUserImage(imageId, userId);
            logger.info("删除图片成功，userId: {}, imageId: {}", userId, imageId);
            return JsonResult.success("删除成功");

        } catch (Exception e) {
            logger.error("删除图片失败，userId: {}, imageId: {}", userId, imageId, e);
            return JsonResult.error(e.getMessage());
        }
    }

    /**
     * 删除任务
     *
     * @param taskNo 任务编号
     * @return 操作结果
     */
    @ApiOperation(value = "删除任务", notes = "删除指定的图片处理任务")
    @DeleteMapping("/task/{taskNo}")
    public JsonResult<Void> deleteTask(
            @ApiParam(value = "任务编号", required = true) @PathVariable @NotBlank String taskNo) {

        Long userId = UserContext.getCurrentWeChatUserId();
        logger.info("删除任务请求，userId: {}, taskNo: {}", userId, taskNo);

        try {
            imageProcessService.deleteTask(taskNo, userId);
            logger.info("删除任务成功，userId: {}, taskNo: {}", userId, taskNo);
            return JsonResult.success("删除成功");

        } catch (Exception e) {
            logger.error("删除任务失败，userId: {}, taskNo: {}", userId, taskNo, e);
            return JsonResult.error(e.getMessage());
        }
    }

    /**
     * 设置图片公开状态请求DTO
     */
    public static class SetImagePublicReqDTO {
        
        @ApiParam(value = "是否公开 0:私有 1:公开", required = true, example = "1")
        @Min(value = 0, message = "isPublic值必须为0或1")
        @Max(value = 1, message = "isPublic值必须为0或1")
        private Integer isPublic;
        
        public Integer getIsPublic() {
            return isPublic;
        }
        
        public void setIsPublic(Integer isPublic) {
            this.isPublic = isPublic;
        }
        
        @Override
        public String toString() {
            return "SetImagePublicReqDTO{" +
                    "isPublic=" + isPublic +
                    '}';
        }
    }
}
