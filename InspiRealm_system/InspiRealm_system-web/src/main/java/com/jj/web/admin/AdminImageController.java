package com.jj.web.admin;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jj.common.response.JsonResult;
import com.jj.core.annotation.AdminAuth;
import com.jj.core.domain.dto.admin.ImageManageDTO;
import com.jj.core.service.admin.AdminImageService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.Size;

/**
 * 管理员图片管理控制器
 * 
 * <AUTHOR>
 * @date 2024-07-29
 */
@Api(tags = "管理员端-图片管理")
@RestController
@RequestMapping("/api/admin/images")
@Validated
@AdminAuth
public class AdminImageController {
    
    private static final Logger logger = LoggerFactory.getLogger(AdminImageController.class);
    
    @Autowired
    private AdminImageService adminImageService;
    
    /**
     * 分页查询图片列表
     * 
     * @param page 页码
     * @param size 每页大小
     * @param keyword 搜索关键词
     * @param isPublic 是否公开
     * @param auditStatus 审核状态
     * @return 图片分页数据
     */
    @ApiOperation(value = "分页查询图片列表", notes = "支持按文件名、处理类型搜索，支持按公开状态和审核状态筛选")
    @GetMapping
    public JsonResult<IPage<ImageManageDTO>> getImageList(
            @ApiParam(value = "页码", example = "1") @RequestParam(defaultValue = "1") @Min(1) Integer page,
            @ApiParam(value = "每页大小", example = "10") @RequestParam(defaultValue = "10") @Min(1) @Max(100) Integer size,
            @ApiParam(value = "搜索关键词") @RequestParam(required = false) String keyword,
            @ApiParam(value = "是否公开 0:私有 1:公开") @RequestParam(required = false) Integer isPublic,
            @ApiParam(value = "审核状态 0:待审核 1:审核通过 2:审核拒绝") @RequestParam(required = false) Integer auditStatus) {
        
        logger.info("分页查询图片列表请求，page: {}, size: {}, keyword: {}, isPublic: {}, auditStatus: {}", 
                page, size, keyword, isPublic, auditStatus);
        
        try {
            IPage<ImageManageDTO> imagePage = adminImageService.getImageList(page, size, keyword, isPublic, auditStatus);
            logger.info("分页查询图片列表成功，total: {}", imagePage.getTotal());
            return JsonResult.success(imagePage);
            
        } catch (Exception e) {
            logger.error("分页查询图片列表失败", e);
            return JsonResult.error(e.getMessage());
        }
    }
    
    /**
     * 获取图片详情
     * 
     * @param imageId 图片ID
     * @return 图片详情
     */
    @ApiOperation(value = "获取图片详情", notes = "获取指定图片的详细信息")
    @GetMapping("/{imageId}")
    public JsonResult<ImageManageDTO> getImageDetail(
            @ApiParam(value = "图片ID", required = true) @PathVariable @Min(1) Long imageId) {
        
        logger.info("获取图片详情请求，imageId: {}", imageId);
        
        try {
            ImageManageDTO imageDTO = adminImageService.getImageDetail(imageId);
            logger.info("获取图片详情成功，imageId: {}", imageId);
            return JsonResult.success(imageDTO);
            
        } catch (Exception e) {
            logger.error("获取图片详情失败，imageId: {}", imageId, e);
            return JsonResult.error(e.getMessage());
        }
    }
    
    /**
     * 图片审核
     * 
     * @param imageId 图片ID
     * @param auditStatus 审核状态
     * @param auditRemark 审核备注
     * @return 操作结果
     */
    @ApiOperation(value = "图片审核", notes = "审核图片内容，通过或拒绝")
    @PostMapping("/{imageId}/audit")
    public JsonResult<Void> auditImage(
            @ApiParam(value = "图片ID", required = true) @PathVariable @Min(1) Long imageId,
            @ApiParam(value = "审核状态 1:审核通过 2:审核拒绝", required = true) @RequestParam @Min(1) @Max(2) Integer auditStatus,
            @ApiParam(value = "审核备注") @RequestParam(required = false) @Size(max = 200) String auditRemark) {
        
        logger.info("图片审核请求，imageId: {}, auditStatus: {}, auditRemark: {}", imageId, auditStatus, auditRemark);
        
        try {
            adminImageService.auditImage(imageId, auditStatus, auditRemark);
            
            String message = auditStatus == 1 ? "图片审核通过" : "图片审核拒绝";
            logger.info("图片审核成功，imageId: {}, auditStatus: {}", imageId, auditStatus);
            return JsonResult.success(message);
            
        } catch (Exception e) {
            logger.error("图片审核失败，imageId: {}", imageId, e);
            return JsonResult.error(e.getMessage());
        }
    }
    
    /**
     * 删除图片
     * 
     * @param imageId 图片ID
     * @return 操作结果
     */
    @ApiOperation(value = "删除图片", notes = "删除指定图片")
    @DeleteMapping("/{imageId}")
    @AdminAuth(requireSuperAdmin = true)
    public JsonResult<Void> deleteImage(
            @ApiParam(value = "图片ID", required = true) @PathVariable @Min(1) Long imageId) {
        
        logger.info("删除图片请求，imageId: {}", imageId);
        
        try {
            adminImageService.deleteImage(imageId);
            logger.info("删除图片成功，imageId: {}", imageId);
            return JsonResult.success("图片删除成功");
            
        } catch (Exception e) {
            logger.error("删除图片失败，imageId: {}", imageId, e);
            return JsonResult.error(e.getMessage());
        }
    }
    
    /**
     * 获取图片统计信息
     * 
     * @return 统计信息
     */
    @ApiOperation(value = "获取图片统计信息", notes = "获取图片相关的统计数据")
    @GetMapping("/stats")
    public JsonResult<AdminImageService.ImageStatsResult> getImageStats() {
        logger.info("获取图片统计信息请求");
        
        try {
            AdminImageService.ImageStatsResult stats = adminImageService.getImageStats();
            logger.info("获取图片统计信息成功");
            return JsonResult.success(stats);
            
        } catch (Exception e) {
            logger.error("获取图片统计信息失败", e);
            return JsonResult.error(e.getMessage());
        }
    }
}
