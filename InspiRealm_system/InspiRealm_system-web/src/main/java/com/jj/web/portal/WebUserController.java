package com.jj.web.portal;

import com.jj.common.response.JsonResult;
import com.jj.core.annotation.WebAuth;
import com.jj.core.domain.dto.portal.web.*;
import com.jj.core.service.user.WebUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * Web端用户管理控制器
 *
 * <AUTHOR>
 * @date 2025-01-05
 */
@Api(tags = "Web端-用户管理")
@RestController
@RequestMapping("/api/web/user")
@Validated
public class WebUserController {

    private static final Logger logger = LoggerFactory.getLogger(WebUserController.class);

    @Autowired
    private WebUserService webUserService;

    /**
     * 获取用户信息
     *
     * @return 用户信息
     */
    @ApiOperation(value = "获取用户信息", notes = "获取当前登录用户的详细信息")
    @GetMapping("/profile")
    @WebAuth("获取用户信息")
    public JsonResult<WebUserProfileResDTO> getUserProfile() {
        logger.info("收到获取用户信息请求");

        try {
            WebUserProfileResDTO resDTO = webUserService.getUserProfile();
            return JsonResult.success(resDTO);
        } catch (Exception e) {
            logger.error("获取用户信息失败", e);
            return JsonResult.error(e.getMessage());
        }
    }

    /**
     * 更新用户信息
     *
     * @param reqDTO 更新请求
     * @return 更新结果
     */
    @ApiOperation(value = "更新用户信息", notes = "更新当前用户的个人信息")
    @PutMapping("/profile")
    @WebAuth("更新用户信息")
    public JsonResult<Void> updateUserProfile(@Valid @RequestBody WebUpdateUserProfileReqDTO reqDTO) {
        logger.info("收到更新用户信息请求");

        try {
            webUserService.updateUserProfile(reqDTO);
            logger.info("用户信息更新成功");
            return JsonResult.success();
        } catch (Exception e) {
            logger.error("用户信息更新失败", e);
            return JsonResult.error(e.getMessage());
        }
    }

    /**
     * 修改密码
     *
     * @param reqDTO 修改密码请求
     * @return 修改结果
     */
    @ApiOperation(value = "修改密码", notes = "修改当前用户的登录密码")
    @PostMapping("/change-password")
    @WebAuth("修改密码")
    public JsonResult<Void> changePassword(@Valid @RequestBody WebChangePasswordReqDTO reqDTO) {
        logger.info("收到修改密码请求");

        try {
            webUserService.changePassword(reqDTO);
            logger.info("密码修改成功");
            return JsonResult.success();
        } catch (Exception e) {
            logger.error("密码修改失败", e);
            return JsonResult.error(e.getMessage());
        }
    }

    /**
     * 获取用户积分信息
     *
     * @return 积分信息
     */
    @ApiOperation(value = "获取积分信息", notes = "获取当前用户的积分余额和消费记录")
    @GetMapping("/points")
    @WebAuth("获取积分信息")
    public JsonResult<WebUserPointsResDTO> getUserPoints() {
        logger.info("收到获取积分信息请求");

        try {
            WebUserPointsResDTO resDTO = webUserService.getUserPoints();
            return JsonResult.success(resDTO);
        } catch (Exception e) {
            logger.error("获取积分信息失败", e);
            return JsonResult.error(e.getMessage());
        }
    }

    /**
     * 获取积分消费记录
     *
     * @param reqDTO 查询请求
     * @return 消费记录
     */
    @ApiOperation(value = "获取积分记录", notes = "获取用户的积分消费和充值记录")
    @PostMapping("/points/history")
    @WebAuth("获取积分记录")
    public JsonResult<WebPointsHistoryResDTO> getPointsHistory(@Valid @RequestBody WebPointsHistoryReqDTO reqDTO) {
        logger.info("收到获取积分记录请求，页码: {}", reqDTO.getPage());

        try {
            WebPointsHistoryResDTO resDTO = webUserService.getPointsHistory(reqDTO);
            return JsonResult.success(resDTO);
        } catch (Exception e) {
            logger.error("获取积分记录失败", e);
            return JsonResult.error(e.getMessage());
        }
    }

    /**
     * 获取用户统计信息
     *
     * @return 统计信息
     */
    @ApiOperation(value = "获取用户统计", notes = "获取用户的使用统计信息")
    @GetMapping("/statistics")
    @WebAuth("获取用户统计")
    public JsonResult<WebUserStatisticsResDTO> getUserStatistics() {
        logger.info("收到获取用户统计请求");

        try {
            WebUserStatisticsResDTO resDTO = webUserService.getUserStatistics();
            return JsonResult.success(resDTO);
        } catch (Exception e) {
            logger.error("获取用户统计失败", e);
            return JsonResult.error(e.getMessage());
        }
    }
}