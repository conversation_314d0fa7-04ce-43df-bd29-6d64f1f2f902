package com.jj.web.admin;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jj.common.response.JsonResult;
import com.jj.core.annotation.AdminAuth;
import com.jj.core.domain.dto.admin.PointsManageReqDTO;
import com.jj.core.domain.dto.admin.UserManageDTO;
import com.jj.core.domain.dto.admin.UserStatusUpdateReqDTO;
import com.jj.core.service.admin.AdminUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;

/**
 * 管理员用户管理控制器
 * 
 * <AUTHOR>
 * @date 2024-07-29
 */
@Api(tags = "管理员端-用户管理")
@RestController
@RequestMapping("/api/admin/users")
@Validated
@AdminAuth
public class AdminUserController {
    
    private static final Logger logger = LoggerFactory.getLogger(AdminUserController.class);
    
    @Autowired
    private AdminUserService adminUserService;
    
    /**
     * 分页查询用户列表
     * 
     * @param page 页码
     * @param size 每页大小
     * @param keyword 搜索关键词
     * @param status 用户状态
     * @return 用户分页数据
     */
    @ApiOperation(value = "分页查询用户列表", notes = "支持按昵称、手机号、邮箱搜索，支持按状态筛选")
    @GetMapping
    public JsonResult<IPage<UserManageDTO>> getUserList(
            @ApiParam(value = "页码", example = "1") @RequestParam(defaultValue = "1") @Min(1) Integer page,
            @ApiParam(value = "每页大小", example = "10") @RequestParam(defaultValue = "10") @Min(1) @Max(100) Integer size,
            @ApiParam(value = "搜索关键词") @RequestParam(required = false) String keyword,
            @ApiParam(value = "用户状态 0:禁用 1:正常") @RequestParam(required = false) Integer status) {
        
        logger.info("分页查询用户列表请求，page: {}, size: {}, keyword: {}, status: {}", page, size, keyword, status);
        
        try {
            IPage<UserManageDTO> userPage = adminUserService.getUserList(page, size, keyword, status);
            logger.info("分页查询用户列表成功，total: {}", userPage.getTotal());
            return JsonResult.success(userPage);
            
        } catch (Exception e) {
            logger.error("分页查询用户列表失败", e);
            return JsonResult.error(e.getMessage());
        }
    }
    
    /**
     * 获取用户详情
     * 
     * @param userId 用户ID
     * @return 用户详情
     */
    @ApiOperation(value = "获取用户详情", notes = "获取指定用户的详细信息")
    @GetMapping("/{userId}")
    public JsonResult<UserManageDTO> getUserDetail(
            @ApiParam(value = "用户ID", required = true) @PathVariable @Min(1) Long userId) {
        
        logger.info("获取用户详情请求，userId: {}", userId);
        
        try {
            UserManageDTO userDTO = adminUserService.getUserDetail(userId);
            logger.info("获取用户详情成功，userId: {}", userId);
            return JsonResult.success(userDTO);
            
        } catch (Exception e) {
            logger.error("获取用户详情失败，userId: {}", userId, e);
            return JsonResult.error(e.getMessage());
        }
    }
    
    /**
     * 更新用户状态
     * 
     * @param userId 用户ID
     * @param reqDTO 状态更新请求
     * @return 操作结果
     */
    @ApiOperation(value = "更新用户状态", notes = "启用或禁用用户账号")
    @PutMapping("/{userId}/status")
    public JsonResult<Void> updateUserStatus(
            @ApiParam(value = "用户ID", required = true) @PathVariable @Min(1) Long userId,
            @Valid @RequestBody UserStatusUpdateReqDTO reqDTO) {
        
        logger.info("更新用户状态请求，userId: {}, status: {}", userId, reqDTO.getStatus());
        
        try {
            adminUserService.updateUserStatus(userId, reqDTO);
            
            String message = reqDTO.getStatus() == 1 ? "用户已启用" : "用户已禁用";
            logger.info("更新用户状态成功，userId: {}, status: {}", userId, reqDTO.getStatus());
            return JsonResult.success(message);
            
        } catch (Exception e) {
            logger.error("更新用户状态失败，userId: {}", userId, e);
            return JsonResult.error(e.getMessage());
        }
    }
    
    /**
     * 积分管理操作
     * 
     * @param reqDTO 积分管理请求
     * @return 操作结果
     */
    @ApiOperation(value = "积分管理操作", notes = "为用户充值或扣除积分")
    @PostMapping("/points/manage")
    @AdminAuth(requireSuperAdmin = true)
    public JsonResult<Void> manageUserPoints(@Valid @RequestBody PointsManageReqDTO reqDTO) {
        logger.info("积分管理操作请求，userId: {}, amount: {}, type: {}", 
                reqDTO.getUserId(), reqDTO.getPointsAmount(), reqDTO.getOperationType());
        
        try {
            adminUserService.manageUserPoints(reqDTO);
            
            String message = reqDTO.getOperationType() == 1 ? "积分充值成功" : "积分扣除成功";
            logger.info("积分管理操作成功，userId: {}, amount: {}, type: {}", 
                    reqDTO.getUserId(), reqDTO.getPointsAmount(), reqDTO.getOperationType());
            return JsonResult.success(message);
            
        } catch (Exception e) {
            logger.error("积分管理操作失败，userId: {}", reqDTO.getUserId(), e);
            return JsonResult.error(e.getMessage());
        }
    }
}
