package com.jj.web.wechat;

import com.jj.common.response.JsonResult;
import com.jj.core.annotation.NoLogin;
import com.jj.core.domain.dto.wechat.WeChatLoginReqDTO;
import com.jj.core.domain.dto.wechat.WeChatLoginResDTO;
import com.jj.core.service.user.WeChatAuthService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 微信认证控制器
 * 
 * <AUTHOR>
 * @date 2024-07-29
 */
@Api(tags = "微信小程序端-认证管理")
@RestController
@RequestMapping("/api/wechat/auth")
@Validated
public class WeChatAuthController {
    
    private static final Logger logger = LoggerFactory.getLogger(WeChatAuthController.class);
    
    @Autowired
    private WeChatAuthService weChatAuthService;
    
    /**
     * 微信登录
     * 
     * @param reqDTO 登录请求
     * @return 登录响应
     */
    @ApiOperation(value = "微信登录", notes = "通过微信授权码进行登录，返回用户信息和访问令牌")
    @PostMapping("/login")
    @NoLogin
    public JsonResult<WeChatLoginResDTO> login(@Valid @RequestBody WeChatLoginReqDTO reqDTO) {
        logger.info("收到微信登录请求，code: {}", reqDTO.getCode());
        
        try {
            WeChatLoginResDTO resDTO = weChatAuthService.login(reqDTO);
            logger.info("微信登录成功，userId: {}", resDTO.getUserInfo().getId());
            return JsonResult.success(resDTO);
            
        } catch (Exception e) {
            logger.error("微信登录失败", e);
            return JsonResult.error(e.getMessage());
        }
    }
    
    /**
     * 登出
     * 
     * @return 响应结果
     */
    @ApiOperation(value = "登出", notes = "用户登出，清除服务端Token")
    @PostMapping("/logout")
    public JsonResult<Void> logout() {
        // 这里可以从请求头获取Token并清除
        // 由于使用了ThreadLocal，Token会在请求结束后自动清除
        logger.info("用户登出成功");
        return JsonResult.success();
    }
}
