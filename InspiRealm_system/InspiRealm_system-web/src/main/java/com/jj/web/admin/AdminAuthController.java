package com.jj.web.admin;

import com.jj.common.response.JsonResult;
import com.jj.core.domain.dto.admin.AdminLoginReqDTO;
import com.jj.core.domain.dto.admin.AdminLoginResDTO;
import com.jj.core.domain.dto.admin.AdminChangePasswordReqDTO;
import com.jj.core.service.admin.AdminAuthService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

/**
 * 管理员认证控制器
 * 
 * <AUTHOR>
 * @date 2024-07-29
 */
@Api(tags = "管理员端-认证管理")
@RestController
@RequestMapping("/api/admin/auth")
@Validated
public class AdminAuthController {
    
    private static final Logger logger = LoggerFactory.getLogger(AdminAuthController.class);
    
    @Autowired
    private AdminAuthService adminAuthService;
    
    /**
     * 管理员登录
     * 
     * @param reqDTO 登录请求
     * @param request HTTP请求
     * @return 登录响应
     */
    @ApiOperation(value = "管理员登录", notes = "管理员用户名密码登录，返回访问Token")
    @PostMapping("/login")
    public JsonResult<AdminLoginResDTO> login(@Valid @RequestBody AdminLoginReqDTO reqDTO, HttpServletRequest request) {
        logger.info("管理员登录请求，username: {}", reqDTO.getUsername());
        
        try {
            AdminLoginResDTO resDTO = adminAuthService.login(reqDTO, request);
            logger.info("管理员登录成功，username: {}, adminId: {}", reqDTO.getUsername(), resDTO.getAdminId());
            return JsonResult.success("登录成功", resDTO);
            
        } catch (Exception e) {
            logger.error("管理员登录失败，username: {}", reqDTO.getUsername(), e);
            return JsonResult.error(e.getMessage());
        }
    }

    /**
     * 修改密码
     *
     * @param reqDTO 修改密码请求
     * @param request HTTP请求
     * @return 修改结果
     */
    @ApiOperation(value = "修改密码", notes = "管理员修改登录密码")
    @PostMapping("/change-password")
    public JsonResult<Void> changePassword(@Valid @RequestBody AdminChangePasswordReqDTO reqDTO, HttpServletRequest request) {
        logger.info("管理员修改密码请求");

        try {
            adminAuthService.changePassword(reqDTO, request);
            logger.info("管理员修改密码成功");
            return JsonResult.success("密码修改成功");

        } catch (Exception e) {
            logger.error("管理员修改密码失败", e);
            return JsonResult.error(e.getMessage());
        }
    }
}
