package com.jj.web.aspect;

import com.jj.common.exception.BizException;
import com.jj.core.annotation.AdminRequired;
import com.jj.core.annotation.NoLogin;
import com.jj.core.annotation.WeChatAuth;
import com.jj.core.auth.AdminTokenInfo;
import com.jj.core.auth.TokenManager;
import com.jj.core.auth.UserContext;
import com.jj.core.auth.WeChatTokenInfo;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;

/**
 * 权限验证切面
 * 负责处理@NoLogin、@WeChatAuth、@AdminRequired注解的权限验证
 * 
 * <AUTHOR>
 * @date 2024-07-29
 */
@Aspect
@Component
@Order(1)
public class AuthAspect {
    
    private static final Logger logger = LoggerFactory.getLogger(AuthAspect.class);
    
    @Autowired
    private TokenManager tokenManager;
    
    /**
     * 权限验证切点
     */
    @Around("@annotation(com.jj.core.annotation.NoLogin) || " +
            "@annotation(com.jj.core.annotation.WeChatAuth) || " +
            "@annotation(com.jj.core.annotation.AdminRequired) || " +
            "@within(com.jj.core.annotation.NoLogin) || " +
            "@within(com.jj.core.annotation.WeChatAuth) || " +
            "@within(com.jj.core.annotation.AdminRequired)")
    public Object doAuth(ProceedingJoinPoint joinPoint) throws Throwable {
        try {
            // 获取方法和类上的注解
            MethodSignature signature = (MethodSignature) joinPoint.getSignature();
            Method method = signature.getMethod();
            Class<?> targetClass = joinPoint.getTarget().getClass();
            
            // 检查方法级别的注解
            NoLogin noLogin = method.getAnnotation(NoLogin.class);
            WeChatAuth weChatAuth = method.getAnnotation(WeChatAuth.class);
            AdminRequired adminRequired = method.getAnnotation(AdminRequired.class);
            
            // 如果方法上没有注解，检查类级别的注解
            if (noLogin == null && weChatAuth == null && adminRequired == null) {
                noLogin = targetClass.getAnnotation(NoLogin.class);
                weChatAuth = targetClass.getAnnotation(WeChatAuth.class);
                adminRequired = targetClass.getAnnotation(AdminRequired.class);
            }
            
            // 如果标记了@NoLogin，尝试获取用户信息但不强制要求登录
            if (noLogin != null) {
                logger.debug("接口无需登录验证，但尝试获取用户信息: {}", method.getName());

                // 获取请求对象
                ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
                if (attributes != null) {
                    HttpServletRequest request = attributes.getRequest();
                    String token = getTokenFromRequest(request);

                    // 如果有Token，尝试验证并设置用户上下文
                    if (token != null && !token.trim().isEmpty()) {
                        try {
                            // 优先尝试微信用户Token
                            WeChatTokenInfo weChatTokenInfo = tokenManager.validateWeChatToken(token);
                            if (weChatTokenInfo != null) {
                                UserContext.setCurrentWeChatUser(weChatTokenInfo);
                                logger.debug("@NoLogin接口获取到微信用户信息: userId={}", weChatTokenInfo.getUserId());
                            } else {
                                // 尝试管理员Token
                                AdminTokenInfo adminTokenInfo = tokenManager.validateAdminToken(token);
                                if (adminTokenInfo != null) {
                                    UserContext.setCurrentAdmin(adminTokenInfo);
                                    logger.debug("@NoLogin接口获取到管理员信息: adminId={}", adminTokenInfo.getAdminId());
                                }
                            }
                        } catch (Exception e) {
                            // Token验证失败不影响@NoLogin接口的执行
                            logger.debug("@NoLogin接口Token验证失败，继续以游客身份访问: {}", e.getMessage());
                        }
                    } else {
                        logger.debug("@NoLogin接口未提供Token，以游客身份访问");
                    }
                }

                return joinPoint.proceed();
            }
            
            // 获取请求对象
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes == null) {
                throw new BizException("无法获取请求上下文");
            }
            HttpServletRequest request = attributes.getRequest();
            
            // 获取Token
            String token = getTokenFromRequest(request);
            if (token == null || token.trim().isEmpty()) {
                throw new BizException("未提供认证Token");
            }
            
            // 微信用户权限验证
            if (weChatAuth != null) {
                WeChatTokenInfo tokenInfo = tokenManager.validateWeChatToken(token);
                if (tokenInfo == null) {
                    throw new BizException("微信用户认证失败，请重新登录");
                }
                
                // 设置用户上下文
                UserContext.setCurrentWeChatUser(tokenInfo);
                logger.debug("微信用户认证成功: userId={}, openId={}", tokenInfo.getUserId(), tokenInfo.getOpenId());
                
                return joinPoint.proceed();
            }
            
            // 管理员权限验证
            if (adminRequired != null) {
                AdminTokenInfo tokenInfo = tokenManager.validateAdminToken(token);
                if (tokenInfo == null) {
                    throw new BizException("管理员认证失败，请重新登录");
                }
                
                // 设置管理员上下文
                UserContext.setCurrentAdmin(tokenInfo);
                logger.debug("管理员认证成功: adminId={}, username={}", tokenInfo.getAdminId(), tokenInfo.getUsername());
                
                return joinPoint.proceed();
            }
            
            // 如果没有任何权限注解，默认需要登录（可以是微信用户或管理员）
            WeChatTokenInfo weChatTokenInfo = tokenManager.validateWeChatToken(token);
            if (weChatTokenInfo != null) {
                UserContext.setCurrentWeChatUser(weChatTokenInfo);
                logger.debug("默认微信用户认证成功: userId={}", weChatTokenInfo.getUserId());
                return joinPoint.proceed();
            }
            
            AdminTokenInfo adminTokenInfo = tokenManager.validateAdminToken(token);
            if (adminTokenInfo != null) {
                UserContext.setCurrentAdmin(adminTokenInfo);
                logger.debug("默认管理员认证成功: adminId={}", adminTokenInfo.getAdminId());
                return joinPoint.proceed();
            }
            
            throw new BizException("认证失败，请重新登录");
            
        } finally {
            // 清除用户上下文（在请求结束后清除）
            UserContext.clear();
        }
    }
    
    /**
     * 从请求中获取Token
     * 优先从Header中获取，其次从参数中获取
     * 
     * @param request HTTP请求
     * @return Token
     */
    private String getTokenFromRequest(HttpServletRequest request) {
        // 从Header中获取Token
        String token = request.getHeader("Authorization");
        if (token != null && token.startsWith("Bearer ")) {
            return token.substring(7);
        }
        
        // 从Header中获取Token（不带Bearer前缀）
        token = request.getHeader("Token");
        if (token != null && !token.trim().isEmpty()) {
            return token.trim();
        }
        
        // 从参数中获取Token
        token = request.getParameter("token");
        if (token != null && !token.trim().isEmpty()) {
            return token.trim();
        }
        
        return null;
    }
}
