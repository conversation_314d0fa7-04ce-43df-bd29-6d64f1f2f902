package com.jj.web.wechat;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jj.common.response.JsonResult;
import com.jj.core.annotation.WeChatAuth;
import com.jj.core.auth.UserContext;
import com.jj.core.domain.dto.points.DailyCheckinResDTO;
import com.jj.core.domain.dto.points.PointsBalanceDTO;
import com.jj.core.domain.dto.points.PointsTransactionDTO;
import com.jj.core.service.points.PointsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;

/**
 * 积分管理控制器
 * 
 * <AUTHOR>
 * @date 2024-07-29
 */
@Api(tags = "微信小程序端-积分管理")
@RestController
@RequestMapping("/api/wechat/points")
@Validated
@WeChatAuth
public class PointsController {
    
    private static final Logger logger = LoggerFactory.getLogger(PointsController.class);
    
    @Autowired
    private PointsService pointsService;
    
    /**
     * 获取积分余额
     * 
     * @return 积分余额
     */
    @ApiOperation(value = "获取积分余额", notes = "获取当前登录用户的积分余额信息")
    @GetMapping("/balance")
    public JsonResult<PointsBalanceDTO> getPointsBalance() {
        Long userId = UserContext.getCurrentWeChatUserId();
        logger.info("获取积分余额请求，userId: {}", userId);
        
        try {
            PointsBalanceDTO balanceDTO = pointsService.getPointsBalance(userId);
            logger.info("获取积分余额成功，userId: {}, availablePoints: {}", userId, balanceDTO.getAvailablePoints());
            return JsonResult.success(balanceDTO);
            
        } catch (Exception e) {
            logger.error("获取积分余额失败，userId: {}", userId, e);
            return JsonResult.error(e.getMessage());
        }
    }
    
    /**
     * 分页查询积分流水
     * 
     * @param page 页码
     * @param size 每页大小
     * @param transactionType 交易类型
     * @return 积分流水分页数据
     */
    @ApiOperation(value = "分页查询积分流水", notes = "分页查询当前登录用户的积分流水记录")
    @GetMapping("/transactions")
    public JsonResult<IPage<PointsTransactionDTO>> getPointsTransactions(
            @ApiParam(value = "页码", example = "1") @RequestParam(defaultValue = "1") @Min(1) Integer page,
            @ApiParam(value = "每页大小", example = "10") @RequestParam(defaultValue = "10") @Min(1) @Max(100) Integer size,
            @ApiParam(value = "交易类型 1:获得 2:消费 3:锁定 4:解锁 5:回退") @RequestParam(required = false) Integer transactionType) {
        
        Long userId = UserContext.getCurrentWeChatUserId();
        logger.info("分页查询积分流水请求，userId: {}, page: {}, size: {}, transactionType: {}", userId, page, size, transactionType);
        
        try {
            IPage<PointsTransactionDTO> transactionPage = pointsService.getPointsTransactions(userId, page, size, transactionType);
            logger.info("分页查询积分流水成功，userId: {}, total: {}", userId, transactionPage.getTotal());
            return JsonResult.success(transactionPage);
            
        } catch (Exception e) {
            logger.error("分页查询积分流水失败，userId: {}", userId, e);
            return JsonResult.error(e.getMessage());
        }
    }
    
    /**
     * 每日签到
     * 
     * @return 签到结果
     */
    @ApiOperation(value = "每日签到", notes = "用户每日签到获取积分奖励")
    @PostMapping("/daily-checkin")
    public JsonResult<DailyCheckinResDTO> dailyCheckin() {
        Long userId = UserContext.getCurrentWeChatUserId();
        logger.info("每日签到请求，userId: {}", userId);
        
        try {
            DailyCheckinResDTO checkinResult = pointsService.dailyCheckin(userId);
            
            if (checkinResult.getSuccess()) {
                logger.info("每日签到成功，userId: {}, earnedPoints: {}", userId, checkinResult.getEarnedPoints());
                return JsonResult.success("签到成功", checkinResult);
            } else if (checkinResult.getAlreadyCheckedIn()) {
                logger.info("用户今日已签到，userId: {}", userId);
                return JsonResult.success("今日已签到", checkinResult);
            } else {
                logger.warn("每日签到失败，userId: {}", userId);
                return JsonResult.error("签到失败");
            }
            
        } catch (Exception e) {
            logger.error("每日签到失败，userId: {}", userId, e);
            return JsonResult.error(e.getMessage());
        }
    }
    
    /**
     * 获取签到状态
     * 
     * @return 签到状态
     */
    @ApiOperation(value = "获取签到状态", notes = "获取当前用户今日的签到状态")
    @GetMapping("/checkin-status")
    public JsonResult<CheckinStatusDTO> getCheckinStatus() {
        Long userId = UserContext.getCurrentWeChatUserId();
        logger.info("获取签到状态请求，userId: {}", userId);
        
        try {
            // 检查今日是否已签到
            String checkinKey = "daily_checkin:" + userId + ":" + java.time.LocalDateTime.now().toLocalDate();
            boolean hasCheckedIn = pointsService.hasCheckedInToday(userId);
            
            CheckinStatusDTO statusDTO = new CheckinStatusDTO();
            statusDTO.setHasCheckedIn(hasCheckedIn);
            statusDTO.setCheckinDate(java.time.LocalDate.now().toString());
            
            logger.info("获取签到状态成功，userId: {}, hasCheckedIn: {}", userId, hasCheckedIn);
            return JsonResult.success(statusDTO);
            
        } catch (Exception e) {
            logger.error("获取签到状态失败，userId: {}", userId, e);
            return JsonResult.error(e.getMessage());
        }
    }
    
    /**
     * 签到状态DTO
     */
    public static class CheckinStatusDTO {
        
        private Boolean hasCheckedIn;
        private String checkinDate;
        
        public Boolean getHasCheckedIn() {
            return hasCheckedIn;
        }
        
        public void setHasCheckedIn(Boolean hasCheckedIn) {
            this.hasCheckedIn = hasCheckedIn;
        }
        
        public String getCheckinDate() {
            return checkinDate;
        }
        
        public void setCheckinDate(String checkinDate) {
            this.checkinDate = checkinDate;
        }
        
        @Override
        public String toString() {
            return "CheckinStatusDTO{" +
                    "hasCheckedIn=" + hasCheckedIn +
                    ", checkinDate='" + checkinDate + '\'' +
                    '}';
        }
    }
}
