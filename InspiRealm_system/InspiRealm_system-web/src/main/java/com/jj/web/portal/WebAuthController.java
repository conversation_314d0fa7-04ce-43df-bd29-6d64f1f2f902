package com.jj.web.portal;

import com.jj.common.response.JsonResult;
import com.jj.core.annotation.NoLogin;
import com.jj.core.domain.dto.portal.web.*;
import com.jj.core.service.user.WebAuthService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * Web端用户认证控制器
 *
 * <AUTHOR>
 * @date 2025-01-05
 */
@Api(tags = "Web端-用户认证管理")
@RestController
@RequestMapping("/api/web/auth")
@Validated
public class WebAuthController {

    private static final Logger logger = LoggerFactory.getLogger(WebAuthController.class);

    @Autowired
    private WebAuthService webAuthService;

    /**
     * 用户注册
     *
     * @param reqDTO 注册请求
     * @return 注册响应
     */
    @ApiOperation(value = "用户注册", notes = "通过邮箱和密码进行用户注册，需要邮箱验证")
    @PostMapping("/register")
    @NoLogin
    public JsonResult<WebRegisterResDTO> register(@Valid @RequestBody WebRegisterReqDTO reqDTO) {
        logger.info("收到用户注册请求，邮箱: {}, 用户名: {}", reqDTO.getEmail(), reqDTO.getUsername());

        try {
            WebRegisterResDTO resDTO = webAuthService.register(reqDTO);
            logger.info("用户注册成功，userId: {}", resDTO.getUserId());
            return JsonResult.success(resDTO);
        } catch (Exception e) {
            logger.error("用户注册失败", e);
            return JsonResult.error(e.getMessage());
        }
    }

    /**
     * 用户登录
     *
     * @param reqDTO 登录请求
     * @return 登录响应
     */
    @ApiOperation(value = "用户登录", notes = "通过邮箱和密码进行登录，返回用户信息和访问令牌")
    @PostMapping("/login")
    @NoLogin
    public JsonResult<WebLoginResDTO> login(@Valid @RequestBody WebLoginReqDTO reqDTO) {
        logger.info("收到用户登录请求，邮箱: {}", reqDTO.getEmail());

        try {
            WebLoginResDTO resDTO = webAuthService.login(reqDTO);
            logger.info("用户登录成功，userId: {}", resDTO.getUserInfo().getId());
            return JsonResult.success(resDTO);
        } catch (Exception e) {
            logger.error("用户登录失败", e);
            return JsonResult.error(e.getMessage());
        }
    }

    /**
     * 刷新访问令牌
     *
     * @param reqDTO 刷新令牌请求
     * @return 新的访问令牌
     */
    @ApiOperation(value = "刷新令牌", notes = "使用刷新令牌获取新的访问令牌")
    @PostMapping("/refresh")
    @NoLogin
    public JsonResult<WebRefreshTokenResDTO> refreshToken(@Valid @RequestBody WebRefreshTokenReqDTO reqDTO) {
        logger.info("收到刷新令牌请求");

        try {
            WebRefreshTokenResDTO resDTO = webAuthService.refreshToken(reqDTO);
            logger.info("令牌刷新成功");
            return JsonResult.success(resDTO);
        } catch (Exception e) {
            logger.error("令牌刷新失败", e);
            return JsonResult.error(e.getMessage());
        }
    }

    /**
     * 用户登出
     *
     * @return 登出响应
     */
    @ApiOperation(value = "用户登出", notes = "用户登出，清除会话信息")
    @PostMapping("/logout")
    public JsonResult<Void> logout() {
        logger.info("收到用户登出请求");

        try {
            webAuthService.logout();
            logger.info("用户登出成功");
            return JsonResult.success();
        } catch (Exception e) {
            logger.error("用户登出失败", e);
            return JsonResult.error(e.getMessage());
        }
    }

    /**
     * 发送邮箱验证码
     *
     * @param reqDTO 发送验证码请求
     * @return 发送结果
     */
    @ApiOperation(value = "发送邮箱验证码", notes = "发送邮箱验证码，用于注册或密码重置")
    @PostMapping("/send-verification-code")
    @NoLogin
    public JsonResult<Void> sendVerificationCode(@Valid @RequestBody WebSendVerificationCodeReqDTO reqDTO) {
        logger.info("收到发送验证码请求，邮箱: {}, 类型: {}", reqDTO.getEmail(), reqDTO.getType());

        try {
            webAuthService.sendVerificationCode(reqDTO);
            logger.info("验证码发送成功");
            return JsonResult.success();
        } catch (Exception e) {
            logger.error("验证码发送失败", e);
            return JsonResult.error(e.getMessage());
        }
    }

    /**
     * 重置密码
     *
     * @param reqDTO 重置密码请求
     * @return 重置结果
     */
    @ApiOperation(value = "重置密码", notes = "通过邮箱验证码重置密码")
    @PostMapping("/reset-password")
    @NoLogin
    public JsonResult<Void> resetPassword(@Valid @RequestBody WebResetPasswordReqDTO reqDTO) {
        logger.info("收到重置密码请求，邮箱: {}", reqDTO.getEmail());

        try {
            webAuthService.resetPassword(reqDTO);
            logger.info("密码重置成功");
            return JsonResult.success();
        } catch (Exception e) {
            logger.error("密码重置失败", e);
            return JsonResult.error(e.getMessage());
        }
    }
}