package com.jj.web.wechat;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jj.common.response.JsonResult;
import com.jj.core.annotation.NoLogin;
import com.jj.core.annotation.WeChatAuth;
import com.jj.core.auth.UserContext;
import com.jj.core.domain.dto.square.ImageDetailDTO;
import com.jj.core.domain.dto.square.SquareImageDTO;
import com.jj.core.service.square.SquareService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;

/**
 * 广场控制器
 * 
 * <AUTHOR>
 * @date 2024-07-29
 */
@Api(tags = "微信小程序端-图片广场")
@RestController
@RequestMapping("/api/wechat/square")
@Validated
public class SquareController {
    
    private static final Logger logger = LoggerFactory.getLogger(SquareController.class);
    
    @Autowired
    private SquareService squareService;
    
    /**
     * 分页查询广场图片
     * 
     * @param page 页码
     * @param size 每页大小
     * @param processType 处理类型
     * @param aspectRatio 图片比例
     * @return 图片分页数据
     */
    @ApiOperation(value = "分页查询广场图片", notes = "获取公开的图片列表，支持按处理类型和比例筛选")
    @GetMapping("/images")
    @NoLogin
    public JsonResult<IPage<SquareImageDTO>> getSquareImages(
            @ApiParam(value = "页码", example = "1") @RequestParam(defaultValue = "1") @Min(1) Integer page,
            @ApiParam(value = "每页大小", example = "10") @RequestParam(defaultValue = "10") @Min(1) @Max(100) Integer size,
            @ApiParam(value = "处理类型") @RequestParam(required = false) String processType,
            @ApiParam(value = "图片比例") @RequestParam(required = false) String aspectRatio) {
        
        Long currentUserId = UserContext.getCurrentWeChatUserId();
        logger.info("分页查询广场图片请求，page: {}, size: {}, processType: {}, aspectRatio: {}, currentUserId: {}", 
                page, size, processType, aspectRatio, currentUserId);
        
        try {
            IPage<SquareImageDTO> imagePage = squareService.getSquareImages(page, size, processType, aspectRatio, currentUserId);
            logger.info("分页查询广场图片成功，total: {}", imagePage.getTotal());
            return JsonResult.success(imagePage);
            
        } catch (Exception e) {
            logger.error("分页查询广场图片失败", e);
            return JsonResult.error(e.getMessage());
        }
    }

    
    /**
     * 点赞或取消点赞图片
     * 
     * @param imageId 图片ID
     * @param reqDTO 点赞请求
     * @return 点赞结果
     */
    @ApiOperation(value = "点赞或取消点赞图片", notes = "对指定图片进行点赞或取消点赞操作")
    @PostMapping("/images/{imageId}/like")
    @WeChatAuth
    public JsonResult<SquareService.LikeResultDTO> likeImage(
            @ApiParam(value = "图片ID", required = true) @PathVariable @Min(1) Long imageId,
            @Valid @RequestBody LikeImageReqDTO reqDTO) {
        
        Long currentUserId = UserContext.getCurrentWeChatUserId();
        logger.info("点赞操作请求，imageId: {}, isLike: {}, currentUserId: {}", imageId, reqDTO.getIsLike(), currentUserId);
        
        try {
            SquareService.LikeResultDTO result = squareService.likeImage(imageId, currentUserId, reqDTO.getIsLike());
            
            String message = reqDTO.getIsLike() ? "点赞成功" : "取消点赞成功";
            logger.info("点赞操作成功，imageId: {}, isLike: {}, likeCount: {}", imageId, reqDTO.getIsLike(), result.getLikeCount());
            return JsonResult.success(message, result);
            
        } catch (Exception e) {
            logger.error("点赞操作失败，imageId: {}, isLike: {}", imageId, reqDTO.getIsLike(), e);
            return JsonResult.error(e.getMessage());
        }
    }
    
    /**
     * 点赞请求DTO
     */
    public static class LikeImageReqDTO {
        
        @ApiParam(value = "是否点赞", required = true, example = "true")
        private Boolean isLike;
        
        public Boolean getIsLike() {
            return isLike;
        }
        
        public void setIsLike(Boolean isLike) {
            this.isLike = isLike;
        }
        
        @Override
        public String toString() {
            return "LikeImageReqDTO{" +
                    "isLike=" + isLike +
                    '}';
        }
    }

    /**
     * 获取图片详情
     *
     * @param imageId 图片ID
     * @return 图片详情信息
     */
    @ApiOperation(value = "获取图片详情", notes = "获取图片的详细信息，包括参考图、结果图、提示词等")
    @GetMapping("/images/{imageId}")
    @NoLogin
    public JsonResult<ImageDetailDTO> getImageDetail(
            @ApiParam(value = "图片ID", required = true) @PathVariable Long imageId) {

        Long currentUserId = UserContext.getCurrentWeChatUserId();
        logger.info("获取图片详情请求，imageId: {}, currentUserId: {}", imageId, currentUserId);

        try {
            ImageDetailDTO result = squareService.getImageDetail(imageId, currentUserId);
            logger.info("获取图片详情成功，imageId: {}", imageId);
            return JsonResult.success(result);

        } catch (Exception e) {
            logger.error("获取图片详情失败，imageId: {}", imageId, e);
            return JsonResult.error(e.getMessage());
        }
    }

    /**
     * 图片预览（增加浏览量）
     *
     * @param imageId 图片ID
     * @return 操作结果
     */
    @ApiOperation(value = "图片预览", notes = "用户预览图片时增加浏览量")
    @PostMapping("/images/{imageId}/view")
    @NoLogin
    public JsonResult<Void> viewImage(
            @ApiParam(value = "图片ID", required = true) @PathVariable Long imageId) {

        logger.info("图片预览请求，imageId: {}", imageId);

        try {
            squareService.incrementViewCount(imageId);
            logger.info("图片预览成功，imageId: {}", imageId);
            return JsonResult.success();

        } catch (Exception e) {
            logger.error("图片预览失败，imageId: {}", imageId, e);
            return JsonResult.error(e.getMessage());
        }
    }
}
