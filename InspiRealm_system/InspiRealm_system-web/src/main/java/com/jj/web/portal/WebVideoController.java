package com.jj.web.portal;

import com.jj.common.response.JsonResult;
import com.jj.core.annotation.WebAuth;
import com.jj.core.domain.dto.portal.web.*;
import com.jj.core.service.video.WebVideoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.util.List;

/**
 * Web端视频生成控制器
 *
 * <AUTHOR>
 * @date 2025-01-05
 */
@Api(tags = "Web端-视频生成管理")
@RestController
@RequestMapping("/api/web/video")
@Validated
public class WebVideoController {

    private static final Logger logger = LoggerFactory.getLogger(WebVideoController.class);

    @Autowired
    private WebVideoService webVideoService;

    /**
     * 创建视频生成任务
     *
     * @param reqDTO 视频生成请求
     * @return 任务创建响应
     */
    @ApiOperation(value = "创建视频生成任务", notes = "基于Veo3模型创建视频生成任务")
    @PostMapping("/generate")
    @WebAuth("创建视频生成任务")
    public JsonResult<WebVideoGenerateResDTO> generateVideo(@Valid @RequestBody WebVideoGenerateReqDTO reqDTO) {
        logger.info("收到视频生成请求，提示词: {}", reqDTO.getPrompt());

        try {
            WebVideoGenerateResDTO resDTO = webVideoService.generateVideo(reqDTO);
            logger.info("视频生成任务创建成功，任务ID: {}", resDTO.getTaskId());
            return JsonResult.success(resDTO);
        } catch (Exception e) {
            logger.error("视频生成任务创建失败", e);
            return JsonResult.error(e.getMessage());
        }
    }

    /**
     * 查询视频生成任务状态
     *
     * @param taskId 任务ID
     * @return 任务状态
     */
    @ApiOperation(value = "查询任务状态", notes = "查询视频生成任务的当前状态和进度")
    @GetMapping("/query/{taskId}")
    @WebAuth("查询任务状态")
    public JsonResult<WebVideoTaskStatusResDTO> queryTaskStatus(@PathVariable String taskId) {
        logger.info("收到任务状态查询请求，任务ID: {}", taskId);

        try {
            WebVideoTaskStatusResDTO resDTO = webVideoService.queryTaskStatus(taskId);
            return JsonResult.success(resDTO);
        } catch (Exception e) {
            logger.error("任务状态查询失败", e);
            return JsonResult.error(e.getMessage());
        }
    }

    /**
     * 获取用户视频任务列表
     *
     * @param reqDTO 查询请求
     * @return 任务列表
     */
    @ApiOperation(value = "获取任务列表", notes = "获取当前用户的视频生成任务列表")
    @PostMapping("/list")
    @WebAuth("获取任务列表")
    public JsonResult<WebVideoTaskListResDTO> getTaskList(@Valid @RequestBody WebVideoTaskListReqDTO reqDTO) {
        logger.info("收到任务列表查询请求，页码: {}, 页大小: {}", reqDTO.getPage(), reqDTO.getPageSize());

        try {
            WebVideoTaskListResDTO resDTO = webVideoService.getTaskList(reqDTO);
            return JsonResult.success(resDTO);
        } catch (Exception e) {
            logger.error("任务列表查询失败", e);
            return JsonResult.error(e.getMessage());
        }
    }

    /**
     * 删除视频任务
     *
     * @param taskId 任务ID
     * @return 删除结果
     */
    @ApiOperation(value = "删除任务", notes = "删除指定的视频生成任务")
    @DeleteMapping("/{taskId}")
    @WebAuth("删除任务")
    public JsonResult<Void> deleteTask(@PathVariable String taskId) {
        logger.info("收到删除任务请求，任务ID: {}", taskId);

        try {
            webVideoService.deleteTask(taskId);
            logger.info("任务删除成功");
            return JsonResult.success();
        } catch (Exception e) {
            logger.error("任务删除失败", e);
            return JsonResult.error(e.getMessage());
        }
    }

    /**
     * 上传图片用于视频生成
     *
     * @param files 图片文件
     * @return 上传结果
     */
    @ApiOperation(value = "上传图片", notes = "上传图片文件用于视频生成")
    @PostMapping("/upload-images")
    @WebAuth("上传图片")
    public JsonResult<WebImageUploadResDTO> uploadImages(@RequestParam("files") List<MultipartFile> files) {
        logger.info("收到图片上传请求，文件数量: {}", files.size());

        try {
            WebImageUploadResDTO resDTO = webVideoService.uploadImages(files);
            logger.info("图片上传成功");
            return JsonResult.success(resDTO);
        } catch (Exception e) {
            logger.error("图片上传失败", e);
            return JsonResult.error(e.getMessage());
        }
    }


}