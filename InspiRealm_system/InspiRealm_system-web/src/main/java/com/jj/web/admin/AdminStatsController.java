package com.jj.web.admin;

import com.jj.common.response.JsonResult;
import com.jj.core.annotation.AdminAuth;
import com.jj.core.domain.dto.admin.SystemStatsDTO;
import com.jj.core.service.admin.AdminStatsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 管理员系统统计控制器
 * 
 * <AUTHOR>
 * @date 2024-07-29
 */
@Api(tags = "管理员端-系统统计")
@RestController
@RequestMapping("/api/admin/stats")
@Validated
@AdminAuth
public class AdminStatsController {
    
    private static final Logger logger = LoggerFactory.getLogger(AdminStatsController.class);
    
    @Autowired
    private AdminStatsService adminStatsService;
    
    /**
     * 获取系统统计信息
     * 
     * @return 系统统计信息
     */
    @ApiOperation(value = "获取系统统计信息", notes = "获取用户、图片、积分、系统运行状态等统计数据")
    @GetMapping("/system")
    public JsonResult<SystemStatsDTO> getSystemStats() {
        logger.info("获取系统统计信息请求");
        
        try {
            SystemStatsDTO stats = adminStatsService.getSystemStats();
            logger.info("获取系统统计信息成功");
            return JsonResult.success(stats);
            
        } catch (Exception e) {
            logger.error("获取系统统计信息失败", e);
            return JsonResult.error(e.getMessage());
        }
    }
}
