package com.jj.web.wechat;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jj.common.response.JsonResult;
import com.jj.core.annotation.WeChatAuth;
import com.jj.core.auth.UserContext;
import com.jj.core.domain.dto.user.UpdateUserInfoReqDTO;
import com.jj.core.domain.dto.user.UserProfileDTO;
import com.jj.core.domain.dto.square.SquareImageDTO;
import com.jj.core.service.user.UserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.Min;
import javax.validation.constraints.Max;

/**
 * 用户管理控制器
 * 
 * <AUTHOR>
 * @date 2024-07-29
 */
@Api(tags = "微信小程序端-用户管理")
@RestController
@RequestMapping("/api/wechat/user")
@Validated
@WeChatAuth
public class UserController {
    
    private static final Logger logger = LoggerFactory.getLogger(UserController.class);
    
    @Autowired
    private UserService userService;
    
    /**
     * 获取用户资料
     * 
     * @return 用户资料
     */
    @ApiOperation(value = "获取用户资料", notes = "获取当前登录用户的详细资料信息")
    @GetMapping("/profile")
    public JsonResult<UserProfileDTO> getUserProfile() {
        Long userId = UserContext.getCurrentWeChatUserId();
        logger.info("获取用户资料请求，userId: {}", userId);
        
        try {
            UserProfileDTO profileDTO = userService.getUserProfile(userId);
            logger.info("获取用户资料成功，userId: {}", userId);
            return JsonResult.success(profileDTO);
            
        } catch (Exception e) {
            logger.error("获取用户资料失败，userId: {}", userId, e);
            return JsonResult.error(e.getMessage());
        }
    }
    
    /**
     * 更新用户信息
     * 
     * @param reqDTO 更新请求
     * @return 更新后的用户资料
     */
    @ApiOperation(value = "更新用户信息", notes = "更新当前登录用户的基本信息")
    @PutMapping("/info")
    public JsonResult<UserProfileDTO> updateUserInfo(@Valid @RequestBody UpdateUserInfoReqDTO reqDTO) {
        Long userId = UserContext.getCurrentWeChatUserId();
        logger.info("更新用户信息请求，userId: {}, reqDTO: {}", userId, reqDTO);
        
        try {
            UserProfileDTO profileDTO = userService.updateUserInfo(userId, reqDTO);
            logger.info("更新用户信息成功，userId: {}", userId);
            return JsonResult.success("更新成功", profileDTO);
            
        } catch (Exception e) {
            logger.error("更新用户信息失败，userId: {}", userId, e);
            return JsonResult.error(e.getMessage());
        }
    }
    
    /**
     * 获取用户基本信息
     * 
     * @return 用户基本信息
     */
    @ApiOperation(value = "获取用户基本信息", notes = "获取当前登录用户的基本信息（简化版）")
    @GetMapping("/info")
    public JsonResult<UserBasicInfoDTO> getUserBasicInfo() {
        Long userId = UserContext.getCurrentWeChatUserId();
        String openId = UserContext.getCurrentWeChatOpenId();
        logger.info("获取用户基本信息请求，userId: {}", userId);
        
        try {
            UserProfileDTO profileDTO = userService.getUserProfile(userId);
            
            // 构建基本信息DTO
            UserBasicInfoDTO basicInfo = new UserBasicInfoDTO();
            basicInfo.setId(profileDTO.getId());
            basicInfo.setNickname(profileDTO.getNickname());
            basicInfo.setAvatarUrl(profileDTO.getAvatarUrl());
            basicInfo.setGender(profileDTO.getGender());
            if (profileDTO.getPointsInfo() != null) {
                basicInfo.setAvailablePoints(profileDTO.getPointsInfo().getAvailablePoints());
            }
            
            logger.info("获取用户基本信息成功，userId: {}", userId);
            return JsonResult.success(basicInfo);
            
        } catch (Exception e) {
            logger.error("获取用户基本信息失败，userId: {}", userId, e);
            return JsonResult.error(e.getMessage());
        }
    }
    
    /**
     * 用户基本信息DTO
     */
    public static class UserBasicInfoDTO {
        
        private Long id;
        private String nickname;
        private String avatarUrl;
        private Integer gender;
        private Integer availablePoints;
        
        public Long getId() {
            return id;
        }
        
        public void setId(Long id) {
            this.id = id;
        }
        
        public String getNickname() {
            return nickname;
        }
        
        public void setNickname(String nickname) {
            this.nickname = nickname;
        }
        
        public String getAvatarUrl() {
            return avatarUrl;
        }
        
        public void setAvatarUrl(String avatarUrl) {
            this.avatarUrl = avatarUrl;
        }
        
        public Integer getGender() {
            return gender;
        }
        
        public void setGender(Integer gender) {
            this.gender = gender;
        }
        
        public Integer getAvailablePoints() {
            return availablePoints;
        }
        
        public void setAvailablePoints(Integer availablePoints) {
            this.availablePoints = availablePoints;
        }
        
        @Override
        public String toString() {
            return "UserBasicInfoDTO{" +
                    "id=" + id +
                    ", nickname='" + nickname + '\'' +
                    ", avatarUrl='" + avatarUrl + '\'' +
                    ", gender=" + gender +
                    ", availablePoints=" + availablePoints +
                    '}';
        }
    }

    /**
     * 获取用户点赞记录
     *
     * @param page 页码
     * @param size 每页大小
     * @return 用户点赞的图片列表
     */
    @ApiOperation(value = "获取用户点赞记录", notes = "获取当前登录用户点赞过的图片列表")
    @GetMapping("/likes")
    public JsonResult<IPage<SquareImageDTO>> getUserLikes(
            @ApiParam(value = "页码", example = "1") @RequestParam(defaultValue = "1") @Min(1) Integer page,
            @ApiParam(value = "每页大小", example = "10") @RequestParam(defaultValue = "10") @Min(1) @Max(100) Integer size) {

        Long userId = UserContext.getCurrentWeChatUserId();
        logger.info("获取用户点赞记录请求，userId: {}, page: {}, size: {}", userId, page, size);

        try {
            IPage<SquareImageDTO> result = userService.getUserLikes(userId, page, size);
            logger.info("获取用户点赞记录成功，userId: {}, total: {}", userId, result.getTotal());
            return JsonResult.success(result);

        } catch (Exception e) {
            logger.error("获取用户点赞记录失败，userId: {}", userId, e);
            return JsonResult.error(e.getMessage());
        }
    }
}
