<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI创作页面设计预览</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8f9fa;
            padding: 20px 16px 60px;
            min-height: 100vh;
        }
        
        .container {
            max-width: 375px;
            margin: 0 auto;
        }
        
        /* 简洁标题 */
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        
        .title {
            font-size: 24px;
            font-weight: 600;
            color: #1a1a1a;
        }
        
        /* 简洁模式切换 */
        .mode-switch {
            display: flex;
            background: white;
            border-radius: 6px;
            margin-bottom: 20px;
            box-shadow: 0 1px 6px rgba(0, 0, 0, 0.08);
        }
        
        .mode-btn {
            flex: 1;
            padding: 12px;
            text-align: center;
            font-size: 14px;
            font-weight: 500;
            color: #666;
            border-radius: 6px;
            transition: all 0.2s ease;
            cursor: pointer;
        }
        
        .mode-btn.active {
            background: #007aff;
            color: white;
        }
        
        /* 图片选择 */
        .image-section {
            margin-bottom: 16px;
        }
        
        .image-box {
            width: 100%;
            height: 150px;
            background: white;
            border-radius: 6px;
            border: 1px dashed #ddd;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
            cursor: pointer;
        }
        
        .image-box:hover {
            border-color: #007aff;
            background: #f8f9fa;
        }
        
        .placeholder-text {
            font-size: 14px;
            color: #999;
        }
        
        /* 选项区域 */
        .options {
            margin-bottom: 16px;
        }
        
        .option {
            background: white;
            border-radius: 6px;
            margin-bottom: 8px;
            box-shadow: 0 1px 4px rgba(0, 0, 0, 0.06);
            position: relative;
            z-index: 1;
        }
        
        .option-selector {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px;
            cursor: pointer;
            transition: background 0.2s ease;
        }
        
        .option-selector:hover {
            background: #f8f9fa;
        }
        
        .option-content {
            flex: 1;
        }
        
        .option-label {
            font-size: 12px;
            font-weight: 500;
            color: #999;
            margin-bottom: 2px;
            display: block;
        }
        
        .option-text {
            font-size: 14px;
            color: #333;
            font-weight: 500;
        }
        
        .option-arrow {
            font-size: 10px;
            color: #999;
        }
        
        /* 输入区域 */
        .input-group {
            background: white;
            border-radius: 6px;
            padding: 12px;
            margin-bottom: 8px;
            box-shadow: 0 1px 4px rgba(0, 0, 0, 0.06);
        }
        
        .main-input {
            margin-bottom: 16px;
        }
        
        .input-label {
            display: block;
            font-size: 14px;
            font-weight: 500;
            color: #333;
            margin-bottom: 8px;
        }
        
        .input {
            width: 100%;
            height: 40px;
            padding: 0 10px;
            border: 1px solid #e5e5e5;
            border-radius: 4px;
            font-size: 14px;
            color: #333;
            background: #fafafa;
        }
        
        .input:focus {
            border-color: #007aff;
            background: white;
            outline: none;
        }
        
        .textarea {
            width: 100%;
            min-height: 80px;
            padding: 10px;
            border: 1px solid #e5e5e5;
            border-radius: 4px;
            font-size: 14px;
            color: #333;
            background: #fafafa;
            line-height: 1.6;
            resize: vertical;
        }
        
        .textarea:focus {
            border-color: #007aff;
            background: white;
            outline: none;
        }
        
        /* 底部区域 */
        .bottom-section {
            background: white;
            border-radius: 6px;
            padding: 12px;
            box-shadow: 0 1px 4px rgba(0, 0, 0, 0.06);
        }
        
        .points {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
            padding-bottom: 10px;
            border-bottom: 1px solid #f5f5f5;
        }
        
        .points-current {
            font-size: 13px;
            color: #666;
        }
        
        .points-cost {
            font-size: 13px;
            color: #ff6b6b;
            font-weight: 500;
        }
        
        .submit-btn {
            width: 100%;
            height: 44px;
            background: #007aff;
            color: white;
            font-size: 15px;
            font-weight: 500;
            border-radius: 4px;
            border: none;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .submit-btn:hover {
            background: #0056cc;
        }
        
        .submit-btn:active {
            transform: scale(0.98);
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 简洁标题 -->
        <div class="header">
            <div class="title">AI创作</div>
        </div>

        <!-- 简洁模式切换 -->
        <div class="mode-switch">
            <div class="mode-btn active">图片处理</div>
            <div class="mode-btn">文字生成</div>
        </div>

        <!-- 图片选择 -->
        <div class="image-section">
            <div class="image-box">
                <div class="placeholder-text">选择图片</div>
            </div>
        </div>

        <!-- 简洁选项 -->
        <div class="options">
            <!-- 处理类型 -->
            <div class="option">
                <div class="option-selector">
                    <div class="option-content">
                        <div class="option-label">类型</div>
                        <div class="option-text">图片美化</div>
                    </div>
                    <div class="option-arrow">▼</div>
                </div>
            </div>

            <!-- 尺寸比例 -->
            <div class="option">
                <div class="option-selector">
                    <div class="option-content">
                        <div class="option-label">比例</div>
                        <div class="option-text">原始</div>
                    </div>
                    <div class="option-arrow">▼</div>
                </div>
            </div>
        </div>

        <!-- 文字输入 -->
        <div class="input-group">
            <div class="input-label">添加文字</div>
            <input class="input" placeholder="要添加到图片上的文字（可选）" />
        </div>

        <!-- 主要描述输入 -->
        <div class="input-group main-input">
            <div class="input-label">处理要求 *</div>
            <textarea class="textarea" placeholder="描述如何处理这张图片"></textarea>
        </div>

        <!-- 积分和提交 -->
        <div class="bottom-section">
            <div class="points">
                <div class="points-current">积分: 100</div>
                <div class="points-cost">消耗: 5</div>
            </div>
            
            <button class="submit-btn">开始处理</button>
        </div>
    </div>
</body>
</html>
