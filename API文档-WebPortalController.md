# JDC视频生成门户网站 - Web端API接口文档

## 📋 概述

本文档描述了JDC视频生成门户网站Web端的所有API接口，基于Veo3模型提供专业级AI视频生成服务。

**基础信息：**
- 基础URL：`http://localhost:8080`
- 认证方式：Bearer <PERSON> (JWT)
- 响应格式：JSON
- 字符编码：UTF-8

## 🔐 认证管理 (WebAuthController)

### 1. 用户注册
**接口地址：** `POST /api/web/auth/register`
**接口描述：** 通过邮箱和密码进行用户注册，需要邮箱验证
**是否需要登录：** 否

**请求参数：**
```json
{
  "username": "testuser",
  "email": "<EMAIL>",
  "password": "Password123",
  "confirmPassword": "Password123",
  "verificationCode": "123456"
}
```

**响应示例：**
```json
{
  "code": 200,
  "message": "注册成功",
  "data": {
    "userId": 1001,
    "username": "testuser",
    "email": "<EMAIL>"
  },
  "timestamp": 1704441600000
}
```

### 2. 用户登录
**接口地址：** `POST /api/web/auth/login`
**接口描述：** 通过邮箱和密码进行登录，返回用户信息和访问令牌
**是否需要登录：** 否

**请求参数：**
```json
{
  "email": "<EMAIL>",
  "password": "Password123",
  "rememberMe": true
}
```

**响应示例：**
```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "userInfo": {
      "id": 1001,
      "username": "testuser",
      "email": "<EMAIL>",
      "nickname": "测试用户",
      "avatarUrl": "https://example.com/avatar.jpg",
      "userType": 1,
      "emailVerified": true
    },
    "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expiresIn": 7200
  },
  "timestamp": 1704441600000
}
```

### 3. 刷新令牌
**接口地址：** `POST /api/web/auth/refresh`
**接口描述：** 使用刷新令牌获取新的访问令牌
**是否需要登录：** 否

**请求参数：**
```json
{
  "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

**响应示例：**
```json
{
  "code": 200,
  "message": "令牌刷新成功",
  "data": {
    "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expiresIn": 7200
  },
  "timestamp": 1704441600000
}
```