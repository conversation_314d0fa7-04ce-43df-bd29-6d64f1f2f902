# ===========================================
# 劈克雀儿项目 - 完整 .gitignore 文件
# ===========================================

# ===========================================
# 通用忽略文件
# ===========================================

# 操作系统生成的文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
desktop.ini

# IDE 和编辑器文件
.vscode/
.idea/
*.swp
*.swo
*~
.project
.classpath
.settings/
*.iml
*.ipr
*.iws

# 临时文件
*.tmp
*.temp
*.log
*.cache
*.pid
*.seed
*.pid.lock

# ===========================================
# Java Spring Boot 项目 (InspiRealm_system)
# ===========================================

# Maven
target/
pom.xml.tag
pom.xml.releaseBackup
pom.xml.versionsBackup
pom.xml.next
release.properties
dependency-reduced-pom.xml
buildNumber.properties
.mvn/timing.properties
.mvn/wrapper/maven-wrapper.jar

# Gradle
.gradle/
build/
!gradle/wrapper/gradle-wrapper.jar
!**/src/main/**/build/
!**/src/test/**/build/

# Spring Boot
*.jar
*.war
*.nar
*.ear
*.zip
*.tar.gz
*.rar

# 日志文件
logs/
*.log
log/
spring.log

# 配置文件中的敏感信息
application-local.properties
application-secret.properties
*-secret.yml
*-secret.yaml

# 数据库文件
*.db
*.sqlite
*.sqlite3

# ===========================================
# React 管理后台项目 (admin-react)
# ===========================================

# 依赖目录
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# 构建输出
dist/
dist-ssr/
build/
*.local

# 环境变量文件
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 包管理器锁文件（根据团队选择保留一个）
# package-lock.json
# yarn.lock
# pnpm-lock.yaml

# 测试覆盖率
coverage/
*.lcov

# ESLint 缓存
.eslintcache

# Parcel 缓存
.cache/
.parcel-cache/

# Next.js
.next/
out/

# Nuxt.js
.nuxt/

# Gatsby
.cache/
public/

# Storybook
.out/
.storybook-out/

# ===========================================
# 微信小程序项目 (InspiRealm_Wechat)
# ===========================================

# 微信开发者工具生成的文件
.wechat_devtools/
project.config.json.bak
project.private.config.json

# 小程序构建产物
miniprogram_npm/
node_modules/

# 小程序日志
*.wxvpkg

# 小程序缓存
.cache/

# TypeScript 编译产物
*.js.map
*.d.ts.map

# ===========================================
# 开发和部署相关
# ===========================================

# Docker
.dockerignore
Dockerfile.dev
docker-compose.override.yml

# 部署脚本
deploy.sh
deploy/

# 备份文件
*.bak
*.backup
*.old

# 压缩文件
*.zip
*.tar
*.tar.gz
*.rar
*.7z

# ===========================================
# 安全和隐私文件
# ===========================================

# 密钥和证书
*.key
*.pem
*.p12
*.crt
*.cer
*.pfx
*.keystore

# 配置文件
config/local/
config/secret/
secrets/
.secrets/

# API 密钥文件
.env.keys
api-keys.json
credentials.json

# ===========================================
# 文档和资源文件
# ===========================================

# 大型媒体文件
*.mp4
*.avi
*.mov
*.wmv
*.flv
*.webm
*.mkv

# 大型图片文件（保留小图标）
# *.png
# *.jpg
# *.jpeg
# *.gif
# *.bmp
# *.tiff
# *.svg

# 文档草稿
*.draft
*.draft.*
*-draft.*

# ===========================================
# 测试和调试文件
# ===========================================

# 测试输出
test-results/
coverage/
.nyc_output/

# 调试文件
debug.log
error.log
access.log

# 性能分析文件
*.prof
*.heap
*.cpuprofile

# ===========================================
# 版本控制相关
# ===========================================

# Git 相关
.git/
.gitattributes.local

# SVN 相关
.svn/

# Mercurial 相关
.hg/

# ===========================================
# 其他工具生成的文件
# ===========================================

# Webpack
.webpack/

# Rollup
.rollup.cache/

# Vite
.vite/

# TypeScript
*.tsbuildinfo

# Babel
.babel_cache/

# PostCSS
.postcssrc.js.cache

# Sass
.sass-cache/

# Less
.less-cache/

# Stylus
.stylus-cache/

# ===========================================
# 项目特定忽略文件
# ===========================================

# 上传的用户文件
uploads/
upload/
static/uploads/
public/uploads/

# 生成的图片缓存
image-cache/
thumbnail-cache/
processed-images/

# 数据库备份
*.sql
*.dump
backup/
db-backup/

# 配置文件模板的实际配置
config.local.js
config.local.json
settings.local.json

# 第三方服务配置
aliyun-config.json
qiniu-config.json
wechat-config.json

# 开发时的测试数据
test-data/
mock-data/
sample-data/

# 性能监控和分析
performance/
analytics/
monitoring/

# 临时开发文件
TODO.md
NOTES.md
scratch/
temp/
tmp/

# 编辑器临时文件
.vscode/settings.json
.vscode/launch.json
.vscode/tasks.json

# 系统服务文件
*.service
*.socket

# 证书和密钥文件
ssl/
certs/
keys/

# 本地开发环境变量
.env.development
.env.staging
.env.production

# 构建和部署脚本
build.sh
start.sh
stop.sh
restart.sh

# 监控和日志
logs/
log/
*.access.log
*.error.log
*.out
*.err

# 缓存目录
.cache/
cache/
.tmp/

# 文档生成
docs/build/
documentation/build/

# 国际化文件
i18n/build/
locales/build/
