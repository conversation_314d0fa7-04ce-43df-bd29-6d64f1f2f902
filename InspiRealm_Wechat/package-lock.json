{"name": "pikequeer-miniprogram", "version": "1.0.0", "lockfileVersion": 3, "requires": true, "packages": {"": {"name": "pikequeer-miniprogram", "version": "1.0.0", "devDependencies": {"miniprogram-api-typings": "^2.8.3-1", "typescript": "^5.9.2"}}, "node_modules/miniprogram-api-typings": {"version": "2.12.0", "resolved": "https://registry.npmmirror.com/miniprogram-api-typings/-/miniprogram-api-typings-2.12.0.tgz", "integrity": "sha512-ibvbqeslVFur0IAvTxLMvsbtvVcMo6gwvOnj0YZHV7aeDLu091VQRrETT2QuiG9P6aZWRcxeNGJChRKVPCp9VQ==", "dev": true, "license": "MIT"}, "node_modules/typescript": {"version": "5.9.2", "resolved": "https://registry.npmmirror.com/typescript/-/typescript-5.9.2.tgz", "integrity": "sha512-CWBzXQrc/qOkhidw1OzBTQuYRbfyxDXJMVJ1XNwUHGROVmuaeiEm3OslpZ1RV96d7SKKjZKrSJu3+t/xlw3R9A==", "dev": true, "license": "Apache-2.0", "bin": {"tsc": "bin/tsc", "tsserver": "bin/tsserver"}, "engines": {"node": ">=14.17"}}}}