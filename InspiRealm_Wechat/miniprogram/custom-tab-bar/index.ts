// custom-tab-bar/index.ts
Component({
  data: {
    selected: 0,
    color: "#8E8E93",
    selectedColor: "#007AFF",
    list: [
      {
        pagePath: "/pages/home/<USER>",
        text: "首页",
        iconPath: "🏠",
        selectedIconPath: "🏠"
      },
      {
        pagePath: "/pages/process/process",
        text: "创作",
        iconPath: "✨",
        selectedIconPath: "✨"
      },
      {
        pagePath: "/pages/profile/profile",
        text: "我的",
        iconPath: "👤",
        selectedIconPath: "👤"
      }
    ]
  },
  
  attached() {
    // 获取当前页面路径
    try {
      const pages = getCurrentPages()
      if (pages && pages.length > 0) {
        const currentPage = pages[pages.length - 1]
        const route = currentPage.route || currentPage.__route__

        if (route) {
          // 设置当前选中的tab
          this.data.list.forEach((item, index) => {
            if (item.pagePath.includes(route)) {
              this.setData({
                selected: index
              })
            }
          })
        }
      }
    } catch (error) {
      console.error('获取当前页面路径失败:', error)
      // 默认选中第一个tab
      this.setData({
        selected: 0
      })
    }
  },

  methods: {
    switchTab(e: any) {
      const data = e.currentTarget.dataset
      const url = data.path
      
      wx.switchTab({
        url,
        success: () => {
          this.setData({
            selected: data.index
          })
        }
      })
    }
  }
})
