/* custom-tab-bar/index.wxss */
.tab-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100rpx;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-top: 1rpx solid #F2F2F7;
  display: flex;
  padding-bottom: env(safe-area-inset-bottom);
  z-index: 1000;
}

.tab-bar-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 8rpx 0;
  transition: all 0.2s ease;
}

.tab-bar-item:active {
  transform: scale(0.95);
}

.tab-bar-icon {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 4rpx;
}

.icon-emoji {
  font-size: 44rpx;
  opacity: 0.6;
  transition: all 0.2s ease;
}

.tab-bar-item-active .icon-emoji {
  opacity: 1;
  transform: scale(1.1);
}

.tab-bar-text {
  font-size: 20rpx;
  color: #8E8E93;
  font-weight: 500;
  transition: all 0.2s ease;
}

.tab-bar-item-active .tab-bar-text {
  color: #007AFF;
  font-weight: 600;
}
