<!--custom-tab-bar/index.wxml-->
<view class="tab-bar">
  <view
    wx:for="{{list}}"
    wx:key="index"
    class="tab-bar-item {{selected === index ? 'tab-bar-item-active' : ''}}"
    data-path="{{item.pagePath}}"
    data-index="{{index}}"
    bindtap="switchTab"
  >
    <view class="tab-bar-icon">
      <text class="icon-emoji">{{item.iconPath}}</text>
    </view>
    <view class="tab-bar-text">{{item.text}}</view>
  </view>
</view>
