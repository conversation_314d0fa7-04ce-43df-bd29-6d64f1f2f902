/**app.wxss**/
/* 全局样式重置 */
page {
  background-color: #f8fafc;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
}

/* 通用容器 */
.container {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  padding: 200rpx 0;
  box-sizing: border-box;
}

/* 通用按钮样式 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 24rpx 48rpx;
  border-radius: 12rpx;
  font-size: 32rpx;
  font-weight: 500;
  transition: all 0.2s ease;
  border: none;
  outline: none;
}

.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.btn-primary:active {
  transform: scale(0.98);
}

.btn-secondary {
  background: #f1f5f9;
  color: #475569;
}

.btn-secondary:active {
  background: #e2e8f0;
}

/* 通用文本样式 */
.text-primary {
  color: #6366f1;
}

.text-secondary {
  color: #64748b;
}

.text-success {
  color: #10b981;
}

.text-warning {
  color: #f59e0b;
}

.text-error {
  color: #ef4444;
}

/* 通用间距 */
.mb-small {
  margin-bottom: 16rpx;
}

.mb-medium {
  margin-bottom: 32rpx;
}

.mb-large {
  margin-bottom: 48rpx;
}

.mt-small {
  margin-top: 16rpx;
}

.mt-medium {
  margin-top: 32rpx;
}

.mt-large {
  margin-top: 48rpx;
}

/* 通用阴影 */
.shadow-soft {
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}

.shadow-medium {
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);
}

/* 通用圆角 */
.rounded-small {
  border-radius: 8rpx;
}

.rounded-medium {
  border-radius: 16rpx;
}

.rounded-large {
  border-radius: 24rpx;
}

/* 底部导航栏优化 */
.uni-tabbar {
  border-top: 1rpx solid #F2F2F7 !important;
  background-color: rgba(255, 255, 255, 0.95) !important;
  backdrop-filter: blur(20rpx) !important;
}

.uni-tabbar-item {
  padding-top: 8rpx !important;
}

.uni-tabbar__text {
  font-size: 20rpx !important;
  font-weight: 500 !important;
  margin-top: 4rpx !important;
}

.uni-tabbar__icon {
  width: 48rpx !important;
  height: 48rpx !important;
}
