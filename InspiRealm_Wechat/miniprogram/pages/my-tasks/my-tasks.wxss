/* pages/my-tasks/my-tasks.wxss */

/* Instagram风格设计 */
.page-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  background-attachment: fixed;
}

/* 顶部导航 - Instagram风格 */
.header {
  position: sticky;
  top: 0;
  z-index: 100;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-bottom: none;
  box-shadow: 0 2rpx 20rpx rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 40rpx;
  height: 100rpx;
}

.header-left .page-title {
  font-size: 40rpx;
  font-weight: 700;
  background: linear-gradient(45deg, #667eea, #764ba2);
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  letter-spacing: 1rpx;
}

.header-right {
  display: flex;
  align-items: center;
}

.filter-trigger {
  padding: 16rpx 24rpx;
  background: rgba(102, 126, 234, 0.1);
  border-radius: 50rpx;
  transition: all 0.3s ease;
}

.filter-text {
  font-size: 28rpx;
  color: #667eea;
  font-weight: 600;
}

.filter-trigger:active {
  transform: scale(0.95);
  background: rgba(102, 126, 234, 0.2);
}

/* 筛选面板样式 */
.filter-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.4);
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.filter-overlay.active {
  opacity: 1;
  visibility: visible;
}

.filter-drawer {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #ffffff;
  border-radius: 24rpx 24rpx 0 0;
  max-height: 80vh;
  transform: translateY(100%);
  transition: transform 0.3s ease;
}

.filter-overlay.active .filter-drawer {
  transform: translateY(0);
}

.filter-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 32rpx 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.filter-title {
  font-size: 36rpx;
  font-weight: 700;
  background: linear-gradient(45deg, #667eea, #764ba2);
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  letter-spacing: 0.5rpx;
}

.filter-close {
  padding: 8rpx 16rpx;
}

.close-text {
  font-size: 28rpx;
  color: #007aff;
  font-weight: 500;
}

.filter-body {
  padding: 0 32rpx 32rpx;
  max-height: 60vh;
  overflow-y: auto;
}

.filter-group {
  margin-bottom: 48rpx;
}

.filter-group:last-child {
  margin-bottom: 0;
}

.group-title {
  font-size: 30rpx;
  font-weight: 700;
  background: linear-gradient(45deg, #667eea, #764ba2);
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  display: block;
  margin-bottom: 24rpx;
  letter-spacing: 0.5rpx;
}

.option-list {
  display: flex;
  flex-direction: column;
  gap: 0;
}

.option-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
  transition: background-color 0.2s ease;
}

.option-item:last-child {
  border-bottom: none;
}

.option-item.selected {
  background-color: #f8f9ff;
}

.option-text {
  font-size: 28rpx;
  color: #333333;
}

.option-item.selected .option-text {
  color: #007aff;
  font-weight: 500;
}

.option-check {
  width: 40rpx;
  height: 40rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 50%;
  position: relative;
  transition: all 0.2s ease;
}

.option-check.checked {
  border-color: #007aff;
  background-color: #007aff;
}

.option-check.checked::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 12rpx;
  height: 12rpx;
  background-color: #ffffff;
  border-radius: 50%;
}

/* 内容区域 */
.content {
  padding: 24rpx 32rpx 120rpx;
}

/* 任务列表 */
.tasks-list {
  display: flex;
  flex-direction: column;
  gap: 32rpx;
}

/* Instagram风格任务卡片 - 渐变玻璃效果 */
.task-item {
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.9) 0%,
    rgba(255, 255, 255, 0.8) 50%,
    rgba(248, 250, 255, 0.85) 100%);
  backdrop-filter: blur(20rpx);
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow:
    0 8rpx 32rpx rgba(102, 126, 234, 0.2),
    0 2rpx 8rpx rgba(118, 75, 162, 0.1),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.8);
  border: 1rpx solid rgba(255, 255, 255, 0.6);
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  margin: 0 24rpx 32rpx 24rpx;
  position: relative;
}

.task-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4rpx;
  background: linear-gradient(90deg, #667eea, #764ba2, #f093fb, #f5576c);
  background-size: 300% 100%;
  animation: gradient-shift 3s ease infinite;
}

@keyframes gradient-shift {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

.task-item:active {
  transform: translateY(-4rpx) scale(0.98);
  box-shadow:
    0 12rpx 40rpx rgba(102, 126, 234, 0.2),
    0 4rpx 12rpx rgba(0, 0, 0, 0.15);
}

/* Instagram风格图片区域 */
.images-section {
  display: flex;
  align-items: center;
  padding: 40rpx 40rpx 32rpx;
  gap: 40rpx;
  background: transparent;
}

.image-container {
  position: relative;
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.task-image {
  width: 200rpx;
  height: 200rpx;
  border-radius: 20rpx;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  box-shadow:
    0 8rpx 24rpx rgba(102, 126, 234, 0.15),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.6);
  border: 3rpx solid rgba(255, 255, 255, 0.9);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.task-image::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transform: rotate(45deg);
  transition: all 0.6s ease;
  opacity: 0;
}

.task-image:active::before {
  opacity: 1;
  animation: shimmer 0.6s ease;
}

@keyframes shimmer {
  0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
  100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

.image-label-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 16rpx;
  padding: 8rpx 16rpx;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 20rpx;
  backdrop-filter: blur(10rpx);
}

.image-label {
  font-size: 26rpx;
  color: #667eea;
  font-weight: 600;
  letter-spacing: 0.5rpx;
}

.image-action-hint {
  font-size: 22rpx;
  color: #999999;
  margin-top: 2rpx;
  font-weight: 500;
}

.error-action-hint {
  font-size: 22rpx;
  color: #ff6b6b;
  margin-top: 4rpx;
  cursor: pointer;
}

.arrow-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60rpx;
}

.arrow-icon {
  font-size: 32rpx;
  color: #cccccc;
  font-weight: bold;
}

/* 结果状态 */
.result-success {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.result-processing,
.result-queued,
.result-failed {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 180rpx;
  height: 180rpx;
  border-radius: 16rpx;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border: 2rpx dashed rgba(0, 0, 0, 0.1);
  box-shadow: inset 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.processing-icon {
  margin-bottom: 16rpx;
}

.loading-spinner {
  width: 48rpx;
  height: 48rpx;
  border: 3rpx solid #f0f0f0;
  border-top: 3rpx solid #007aff;
  border-radius: 50%;
  animation: loading-spin 1s linear infinite;
}

@keyframes loading-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.processing-text,
.queued-text,
.failed-text {
  font-size: 24rpx;
  color: #666666;
  margin-bottom: 12rpx;
}

.progress-bar {
  width: 120rpx;
  height: 8rpx;
  background-color: rgba(0, 0, 0, 0.1);
  border-radius: 4rpx;
  overflow: hidden;
  box-shadow: inset 0 1rpx 3rpx rgba(0, 0, 0, 0.1);
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #74b9ff 0%, #0984e3 100%);
  border-radius: 4rpx;
  transition: width 0.3s ease;
  box-shadow: 0 1rpx 3rpx rgba(116, 185, 255, 0.3);
}

.queued-icon,
.failed-icon {
  font-size: 48rpx;
  margin-bottom: 16rpx;
  opacity: 0.6;
}

/* Instagram风格任务信息 */
.task-info {
  padding: 32rpx 40rpx 40rpx;
  background: transparent;
  border-top: none;
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.prompt-row {
  flex-direction: column;
  align-items: flex-start;
  margin-bottom: 20rpx;
  padding: 16rpx;
  background: rgba(102, 126, 234, 0.05);
  border-radius: 12rpx;
  border-left: 4rpx solid #667eea;
}

.prompt-row .info-label {
  margin-bottom: 8rpx;
  color: #667eea;
  font-weight: 600;
}

.prompt-text {
  line-height: 1.6;
  word-break: break-all;
  color: #333333;
}

.info-label {
  font-size: 26rpx;
  color: #999999;
}

.info-value {
  font-size: 26rpx;
  color: #666666;
  font-weight: 500;
}

/* 状态区域 */
.status-section {
  margin: 24rpx 0;
}

.status-badge {
  display: inline-block;
  padding: 12rpx 20rpx;
  border-radius: 24rpx;
  font-size: 22rpx;
  font-weight: 600;
  letter-spacing: 0.5rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
}

.status-queued {
  background: linear-gradient(135deg, #ffeaa7 0%, #fdcb6e 100%);
  color: #d63031;
  box-shadow: 0 2rpx 8rpx rgba(253, 203, 110, 0.3);
}

.status-processing {
  background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
  color: #ffffff;
  box-shadow: 0 2rpx 8rpx rgba(116, 185, 255, 0.4);
}

.status-completed {
  background: linear-gradient(135deg, #55efc4 0%, #00b894 100%);
  color: #ffffff;
  box-shadow: 0 2rpx 8rpx rgba(85, 239, 196, 0.4);
}

.status-failed {
  background: linear-gradient(135deg, #fd79a8 0%, #e84393 100%);
  color: #ffffff;
  box-shadow: 0 2rpx 8rpx rgba(253, 121, 168, 0.4);
}

/* 操作按钮 */
.task-actions {
  display: flex;
  gap: 20rpx;
  margin-top: 32rpx;
}

/* Instagram风格操作按钮 */
.action-btn {
  flex: 1;
  padding: 24rpx 32rpx;
  border-radius: 50rpx;
  text-align: center;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  box-shadow:
    0 4rpx 16rpx rgba(102, 126, 234, 0.2),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.8);
  border: none;
  position: relative;
  overflow: hidden;
}

.action-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: left 0.5s ease;
}

.action-btn:active {
  transform: translateY(-2rpx) scale(0.98);
  box-shadow:
    0 6rpx 20rpx rgba(102, 126, 234, 0.3),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.9);
}

.action-btn:active::before {
  left: 100%;
}

.retry-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  box-shadow:
    0 6rpx 20rpx rgba(102, 126, 234, 0.4),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.3);
}

.retry-btn .action-text {
  color: #ffffff;
  font-size: 28rpx;
  font-weight: 700;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
}

.delete-btn {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
  border: none;
  box-shadow:
    0 6rpx 20rpx rgba(255, 107, 107, 0.4),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.3);
}

.delete-btn .action-text {
  color: #ffffff;
  font-size: 28rpx;
  font-weight: 700;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
}

/* Instagram风格全局动画 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0rpx);
  }
  50% {
    transform: translateY(-10rpx);
  }
}

.task-item {
  animation: fadeInUp 0.6s ease forwards;
}

.task-item:nth-child(1) { animation-delay: 0.1s; }
.task-item:nth-child(2) { animation-delay: 0.2s; }
.task-item:nth-child(3) { animation-delay: 0.3s; }
.task-item:nth-child(4) { animation-delay: 0.4s; }
.task-item:nth-child(5) { animation-delay: 0.5s; }

/* 加载状态 */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 80rpx 0;
}

.loading-dot {
  width: 48rpx;
  height: 48rpx;
  border: 3rpx solid #f0f0f0;
  border-top: 3rpx solid #007aff;
  border-radius: 50%;
  animation: loading-spin 1s linear infinite;
}

.loading-tip {
  font-size: 26rpx;
  color: #999999;
  margin-top: 24rpx;
}

/* 结束提示 */
.end-tip {
  text-align: center;
  padding: 60rpx 0;
}

.end-text {
  font-size: 26rpx;
  color: #cccccc;
}

/* 空状态 */
.empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 160rpx 0;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 32rpx;
  opacity: 0.3;
}

.empty-title {
  font-size: 32rpx;
  color: #666666;
  font-weight: 500;
  margin-bottom: 16rpx;
}

.empty-desc {
  font-size: 26rpx;
  color: #999999;
  text-align: center;
  line-height: 1.5;
  margin-bottom: 48rpx;
}

.empty-action {
  
}

.create-btn {
  padding: 24rpx 48rpx;
  background-color: #007aff;
  color: #ffffff;
  border-radius: 24rpx;
  font-size: 28rpx;
  font-weight: 500;
  border: none;
}

.create-btn:active {
  background-color: #0056cc;
  transform: scale(0.98);
}

.create-btn::after {
  border: none;
}
