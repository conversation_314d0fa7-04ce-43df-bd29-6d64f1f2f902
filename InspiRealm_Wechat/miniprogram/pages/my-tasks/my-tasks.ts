// pages/my-tasks/my-tasks.ts
import { userApi, errorHandler, utils } from '../../utils/api'

interface ProcessTask {
  taskNo: string
  originalImageUrl: string
  resultImageUrl?: string
  processType: string
  status: 'queued' | 'processing' | 'completed' | 'failed'
  statusDesc: string
  progress?: number
  errorMessage?: string
  submitTime: string
  completeTime?: string
  processedImage?: any
}

Page({
  data: {
    tasks: [] as ProcessTask[],
    loading: false,
    hasMore: true,
    page: 1,
    pageSize: 10,
    total: 0,
    
    // 筛选条件
    activeStatus: '',
    activeProcessType: '',
    
    // 筛选选项
    statusOptions: [
      { value: '', label: '全部状态' },
      { value: '1', label: '待处理' },
      { value: '2', label: '处理中' },
      { value: '3', label: '已完成' },
      { value: '4', label: '失败' }
    ],
    processTypes: [
      { value: '', label: '全部类型' },
      { value: 'beautify', label: '美化' },
      { value: 'style_transfer', label: '风格转换' },
      { value: 'enhance', label: '增强' },
      { value: 'cartoon', label: '卡通化' }
    ],
    
    // UI状态
    showFilterPanel: false,
    refreshing: false,
    
    // 轮询定时器
    pollTimer: null as any
  },

  onLoad() {
    this.loadMyTasks(true)
  },

  onUnload() {
    // 页面卸载时必须清理轮询定时器
    this.stopPolling()
  },

  onShow() {
    // 页面显示时刷新数据并检查是否需要轮询
    this.loadMyTasks(true)
  },

  onHide() {
    // 页面隐藏时停止轮询以节省资源
    this.stopPolling()
  },

  onPullDownRefresh() {
    this.setData({ refreshing: true })
    this.loadMyTasks(true).finally(() => {
      this.setData({ refreshing: false })
      wx.stopPullDownRefresh()
    })
  },

  onReachBottom() {
    if (this.data.hasMore && !this.data.loading) {
      this.loadMyTasks(false)
    }
  },

  /**
   * 开始轮询
   */
  startPolling() {
    this.stopPolling()

    // 设置轮询间隔为4秒，平衡用户体验和服务器压力
    this.data.pollTimer = setInterval(() => {
      // 检查是否有"生成中"状态的任务
      const hasProcessingTasks = this.data.tasks.some(task =>
        task.status === 'processing' || task.status === 'queued'
      )

      if (hasProcessingTasks) {
        console.log('检测到进行中的任务，继续轮询...')
        this.loadMyTasks(true, true) // 静默刷新，不显示loading
      } else {
        console.log('所有任务已完成，停止轮询')
        this.stopPolling() // 所有任务完成时自动停止轮询
      }
    }, 4000) // 每4秒轮询一次
  },

  /**
   * 停止轮询
   */
  stopPolling() {
    if (this.data.pollTimer) {
      clearInterval(this.data.pollTimer)
      this.data.pollTimer = null
      console.log('轮询已停止')
    }
  },

  /**
   * 检查是否需要开始轮询
   */
  checkAndStartPolling() {
    const hasProcessingTasks = this.data.tasks.some(task =>
      task.status === 'processing' || task.status === 'queued'
    )

    if (hasProcessingTasks && !this.data.pollTimer) {
      console.log('发现进行中的任务，开始轮询')
      this.startPolling()
    } else if (!hasProcessingTasks && this.data.pollTimer) {
      console.log('没有进行中的任务，停止轮询')
      this.stopPolling()
    }
  },

  /**
   * 加载我的任务列表
   */
  async loadMyTasks(reset: boolean = false, silent: boolean = false) {
    if (this.data.loading && !silent) return

    if (!silent) {
      this.setData({ loading: true })
    }

    try {
      const page = reset ? 1 : this.data.page + 1
      const params = {
        page,
        size: this.data.pageSize,
        status: this.data.activeStatus || undefined,
        processType: this.data.activeProcessType || undefined
      }

      // 调用真实的API接口
      const response = await userApi.getMyTasks(params)

      // 转换数据格式以匹配前端接口
      const formattedTasks = response.data.records.map(task => ({
        taskNo: task.taskNo,
        originalImageUrl: task.originalImageUrl,
        resultImageUrl: task.resultImageUrl,
        processType: task.processType,
        status: this.convertStatus(task.status),
        statusDesc: task.statusDesc,
        progress: task.progress,
        errorMessage: task.errorMessage,
        submitTime: task.submitTime,
        completeTime: task.completeTime,
        processedImage: task.processedImage,
        prompt: task.prompt || '', // 添加提示词字段
        textContent: task.textContent || '', // 添加文字内容字段
        aspectRatio: task.aspectRatio || '', // 添加尺寸比例字段
        style: task.style || '' // 添加风格字段
      }))

      this.setData({
        tasks: reset ? formattedTasks : [...this.data.tasks, ...formattedTasks],
        page,
        total: response.data.total,
        hasMore: page * this.data.pageSize < response.data.total,
        loading: false
      })

      // 数据加载完成后检查是否需要开始轮询
      if (!silent) {
        this.checkAndStartPolling()
      }

    } catch (error) {
      console.error('加载我的任务失败:', error)
      if (!silent) {
        this.setData({ loading: false })

        if (reset && this.data.tasks.length === 0) {
          errorHandler.showError(error as Error, '加载失败')
        }
      }
    }
  },

  /**
   * 处理任务点击（保留原有功能）
   */
  onTaskTap(e: any) {
    const { task } = e.currentTarget.dataset

    // 检查点击的是否是按钮区域，如果是则不处理
    const target = e.target
    if (target && (target.dataset.task ||
        (target.classList && target.classList.contains('action-btn')) ||
        (target.closest && target.closest('.action-btn')))) {
      return
    }

    if (task.status === 'completed' && task.resultImageUrl) {
      // 预览结果图片
      wx.previewImage({
        current: task.resultImageUrl,
        urls: [task.originalImageUrl, task.resultImageUrl]
      })
    } else if (task.status === 'failed') {
      // 显示错误信息
      this.showErrorDetail(e)
    }
  },

  /**
   * 查看原图
   */
  viewOriginalImage(e: any) {
    const { task } = e.currentTarget.dataset

    if (task.originalImageUrl) {
      wx.previewImage({
        current: task.originalImageUrl,
        urls: [task.originalImageUrl]
      })
    } else {
      wx.showToast({
        title: '原图不存在',
        icon: 'none'
      })
    }
  },

  /**
   * 查看结果图
   */
  viewResultImage(e: any) {
    const { task } = e.currentTarget.dataset

    if (task.resultImageUrl) {
      wx.previewImage({
        current: task.resultImageUrl,
        urls: [task.resultImageUrl, task.originalImageUrl]
      })
    } else {
      wx.showToast({
        title: '结果图不存在',
        icon: 'none'
      })
    }
  },

  /**
   * 显示错误详情
   */
  showErrorDetail(e: any) {
    const { task } = e.currentTarget.dataset

    const errorMessage = task.errorMessage || '图片处理失败，请重试'

    wx.showModal({
      title: '处理失败详情',
      content: errorMessage,
      showCancel: true,
      cancelText: '关闭',
      confirmText: '重新处理',
      success: (res) => {
        if (res.confirm) {
          // 用户选择重新处理
          this.retryTask({ currentTarget: { dataset: { task } } })
        }
      }
    })
  },

  /**
   * 长按保存图片
   */
  onImageLongPress(e: any) {
    const { url, type } = e.currentTarget.dataset
    
    wx.showActionSheet({
      itemList: ['保存图片'],
      success: (res) => {
        if (res.tapIndex === 0) {
          this.saveImageToAlbum(url, type)
        }
      }
    })
  },

  /**
   * 保存图片到相册
   */
  saveImageToAlbum(imageUrl: string, imageType: string) {
    wx.showLoading({
      title: '保存中...'
    })

    wx.downloadFile({
      url: imageUrl,
      success: (res) => {
        if (res.statusCode === 200) {
          wx.saveImageToPhotosAlbum({
            filePath: res.tempFilePath,
            success: () => {
              wx.hideLoading()
              wx.showToast({
                title: `${imageType === 'original' ? '原图' : '结果图'}保存成功`,
                icon: 'success'
              })
            },
            fail: (error) => {
              wx.hideLoading()
              if (error.errMsg.includes('auth deny')) {
                wx.showModal({
                  title: '需要相册权限',
                  content: '请在设置中开启相册权限',
                  confirmText: '去设置',
                  success: (res) => {
                    if (res.confirm) {
                      wx.openSetting()
                    }
                  }
                })
              } else {
                wx.showToast({
                  title: '保存失败',
                  icon: 'none'
                })
              }
            }
          })
        } else {
          wx.hideLoading()
          wx.showToast({
            title: '下载失败',
            icon: 'none'
          })
        }
      },
      fail: () => {
        wx.hideLoading()
        wx.showToast({
          title: '下载失败',
          icon: 'none'
        })
      }
    })
  },

  /**
   * 切换筛选面板
   */
  toggleFilterPanel() {
    this.setData({
      showFilterPanel: !this.data.showFilterPanel
    })
  },

  /**
   * 转换后端状态码为前端状态字符串
   */
  convertStatus(status: number): 'queued' | 'processing' | 'completed' | 'failed' {
    switch (status) {
      case 1:
        return 'queued'
      case 2:
        return 'processing'
      case 3:
        return 'completed'
      case 4:
      case 5:
        return 'failed'
      default:
        return 'queued'
    }
  },

  /**
   * 选择状态筛选
   */
  onStatusChange(e: any) {
    const { value } = e.currentTarget.dataset
    this.setData({
      activeStatus: value,
      showFilterPanel: false
    })
    this.loadMyTasks(true)
  },

  /**
   * 选择处理类型
   */
  onProcessTypeChange(e: any) {
    const { value } = e.currentTarget.dataset
    this.setData({
      activeProcessType: value,
      showFilterPanel: false
    })
    this.loadMyTasks(true)
  },

  /**
   * 关闭筛选面板
   */
  closeFilterPanel() {
    this.setData({
      showFilterPanel: false
    })
  },

  /**
   * 重新处理失败的任务
   */
  async retryTask(e: any) {
    const { task } = e.currentTarget.dataset

    wx.showModal({
      title: '重新处理',
      content: '重新处理需要消耗10积分，确定继续吗？',
      success: async (res) => {
        if (res.confirm) {
          try {
            wx.showLoading({
              title: '提交中...',
              mask: true
            })

            // 调用重新处理API
            const response = await userApi.retryTask(task.taskNo)

            wx.hideLoading()

            if (response.code === 200) {
              wx.showToast({
                title: '重新处理成功',
                icon: 'success'
              })

              // 刷新任务列表
              this.loadMyTasks(true)

              // 可选：跳转到新任务详情或保持在当前页面
              // wx.navigateTo({
              //   url: `/pages/task-detail/task-detail?taskNo=${response.data.taskNo}`
              // })
            } else {
              wx.showToast({
                title: response.message || '重新处理失败',
                icon: 'none'
              })
            }

          } catch (error) {
            wx.hideLoading()
            console.error('重新处理任务失败:', error)

            let errorMessage = '重新处理失败，请重试'
            if (error && typeof error === 'object' && 'message' in error) {
              errorMessage = (error as any).message || errorMessage
            }

            wx.showToast({
              title: errorMessage,
              icon: 'none'
            })
          }
        }
      }
    })
  },

  /**
   * 确认删除任务
   */
  confirmDeleteTask(e: any) {
    console.log('confirmDeleteTask 事件数据:', e.currentTarget.dataset)
    const { task } = e.currentTarget.dataset

    if (!task) {
      console.error('任务数据不存在')
      wx.showToast({
        title: '任务数据错误',
        icon: 'none'
      })
      return
    }

    if (!task.taskNo) {
      console.error('任务编号不存在')
      wx.showToast({
        title: '任务编号错误',
        icon: 'none'
      })
      return
    }

    wx.showModal({
      title: '确认删除',
      content: '删除后将无法恢复，确定要删除这个任务吗？',
      confirmColor: '#ff4757',
      success: (res) => {
        if (res.confirm) {
          this.deleteTask(task)
        }
      }
    })
  },

  /**
   * 删除任务（内部方法）
   */
  async deleteTask(task: any) {
    try {
      console.log('开始删除任务:', task.taskNo)

      wx.showLoading({
        title: '删除中...'
      })

      // 调用后端API删除任务
      await userApi.deleteTask(task.taskNo)
      console.log('后端删除成功')

      wx.hideLoading()
      wx.showToast({
        title: '删除成功',
        icon: 'success'
      })

      // 重新加载数据，确保与服务器数据同步
      this.loadMyTasks(true)

    } catch (error) {
      console.error('删除任务失败:', error)
      wx.hideLoading()

      let errorMessage = '删除失败'
      if (error && typeof error === 'object' && 'message' in error) {
        errorMessage = (error as any).message
      }

      wx.showToast({
        title: errorMessage,
        icon: 'none'
      })
    }
  },

  /**
   * 跳转到创作页面
   */
  goToProcess() {
    wx.switchTab({
      url: '/pages/process/process'
    })
  }
})
