<!--pages/my-tasks/my-tasks.wxml-->
<view class="page-container">
  <!-- 顶部导航栏 -->
  <view class="header">
    <view class="header-content">
      <view class="header-left">
        <text class="page-title">魔法工坊</text>
      </view>
      <view class="header-right">
        <view class="filter-trigger" bindtap="toggleFilterPanel">
          <text class="filter-text">筛选</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 筛选面板 -->
  <view class="filter-overlay {{showFilterPanel ? 'active' : ''}}" bindtap="closeFilterPanel">
    <view class="filter-drawer" catchtap="">
      <view class="filter-header">
        <text class="filter-title">筛选条件</text>
        <view class="filter-close" bindtap="closeFilterPanel">
          <text class="close-text">完成</text>
        </view>
      </view>
      
      <view class="filter-body">
        <view class="filter-group">
          <text class="group-title">任务状态</text>
          <view class="option-list">
            <view 
              wx:for="{{statusOptions}}" 
              wx:key="value"
              class="option-item {{activeStatus === item.value ? 'selected' : ''}}"
              data-value="{{item.value}}"
              bindtap="onStatusChange"
            >
              <text class="option-text">{{item.label}}</text>
              <view class="option-check {{activeStatus === item.value ? 'checked' : ''}}"></view>
            </view>
          </view>
        </view>
        
        <view class="filter-group">
          <text class="group-title">处理类型</text>
          <view class="option-list">
            <view 
              wx:for="{{processTypes}}" 
              wx:key="value"
              class="option-item {{activeProcessType === item.value ? 'selected' : ''}}"
              data-value="{{item.value}}"
              bindtap="onProcessTypeChange"
            >
              <text class="option-text">{{item.label}}</text>
              <view class="option-check {{activeProcessType === item.value ? 'checked' : ''}}"></view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 内容区域 -->
  <view class="content">
    <!-- 任务列表 -->
    <view class="tasks-list">
      <view
        wx:for="{{tasks}}"
        wx:key="taskNo"
        wx:for-index="taskIndex"
        class="task-item"
      >
        <!-- 图片区域 -->
        <view class="images-section" data-task="{{item}}" bindtap="onTaskTap">
          <!-- 原图 -->
          <view class="image-container original">
            <image
              class="task-image"
              src="{{item.originalImageUrl}}"
              mode="aspectFill"
              data-url="{{item.originalImageUrl}}"
              data-type="original"
              data-task="{{item}}"
              bindtap="viewOriginalImage"
              bindlongpress="onImageLongPress"
            />
            <view class="image-label-container">
              <text class="image-label">原图</text>
              <text class="image-action-hint">点击查看</text>
            </view>
          </view>
          
          <!-- 箭头 -->
          <view class="arrow-container">
            <text class="arrow-icon">→</text>
          </view>
          
          <!-- 结果图 -->
          <view class="image-container result">
            <view wx:if="{{item.status === 'completed' && item.resultImageUrl}}" class="result-success">
              <image
                class="task-image"
                src="{{item.resultImageUrl}}"
                mode="aspectFill"
                data-url="{{item.resultImageUrl}}"
                data-type="result"
                data-task="{{item}}"
                bindtap="viewResultImage"
                bindlongpress="onImageLongPress"
              />
              <view class="image-label-container">
                <text class="image-label">结果</text>
                <text class="image-action-hint">点击查看</text>
              </view>
            </view>
            
            <view wx:elif="{{item.status === 'processing'}}" class="result-processing">
              <view class="processing-icon">
                <view class="loading-spinner"></view>
              </view>
              <text class="processing-text">处理中</text>
              <view wx:if="{{item.progress}}" class="progress-bar">
                <view class="progress-fill" style="width: {{item.progress}}%"></view>
              </view>
            </view>
            
            <view wx:elif="{{item.status === 'queued'}}" class="result-queued">
              <view class="queued-icon">⏳</view>
              <text class="queued-text">排队中</text>
            </view>
            
            <view wx:elif="{{item.status === 'failed'}}" class="result-failed" data-task="{{item}}" bindtap="showErrorDetail">
              <view class="failed-icon">❌</view>
              <text class="failed-text">处理失败</text>
              <text class="error-action-hint">点击查看详情</text>
            </view>
          </view>
        </view>

        <!-- 任务信息 -->
        <view class="task-info">
          <!-- 提示词显示 -->
          <view wx:if="{{item.prompt}}" class="info-row prompt-row">
            <text class="info-label">提示词</text>
            <text class="info-value prompt-text">{{item.prompt}}</text>
          </view>

          <view class="info-row">
            <text class="info-label">处理类型</text>
            <text class="info-value">{{item.processType}}</text>
          </view>

          <view class="info-row">
            <text class="info-label">提交时间</text>
            <text class="info-value">{{item.submitTime}}</text>
          </view>
          
          <view wx:if="{{item.completeTime}}" class="info-row">
            <text class="info-label">完成时间</text>
            <text class="info-value">{{item.completeTime}}</text>
          </view>
          
          <!-- 状态标签 -->
          <view class="status-section">
            <view class="status-badge status-{{item.status}}">
              <text class="status-text">{{item.statusDesc || item.status}}</text>
            </view>
          </view>
          
          <!-- 操作按钮 -->
          <view class="task-actions">
            <view
              wx:if="{{item.status === 'failed'}}"
              class="action-btn retry-btn"
              data-task="{{item}}"
              catchtap="retryTask"
            >
              <text class="action-text">重新处理</text>
            </view>

            <!-- 删除按钮：处理中的任务不显示删除按钮 -->
            <view
              wx:if="{{item.status !== 'processing'}}"
              class="action-btn delete-btn"
              data-task="{{item}}"
              catchtap="confirmDeleteTask"
            >
              <text class="action-text">删除</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 加载状态 -->
    <view wx:if="{{loading}}" class="loading-state">
      <view class="loading-dot"></view>
      <text class="loading-tip">加载中</text>
    </view>

    <!-- 没有更多 -->
    <view wx:if="{{!hasMore && tasks.length > 0}}" class="end-tip">
      <text class="end-text">没有更多任务了</text>
    </view>

    <!-- 空状态 -->
    <view wx:if="{{tasks.length === 0 && !loading}}" class="empty">
      <view class="empty-icon">⚡</view>
      <text class="empty-title">还没有处理任务</text>
      <text class="empty-desc">去创作页面开始处理图片吧</text>
      <view class="empty-action">
        <button class="create-btn" bindtap="goToProcess">
          开始创作
        </button>
      </view>
    </view>
  </view>
</view>
