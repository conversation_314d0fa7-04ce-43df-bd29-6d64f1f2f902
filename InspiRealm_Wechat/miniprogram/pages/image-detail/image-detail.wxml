<!-- pages/image-detail/image-detail.wxml -->
<view class="page-container">
  <!-- 顶部导航 -->
  <view class="header">
    <view class="header-content">
      <view class="header-left">
        <text class="page-title">图片详情</text>
      </view>
    </view>
  </view>

  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-container">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 图片详情内容 -->
  <view wx:else class="detail-content">
    <!-- 图片展示区域 -->
    <view class="image-section">
      <!-- 参考图 -->
      <view wx:if="{{imageDetail.originalImageUrl}}" class="image-container">
        <text class="image-label">参考图</text>
        <image
          class="detail-image"
          src="{{imageDetail.originalImageUrl}}"
          mode="aspectFit"
          bindtap="onImagePreview"
          bindlongpress="onImageLongPress"
          data-url="{{imageDetail.originalImageUrl}}"
        />
      </view>

      <!-- 结果图 -->
      <view class="image-container">
        <text class="image-label">结果图</text>
        <image
          class="detail-image"
          src="{{imageDetail.imageUrl}}"
          mode="aspectFit"
          bindtap="onImagePreview"
          bindlongpress="onImageLongPress"
          data-url="{{imageDetail.imageUrl}}"
        />
      </view>
    </view>

    <!-- 图片信息 -->
    <view class="info-section">
      <!-- 作者信息 -->
      <view wx:if="{{imageDetail.userInfo}}" class="author-info">
        <image class="author-avatar" src="{{imageDetail.userInfo.avatarUrl}}" />
        <view class="author-details">
          <text class="author-name">{{imageDetail.userInfo.nickname}}</text>
          <text class="create-time">{{imageDetail.createTime}}</text>
        </view>
      </view>

      <!-- 图片统计 -->
      <view class="stats-info">
        <view class="stat-item">
          <text class="stat-label">浏览</text>
          <text class="stat-value">{{imageDetail.viewCount}}</text>
        </view>
        <view class="stat-item">
          <text class="stat-label">点赞</text>
          <text class="stat-value">{{imageDetail.likeCount}}</text>
        </view>
        <view class="stat-item">
          <text class="stat-label">比例</text>
          <text class="stat-value">{{imageDetail.aspectRatio}}</text>
        </view>
      </view>

      <!-- 提示词区域 -->
      <view wx:if="{{imageDetail.prompt}}" class="prompt-section">
        <view class="prompt-header">
          <text class="prompt-title">提示词</text>
          <view class="copy-btn" bindtap="onCopyPrompt" data-text="{{imageDetail.prompt}}">
            <text class="copy-text">复制</text>
          </view>
        </view>
        <view class="prompt-content">
          <text class="prompt-text">{{imageDetail.prompt}}</text>
        </view>
      </view>

      <!-- 负面提示词区域 -->
      <view wx:if="{{imageDetail.negativePrompt}}" class="prompt-section">
        <view class="prompt-header">
          <text class="prompt-title">负面提示词</text>
          <view class="copy-btn" bindtap="onCopyPrompt" data-text="{{imageDetail.negativePrompt}}">
            <text class="copy-text">复制</text>
          </view>
        </view>
        <view class="prompt-content">
          <text class="prompt-text">{{imageDetail.negativePrompt}}</text>
        </view>
      </view>

      <!-- 技术信息 -->
      <view class="tech-info">
        <view class="tech-item">
          <text class="tech-label">处理类型</text>
          <text class="tech-value">{{imageDetail.processType}}</text>
        </view>
        <view class="tech-item">
          <text class="tech-label">图片尺寸</text>
          <text class="tech-value">{{imageDetail.imageWidth}} × {{imageDetail.imageHeight}}</text>
        </view>
        <view class="tech-item">
          <text class="tech-label">审核状态</text>
          <text class="tech-value">{{auditStatusText}}</text>
        </view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="action-section" wx:if="{{!fromMyWorks}}">
      <view
        class="action-btn like-btn {{imageDetail.isLiked ? 'liked' : ''}}"
        bindtap="onToggleLike"
      >
        <text class="action-icon">{{imageDetail.isLiked ? '❤️' : '🤍'}}</text>
        <text class="action-text">{{imageDetail.isLiked ? '已点赞' : '点赞'}}</text>
      </view>
    </view>

    <!-- 来自作品展厅的提示 -->
    <view class="my-work-tip" wx:if="{{fromMyWorks}}">
      <text class="tip-text">这是您的作品</text>
    </view>
  </view>

  <!-- 错误状态 -->
  <view wx:if="{{error}}" class="error-container">
    <text class="error-text">{{error}}</text>
    <view class="retry-btn" bindtap="loadImageDetail">
      <text class="retry-text">重试</text>
    </view>
  </view>
</view>
