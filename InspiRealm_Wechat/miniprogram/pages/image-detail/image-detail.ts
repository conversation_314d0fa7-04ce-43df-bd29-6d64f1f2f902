// pages/image-detail/image-detail.ts
import { squareApi, loginUtils } from '../../utils/api'

Page({
  data: {
    imageId: 0,
    imageDetail: null as any,
    loading: true,
    error: '',
    auditStatusText: '',
    fromMyWorks: false // 是否来自作品展厅
  },

  onLoad(options: any) {
    console.log('🖼️ 图片详情页面加载，参数:', options)

    if (options.id) {
      this.setData({
        imageId: parseInt(options.id),
        fromMyWorks: options.from === 'my-works' // 检查是否来自作品展厅
      })
      this.loadImageDetail()
    } else {
      this.setData({
        loading: false,
        error: '缺少图片ID参数'
      })
    }
  },

  onShow() {
    console.log('🖼️ 图片详情页面显示')
  },



  onHide() {
    // 页面隐藏时的清理工作
    console.log('🖼️ 图片详情页面隐藏')
  },

  onUnload() {
    // 页面卸载时的清理工作
    console.log('🖼️ 图片详情页面卸载')
  },

  /**
   * 加载图片详情
   */
  async loadImageDetail() {
    console.log('📡 开始加载图片详情，imageId:', this.data.imageId)
    
    this.setData({
      loading: true,
      error: ''
    })

    try {
      const response = await squareApi.getImageDetail(this.data.imageId)
      console.log('📡 图片详情API响应:', response)
      
      const imageDetail = response.data
      
      // 处理审核状态文本
      let auditStatusText = ''
      switch (imageDetail.auditStatus) {
        case 0:
          auditStatusText = '待审核'
          break
        case 1:
          auditStatusText = '审核通过'
          break
        case 2:
          auditStatusText = '审核拒绝'
          break
        default:
          auditStatusText = '未知状态'
      }

      this.setData({
        imageDetail,
        auditStatusText,
        loading: false
      })

      console.log('✅ 图片详情加载成功')

    } catch (error) {
      console.error('❌ 图片详情加载失败:', error)
      this.setData({
        loading: false,
        error: error.message || '加载失败，请重试'
      })
    }
  },

  /**
   * 图片预览
   */
  onImagePreview(e: any) {
    const { url } = e.currentTarget.dataset
    console.log('🔍 预览图片:', url)
    
    const urls = []
    if (this.data.imageDetail.originalImageUrl) {
      urls.push(this.data.imageDetail.originalImageUrl)
    }
    if (this.data.imageDetail.imageUrl) {
      urls.push(this.data.imageDetail.imageUrl)
    }

    wx.previewImage({
      current: url,
      urls: urls
    })

    // 调用预览接口增加浏览量
    this.incrementViewCount()
  },

  /**
   * 增加浏览量
   */
  async incrementViewCount() {
    try {
      await squareApi.viewImage(this.data.imageId)
      console.log('📈 浏览量+1成功')
    } catch (error) {
      console.error('📈 浏览量+1失败:', error)
    }
  },

  /**
   * 切换点赞状态
   */
  async onToggleLike() {
    console.log('💖 点击点赞按钮')
    
    // 检查登录状态
    if (!loginUtils.checkLoginWithRedirect()) {
      return
    }

    try {
      const currentLikeStatus = this.data.imageDetail.isLiked
      const newLikeStatus = !currentLikeStatus

      // 先更新UI
      const imageDetail = { ...this.data.imageDetail }
      imageDetail.isLiked = newLikeStatus
      imageDetail.likeCount = newLikeStatus 
        ? imageDetail.likeCount + 1 
        : Math.max(0, imageDetail.likeCount - 1)
      
      this.setData({ imageDetail })

      // 触觉反馈
      wx.vibrateShort()

      // 点赞动画效果
      if (newLikeStatus) {
        // 点赞成功的特殊效果
        wx.vibrateShort({
          type: 'heavy'
        })
      }

      // 调用API
      const response = await squareApi.toggleLike(this.data.imageId, newLikeStatus)
      console.log('💖 点赞API响应:', response)

      // 更新为服务器返回的真实数据
      imageDetail.isLiked = response.data.isLiked
      imageDetail.likeCount = response.data.likeCount
      this.setData({ imageDetail })

      // 显示提示
      wx.showToast({
        title: response.message || (response.data.isLiked ? '点赞成功' : '取消点赞'),
        icon: 'success',
        duration: 1000
      })

    } catch (error) {
      console.error('💖 点赞失败:', error)
      
      // 恢复原状态
      const imageDetail = { ...this.data.imageDetail }
      imageDetail.isLiked = !imageDetail.isLiked
      imageDetail.likeCount = imageDetail.isLiked 
        ? imageDetail.likeCount + 1 
        : Math.max(0, imageDetail.likeCount - 1)
      this.setData({ imageDetail })

      wx.showToast({
        title: '操作失败，请重试',
        icon: 'none'
      })
    }
  },

  /**
   * 复制提示词
   */
  onCopyPrompt(e: any) {
    const { text } = e.currentTarget.dataset
    console.log('📋 复制提示词:', text)
    
    if (!text) {
      wx.showToast({
        title: '没有可复制的内容',
        icon: 'none'
      })
      return
    }

    wx.setClipboardData({
      data: text,
      success: () => {
        wx.showToast({
          title: '复制成功',
          icon: 'success'
        })
      },
      fail: () => {
        wx.showToast({
          title: '复制失败',
          icon: 'none'
        })
      }
    })
  },



  /**
   * 长按保存图片
   */
  onImageLongPress(e: any) {
    const { url } = e.currentTarget.dataset
    console.log('🖼️ 长按保存图片:', url)

    wx.showActionSheet({
      itemList: ['保存图片'],
      success: (res) => {
        if (res.tapIndex === 0) {
          this.saveImageToAlbum(url)
        }
      }
    })
  },

  /**
   * 保存图片到相册
   */
  saveImageToAlbum(imageUrl: string) {
    console.log('💾 开始保存图片到相册:', imageUrl)

    wx.showLoading({
      title: '保存中...',
      mask: true
    })

    wx.downloadFile({
      url: imageUrl,
      success: (res) => {
        if (res.statusCode === 200) {
          wx.saveImageToPhotosAlbum({
            filePath: res.tempFilePath,
            success: () => {
              wx.hideLoading()
              wx.showToast({
                title: '保存成功',
                icon: 'success'
              })
              console.log('✅ 图片保存成功')
            },
            fail: (error) => {
              wx.hideLoading()
              console.error('❌ 保存图片失败:', error)

              if (error.errMsg.includes('auth deny')) {
                wx.showModal({
                  title: '需要授权',
                  content: '需要您授权保存图片到相册',
                  confirmText: '去设置',
                  success: (modalRes) => {
                    if (modalRes.confirm) {
                      wx.openSetting()
                    }
                  }
                })
              } else {
                wx.showToast({
                  title: '保存失败',
                  icon: 'none'
                })
              }
            }
          })
        } else {
          wx.hideLoading()
          wx.showToast({
            title: '下载失败',
            icon: 'none'
          })
          console.error('❌ 下载图片失败，状态码:', res.statusCode)
        }
      },
      fail: (error) => {
        wx.hideLoading()
        wx.showToast({
          title: '下载失败',
          icon: 'none'
        })
        console.error('❌ 下载图片失败:', error)
      }
    })
  }
})
