/* pages/image-detail/image-detail.wxss */

/* Instagram风格图片详情页面 */
.page-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
  padding-bottom: env(safe-area-inset-bottom); /* 适配安全区域 */
  /* 确保点击事件正常 */
  pointer-events: auto;
}

/* Instagram风格顶部导航 */
.header {
  position: sticky;
  top: 0;
  z-index: 100;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-bottom: none;
  box-shadow: 0 2rpx 20rpx rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 40rpx;
  height: 100rpx;
}

.header-left .page-title {
  font-size: 40rpx;
  font-weight: 800;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  letter-spacing: 1rpx;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 200rpx 0;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid rgba(102, 126, 234, 0.2);
  border-top: 4rpx solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  margin-top: 24rpx;
  font-size: 28rpx;
  color: #666666;
}

/* 详情内容 */
.detail-content {
  padding: 24rpx;
}

/* 图片展示区域 */
.image-section {
  margin-bottom: 32rpx;
}

.image-container {
  background: linear-gradient(135deg, 
    rgba(255, 255, 255, 0.9) 0%, 
    rgba(255, 255, 255, 0.8) 50%, 
    rgba(248, 250, 255, 0.85) 100%);
  backdrop-filter: blur(20rpx);
  border-radius: 24rpx;
  padding: 24rpx;
  margin-bottom: 24rpx;
  box-shadow: 
    0 8rpx 32rpx rgba(102, 126, 234, 0.2),
    0 2rpx 8rpx rgba(118, 75, 162, 0.1),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.8);
  border: 1rpx solid rgba(255, 255, 255, 0.6);
  position: relative;
}

.image-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4rpx;
  background: linear-gradient(90deg, #667eea, #764ba2, #f093fb, #f5576c);
  background-size: 300% 100%;
  animation: gradient-shift 3s ease infinite;
  border-radius: 24rpx 24rpx 0 0;
}

.image-label {
  display: block;
  font-size: 28rpx;
  font-weight: 700;
  color: #1a1a1a;
  margin-bottom: 16rpx;
}

.detail-image {
  width: 100%;
  max-height: 600rpx;
  border-radius: 16rpx;
  display: block;
  /* 确保图片高清显示 */
  image-rendering: -webkit-optimize-contrast;
  image-rendering: crisp-edges;
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
}

/* 信息区域 */
.info-section {
  background: linear-gradient(135deg, 
    rgba(255, 255, 255, 0.9) 0%, 
    rgba(255, 255, 255, 0.8) 50%, 
    rgba(248, 250, 255, 0.85) 100%);
  backdrop-filter: blur(20rpx);
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 
    0 8rpx 32rpx rgba(102, 126, 234, 0.2),
    0 2rpx 8rpx rgba(118, 75, 162, 0.1),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.8);
  border: 1rpx solid rgba(255, 255, 255, 0.6);
  position: relative;
}

.info-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4rpx;
  background: linear-gradient(90deg, #667eea, #764ba2, #f093fb, #f5576c);
  background-size: 300% 100%;
  animation: gradient-shift 3s ease infinite;
  border-radius: 24rpx 24rpx 0 0;
}

/* 作者信息 */
.author-info {
  display: flex;
  align-items: center;
  margin-bottom: 32rpx;
  padding-bottom: 24rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.1);
}

.author-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 24rpx;
  border: 2rpx solid rgba(102, 126, 234, 0.3);
}

.author-details {
  flex: 1;
}

.author-name {
  display: block;
  font-size: 32rpx;
  font-weight: 700;
  color: #1a1a1a;
  margin-bottom: 8rpx;
}

.create-time {
  font-size: 24rpx;
  color: #666666;
}

/* 统计信息 */
.stats-info {
  display: flex;
  justify-content: space-around;
  margin-bottom: 32rpx;
  padding-bottom: 24rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.1);
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-label {
  font-size: 24rpx;
  color: #666666;
  margin-bottom: 8rpx;
}

.stat-value {
  font-size: 32rpx;
  font-weight: 700;
  color: #1a1a1a;
}

/* 提示词区域 */
.prompt-section {
  margin-bottom: 32rpx;
  padding-bottom: 24rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.1);
}

.prompt-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.prompt-title {
  font-size: 28rpx;
  font-weight: 700;
  color: #1a1a1a;
}

.copy-btn {
  padding: 12rpx 24rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50rpx;
  transition: all 0.3s ease;
}

.copy-btn:active {
  transform: scale(0.95);
}

.copy-text {
  font-size: 24rpx;
  color: #ffffff;
  font-weight: 600;
}

.prompt-content {
  background: rgba(248, 250, 255, 0.8);
  border-radius: 16rpx;
  padding: 24rpx;
  border: 1rpx solid rgba(102, 126, 234, 0.2);
}

.prompt-text {
  font-size: 28rpx;
  color: #1a1a1a;
  line-height: 1.6;
  word-break: break-all;
}

/* 技术信息 */
.tech-info {
  margin-bottom: 0;
}

.tech-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.tech-item:last-child {
  margin-bottom: 0;
}

.tech-label {
  font-size: 28rpx;
  color: #666666;
}

.tech-value {
  font-size: 28rpx;
  font-weight: 600;
  color: #1a1a1a;
}

/* Instagram风格操作按钮 */
.action-section {
  display: flex;
  gap: 32rpx;
  padding: 0 32rpx 40rpx 32rpx;
}

.action-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
  padding: 28rpx 40rpx;
  border-radius: 60rpx;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  box-shadow:
    0 8rpx 32rpx rgba(102, 126, 234, 0.25),
    0 4rpx 16rpx rgba(118, 75, 162, 0.15),
    inset 0 2rpx 0 rgba(255, 255, 255, 0.3);
  position: relative;
  overflow: hidden;
  border: 2rpx solid rgba(255, 255, 255, 0.2);
}

.action-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: left 0.8s ease;
}

.action-btn:active {
  transform: translateY(-4rpx) scale(0.96);
  box-shadow:
    0 12rpx 40rpx rgba(102, 126, 234, 0.35),
    0 6rpx 20rpx rgba(118, 75, 162, 0.25),
    inset 0 2rpx 0 rgba(255, 255, 255, 0.4);
}

.action-btn:active::before {
  left: 100%;
}

/* 点赞按钮 */
.like-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
}

.like-btn.liked {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 50%, #ff4757 100%);
  animation: likeAnimation 0.6s ease;
}

@keyframes likeAnimation {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

/* 分享按钮 */
.share-btn {
  background: linear-gradient(135deg, #20c997 0%, #17a2b8 50%, #007bff 100%);
}

/* 按钮图标 */
.action-icon {
  font-size: 36rpx;
  position: relative;
  z-index: 1;
  transition: transform 0.3s ease;
}

.action-btn:active .action-icon {
  transform: scale(1.2);
}

.like-btn.liked .action-icon {
  animation: heartBeat 0.8s ease;
}

@keyframes heartBeat {
  0% { transform: scale(1); }
  25% { transform: scale(1.3); }
  50% { transform: scale(1.1); }
  75% { transform: scale(1.25); }
  100% { transform: scale(1); }
}

/* 按钮文字 */
.action-text {
  font-size: 30rpx;
  color: #ffffff;
  font-weight: 800;
  text-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.3);
  position: relative;
  z-index: 1;
  letter-spacing: 1rpx;
  transition: all 0.3s ease;
}

.action-btn:active .action-text {
  transform: translateY(-2rpx);
}

/* 错误状态 */
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 200rpx 40rpx;
}

.error-text {
  font-size: 28rpx;
  color: #ff4757;
  margin-bottom: 32rpx;
  text-align: center;
}

.retry-btn {
  padding: 24rpx 48rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50rpx;
  transition: all 0.3s ease;
}

.retry-btn:active {
  transform: scale(0.95);
}

.retry-text {
  font-size: 28rpx;
  color: #ffffff;
  font-weight: 600;
}

/* 全局动画 */
@keyframes gradient-shift {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

/* 测试按钮样式 */
.test-btn {
  background: #ff4444 !important;
  color: white !important;
  border: none !important;
  font-size: 24rpx !important;
}

/* 作品展厅提示样式 */
.my-work-tip {
  text-align: center;
  padding: 32rpx;
  margin: 32rpx;
  background: rgba(102, 126, 234, 0.1);
  border-radius: 16rpx;
  border: 2rpx dashed rgba(102, 126, 234, 0.3);
}

.tip-text {
  font-size: 28rpx;
  color: #667eea;
  font-weight: 500;
}




