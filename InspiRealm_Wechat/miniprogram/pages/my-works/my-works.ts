// pages/my-works/my-works.ts
import { user<PERSON><PERSON>, errorHandler, utils } from '../../utils/api'

interface MyWork {
  id: number
  imageUrl: string
  thumbnailUrl?: string
  title?: string
  aspectRatio: string
  processType: string
  viewCount: number
  likeCount: number
  downloadCount: number
  status: string // 'pending' | 'approved' | 'rejected'
  isPublic: boolean
  createTime: string
}

Page({
  data: {
    works: [] as MyWork[],
    loading: false,
    hasMore: true,
    page: 1,
    pageSize: 10,
    total: 0,
    
    // 筛选选项已移除，现在显示所有作品
    
    // UI状态
    showFilterPanel: false,
    refreshing: false
  },

  onLoad() {
    this.loadMyWorks(true)
  },

  onShow() {
    // 更新底部导航栏选中状态
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setData({
        selected: 2
      })
    }
  },

  onPullDownRefresh() {
    this.setData({ refreshing: true })
    this.loadMyWorks(true).finally(() => {
      this.setData({ refreshing: false })
      wx.stopPullDownRefresh()
    })
  },

  onReachBottom() {
    if (this.data.hasMore && !this.data.loading) {
      this.loadMyWorks(false)
    }
  },

  /**
   * 加载我的作品列表
   */
  async loadMyWorks(reset: boolean = false) {
    if (this.data.loading) return

    this.setData({ loading: true })

    try {
      const page = reset ? 1 : this.data.page + 1
      const params = {
        page,
        size: this.data.pageSize
      }

      const response = await userApi.getMyWorks(params)

      // 格式化时间和数字
      const formattedWorks = response.data.records.map(work => ({
        ...work,
        createTime: utils.formatTime(work.createTime),
        viewCount: utils.formatNumber(work.viewCount),
        likeCount: utils.formatNumber(work.likeCount)
      }))

      this.setData({
        works: reset ? formattedWorks : [...this.data.works, ...formattedWorks],
        page,
        total: response.data.total,
        hasMore: page * this.data.pageSize < response.data.total,
        loading: false
      })

    } catch (error) {
      console.error('加载我的作品失败:', error)
      this.setData({ loading: false })
      
      if (reset && this.data.works.length === 0) {
        errorHandler.showError(error as Error, '加载失败')
      }
    }
  },

  /**
   * 处理图片详情点击 - 跳转到详情页面
   */
  onImageDetailTap(e: any) {
    const { id } = e.currentTarget.dataset
    console.log('🔍 点击图片跳转详情，imageId:', id)

    // 跳转到详情页面，添加来源参数表示来自作品展厅
    wx.navigateTo({
      url: `/pages/image-detail/image-detail?id=${id}&from=my-works`
    })
  },

  /**
   * 处理作品点击
   */
  onWorkTap(e: any) {
    const { id } = e.currentTarget.dataset
    wx.navigateTo({
      url: `/pages/work-detail/work-detail?id=${id}`
    })
  },

  /**
   * 切换筛选面板
   */
  toggleFilterPanel() {
    this.setData({
      showFilterPanel: !this.data.showFilterPanel
    })
  },



  /**
   * 关闭筛选面板
   */
  closeFilterPanel() {
    this.setData({
      showFilterPanel: false
    })
  },

  /**
   * 获取状态样式
   */
  getStatusStyle(status: string) {
    const statusMap = {
      'pending': { text: '审核中', class: 'status-pending', icon: '⏳' },
      'approved': { text: '已通过', class: 'status-approved', icon: '✅' },
      'rejected': { text: '已拒绝', class: 'status-rejected', icon: '❌' }
    }
    return statusMap[status] || { text: '未知', class: 'status-unknown', icon: '❓' }
  },

  /**
   * 删除作品
   */
  onDeleteWork(e: any) {
    const { id, index } = e.currentTarget.dataset
    
    wx.showModal({
      title: '确认删除',
      content: '删除后无法恢复，确定要删除这个作品吗？',
      success: async (res) => {
        if (res.confirm) {
          try {
            await userApi.deleteWork(id)

            // 从列表中移除
            const works = [...this.data.works]
            works.splice(index, 1)
            this.setData({ works })

            wx.showToast({
              title: '删除成功',
              icon: 'success'
            })
          } catch (error) {
            console.error('删除作品失败:', error)
            wx.showToast({
              title: '删除失败',
              icon: 'none'
            })
          }
        }
      }
    })
  },



  /**
   * 长按保存图片
   */
  onImageLongPress(e: any) {
    const { url } = e.currentTarget.dataset

    wx.showActionSheet({
      itemList: ['保存图片'],
      success: (res) => {
        if (res.tapIndex === 0) {
          this.saveImageToAlbum(url)
        }
      }
    })
  },

  /**
   * 保存图片到相册
   */
  saveImageToAlbum(imageUrl: string) {
    wx.showLoading({
      title: '保存中...'
    })

    wx.downloadFile({
      url: imageUrl,
      success: (res) => {
        if (res.statusCode === 200) {
          wx.saveImageToPhotosAlbum({
            filePath: res.tempFilePath,
            success: () => {
              wx.hideLoading()
              wx.showToast({
                title: '保存成功',
                icon: 'success'
              })
            },
            fail: (error) => {
              wx.hideLoading()
              if (error.errMsg.includes('auth deny')) {
                wx.showModal({
                  title: '需要相册权限',
                  content: '请在设置中开启相册权限',
                  confirmText: '去设置',
                  success: (res) => {
                    if (res.confirm) {
                      wx.openSetting()
                    }
                  }
                })
              } else {
                wx.showToast({
                  title: '保存失败',
                  icon: 'none'
                })
              }
            }
          })
        } else {
          wx.hideLoading()
          wx.showToast({
            title: '下载失败',
            icon: 'none'
          })
        }
      },
      fail: () => {
        wx.hideLoading()
        wx.showToast({
          title: '下载失败',
          icon: 'none'
        })
      }
    })
  },

  /**
   * 跳转到创作页面
   */
  goToCreate() {
    wx.switchTab({
      url: '/pages/process/process'
    })
  }
})
