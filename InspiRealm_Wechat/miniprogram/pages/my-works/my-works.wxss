/* pages/my-works/my-works.wxss */

/* Instagram风格我的作品页面 */
.page-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
  background-attachment: fixed;
}

/* Instagram风格顶部导航 */
.header {
  position: sticky;
  top: 0;
  z-index: 100;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-bottom: none;
  box-shadow: 0 2rpx 20rpx rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 40rpx;
  height: 100rpx;
}

.header-left .page-title {
  font-size: 40rpx;
  font-weight: 800;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  letter-spacing: 1rpx;
}

.header-right {
  display: flex;
  align-items: center;
}

.filter-trigger {
  padding: 16rpx 24rpx;
  background: rgba(102, 126, 234, 0.1);
  border-radius: 50rpx;
  transition: all 0.3s ease;
}

.filter-text {
  font-size: 28rpx;
  color: #667eea;
  font-weight: 600;
}

.filter-trigger:active {
  transform: scale(0.95);
  background: rgba(102, 126, 234, 0.2);
}

/* 筛选面板样式 */
.filter-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.4);
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.filter-overlay.active {
  opacity: 1;
  visibility: visible;
}

.filter-drawer {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #ffffff;
  border-radius: 24rpx 24rpx 0 0;
  max-height: 80vh;
  transform: translateY(100%);
  transition: transform 0.3s ease;
}

.filter-overlay.active .filter-drawer {
  transform: translateY(0);
}

.filter-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 32rpx 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.filter-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1a1a1a;
}

.filter-close {
  padding: 8rpx 16rpx;
}

.close-text {
  font-size: 28rpx;
  color: #007aff;
  font-weight: 500;
}

.filter-body {
  padding: 0 32rpx 32rpx;
  max-height: 60vh;
  overflow-y: auto;
}

.filter-tip {
  text-align: center;
  padding: 40rpx 20rpx;
}

.tip-text {
  font-size: 28rpx;
  color: #666666;
  line-height: 1.5;
}

.filter-group {
  margin-bottom: 48rpx;
}

.filter-group:last-child {
  margin-bottom: 0;
}

.group-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #1a1a1a;
  display: block;
  margin-bottom: 24rpx;
}

.option-list {
  display: flex;
  flex-direction: column;
  gap: 0;
}

.option-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
  transition: background-color 0.2s ease;
}

.option-item:last-child {
  border-bottom: none;
}

.option-item.selected {
  background-color: #f8f9ff;
}

.option-text {
  font-size: 28rpx;
  color: #333333;
}

.option-item.selected .option-text {
  color: #007aff;
  font-weight: 500;
}

.option-check {
  width: 40rpx;
  height: 40rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 50%;
  position: relative;
  transition: all 0.2s ease;
}

.option-check.checked {
  border-color: #007aff;
  background-color: #007aff;
}

.option-check.checked::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 12rpx;
  height: 12rpx;
  background-color: #ffffff;
  border-radius: 50%;
}

/* 内容区域 */
.content {
  padding: 24rpx 32rpx 120rpx;
}

/* 作品网格 */
.works-grid {
  display: flex;
  flex-direction: column;
  gap: 32rpx;
}

/* Instagram风格作品卡片 - 渐变玻璃效果 */
.work-item {
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.9) 0%,
    rgba(255, 255, 255, 0.8) 50%,
    rgba(248, 250, 255, 0.85) 100%);
  backdrop-filter: blur(20rpx);
  border-radius: 24rpx;
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  box-shadow:
    0 8rpx 32rpx rgba(102, 126, 234, 0.2),
    0 2rpx 8rpx rgba(118, 75, 162, 0.1),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.8);
  border: 1rpx solid rgba(255, 255, 255, 0.6);
  margin: 0 24rpx 32rpx 24rpx;
  position: relative;
}

.work-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4rpx;
  background: linear-gradient(90deg, #667eea, #764ba2, #f093fb, #f5576c);
  background-size: 300% 100%;
  animation: gradient-shift 3s ease infinite;
  z-index: 1;
}

.work-item:active {
  transform: translateY(-4rpx) scale(0.98);
  box-shadow:
    0 12rpx 40rpx rgba(102, 126, 234, 0.2),
    0 4rpx 12rpx rgba(0, 0, 0, 0.15);
}

/* Instagram风格动画 */
@keyframes gradient-shift {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.work-item {
  animation: fadeInUp 0.6s ease forwards;
}

.work-item:nth-child(1) { animation-delay: 0.1s; }
.work-item:nth-child(2) { animation-delay: 0.2s; }
.work-item:nth-child(3) { animation-delay: 0.3s; }
.work-item:nth-child(4) { animation-delay: 0.4s; }
.work-item:nth-child(5) { animation-delay: 0.5s; }

/* 图片容器 */
.image-container {
  position: relative;
  width: 100%;
  height: 400rpx;
  background-color: #f5f5f5;
}

.work-image {
  width: 100%;
  height: 100%;
  display: block;
  object-fit: cover;
  /* 确保图片高清显示 */
  image-rendering: -webkit-optimize-contrast;
  image-rendering: crisp-edges;
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
}



/* 作品信息 */
.work-info {
  padding: 24rpx;
}

.work-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16rpx;
}

.work-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1a1a1a;
  line-height: 1.4;
  flex: 1;
  margin-right: 16rpx;
}

.work-badges {
  display: flex;
  gap: 8rpx;
  flex-shrink: 0;
}

/* Instagram风格状态标签 */
.status-badge {
  padding: 8rpx 16rpx;
  border-radius: 50rpx;
  font-size: 22rpx;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 6rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  border: 1rpx solid rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10rpx);
}

.status-pending {
  background: linear-gradient(135deg, #ffc107 0%, #ff8f00 50%, #ffb300 100%);
  color: #ffffff;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
  box-shadow:
    0 4rpx 12rpx rgba(255, 193, 7, 0.3),
    0 2rpx 6rpx rgba(255, 143, 0, 0.2);
}

.status-pending::before {
  content: '⏳';
  font-size: 20rpx;
  filter: drop-shadow(0 1rpx 2rpx rgba(0, 0, 0, 0.1));
}

.status-approved {
  background: linear-gradient(135deg, #28a745 0%, #20c997 50%, #17a2b8 100%);
  color: #ffffff;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
  box-shadow:
    0 4rpx 12rpx rgba(40, 167, 69, 0.3),
    0 2rpx 6rpx rgba(32, 201, 151, 0.2);
}

.status-approved::before {
  content: '✅';
  font-size: 20rpx;
  filter: drop-shadow(0 1rpx 2rpx rgba(0, 0, 0, 0.1));
}

.status-rejected {
  background: linear-gradient(135deg, #dc3545 0%, #e74c3c 50%, #c0392b 100%);
  color: #ffffff;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
  box-shadow:
    0 4rpx 12rpx rgba(220, 53, 69, 0.3),
    0 2rpx 6rpx rgba(231, 76, 60, 0.2);
}

.status-rejected::before {
  content: '❌';
  font-size: 20rpx;
  filter: drop-shadow(0 1rpx 2rpx rgba(0, 0, 0, 0.1));
}

.status-text {
  font-size: 22rpx;
  font-weight: 600;
  letter-spacing: 0.5rpx;
}

/* Instagram风格公开状态标签 */
.public-badge {
  padding: 8rpx 16rpx;
  border-radius: 50rpx;
  font-size: 22rpx;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 6rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  border: 1rpx solid rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10rpx);
  transition: all 0.3s ease;
}

.public-badge.public {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
  color: #ffffff;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
  box-shadow:
    0 4rpx 12rpx rgba(102, 126, 234, 0.3),
    0 2rpx 6rpx rgba(118, 75, 162, 0.2);
}

.public-badge.public::before {
  content: '🌐';
  font-size: 20rpx;
  filter: drop-shadow(0 1rpx 2rpx rgba(0, 0, 0, 0.1));
}

.public-badge.private {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 50%, #ff4757 100%);
  color: #ffffff;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
  box-shadow:
    0 4rpx 12rpx rgba(255, 107, 107, 0.3),
    0 2rpx 6rpx rgba(238, 90, 82, 0.2);
}

.public-badge.private::before {
  content: '🔒';
  font-size: 20rpx;
  filter: drop-shadow(0 1rpx 2rpx rgba(0, 0, 0, 0.1));
}

.public-text {
  font-size: 22rpx;
  font-weight: 600;
  letter-spacing: 0.5rpx;
}

.work-meta {
  display: flex;
  gap: 32rpx;
  margin-bottom: 16rpx;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.meta-label {
  font-size: 24rpx;
  color: #999999;
}

.meta-value {
  font-size: 24rpx;
  color: #666666;
  font-weight: 500;
}

.work-stats {
  display: flex;
  gap: 32rpx;
  margin-bottom: 16rpx;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-number {
  font-size: 28rpx;
  font-weight: 600;
  color: #1a1a1a;
  line-height: 1.2;
}

.stat-label {
  font-size: 22rpx;
  color: #999999;
  margin-top: 4rpx;
}



/* Instagram风格操作按钮 */
.work-actions {
  display: flex;
  justify-content: center;
  gap: 20rpx;
  margin-top: 24rpx;
}

.action-btn {
  flex: 1;
  padding: 24rpx 32rpx;
  border-radius: 50rpx;
  text-align: center;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  box-shadow:
    0 4rpx 16rpx rgba(102, 126, 234, 0.2),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.8);
  border: none;
  position: relative;
  overflow: hidden;
}

.action-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: left 0.5s ease;
}

.action-btn:active {
  transform: translateY(-2rpx) scale(0.98);
  box-shadow:
    0 6rpx 20rpx rgba(102, 126, 234, 0.3),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.9);
}

.action-btn:active::before {
  left: 100%;
}

/* 公开状态按钮 - 绿色系 */
.public-btn {
  background: linear-gradient(135deg, #4CAF50 0%, #45a049 50%, #66BB6A 100%);
  border: none;
  box-shadow:
    0 8rpx 25rpx rgba(76, 175, 80, 0.4),
    0 4rpx 12rpx rgba(69, 160, 73, 0.3),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.4);
  position: relative;
  overflow: hidden;
}

.public-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.6s ease;
}

.public-btn:active::before {
  left: 100%;
}

/* 私密状态按钮 - 橙色系 */
.private-btn {
  background: linear-gradient(135deg, #FF9800 0%, #F57C00 50%, #FFB74D 100%);
  border: none;
  box-shadow:
    0 8rpx 25rpx rgba(255, 152, 0, 0.4),
    0 4rpx 12rpx rgba(245, 124, 0, 0.3),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.4);
  position: relative;
  overflow: hidden;
}

.private-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.6s ease;
}

.private-btn:active::before {
  left: 100%;
}

/* 通用按钮文字样式 */
.toggle-btn .action-icon {
  font-size: 32rpx;
  margin-right: 8rpx;
}

.toggle-btn .action-text {
  color: #ffffff;
  font-size: 28rpx;
  font-weight: 800;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
  position: relative;
  z-index: 1;
}

.delete-btn {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 50%, #ff4757 100%);
  border: none;
  box-shadow:
    0 8rpx 25rpx rgba(255, 107, 107, 0.4),
    0 4rpx 12rpx rgba(238, 90, 82, 0.3),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.4);
  position: relative;
  overflow: hidden;
}

.delete-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.6s ease;
}

.delete-btn:active::before {
  left: 100%;
}

.delete-btn .action-text {
  color: #ffffff;
  font-size: 28rpx;
  font-weight: 800;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
  position: relative;
  z-index: 1;
}

/* 加载状态 */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 80rpx 0;
}

.loading-dot {
  width: 48rpx;
  height: 48rpx;
  border: 3rpx solid #f0f0f0;
  border-top: 3rpx solid #007aff;
  border-radius: 50%;
  animation: loading-spin 1s linear infinite;
}

@keyframes loading-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-tip {
  font-size: 26rpx;
  color: #999999;
  margin-top: 24rpx;
}

/* 结束提示 */
.end-tip {
  text-align: center;
  padding: 60rpx 0;
}

.end-text {
  font-size: 26rpx;
  color: #cccccc;
}

/* 空状态 */
.empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 160rpx 0;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 32rpx;
  opacity: 0.3;
}

.empty-title {
  font-size: 34rpx;
  color: #1a1a1a;
  font-weight: 700;
  margin-bottom: 16rpx;
  
}

.empty-desc {
  font-size: 28rpx;
  color: #333333;
  text-align: center;
  line-height: 1.5;
  margin-bottom: 48rpx;
  
}

.empty-action {
  
}

.create-btn {
  padding: 28rpx 56rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
  color: #ffffff;
  border-radius: 50rpx;
  font-size: 30rpx;
  font-weight: 800;
  border: none;
  box-shadow:
    0 8rpx 25rpx rgba(102, 126, 234, 0.4),
    0 4rpx 12rpx rgba(118, 75, 162, 0.3),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.4);
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
  position: relative;
  overflow: hidden;
}

.create-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.6s ease;
}

.create-btn:active {
  transform: translateY(-2rpx) scale(0.98);
  box-shadow:
    0 12rpx 30rpx rgba(102, 126, 234, 0.5),
    0 6rpx 15rpx rgba(118, 75, 162, 0.4),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.5);
}

.create-btn:active::before {
  left: 100%;
}

.create-btn::after {
  border: none;
}
