<!--pages/my-works/my-works.wxml-->
<view class="page-container">
  <!-- 顶部导航栏 -->
  <view class="header">
    <view class="header-content">
      <view class="header-left">
        <text class="page-title">作品展厅</text>
      </view>
      <view class="header-right">
        <view class="filter-trigger" bindtap="toggleFilterPanel">
          <text class="filter-text">筛选</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 筛选面板 -->
  <view class="filter-overlay {{showFilterPanel ? 'active' : ''}}" bindtap="closeFilterPanel">
    <view class="filter-drawer" catchtap="">
      <view class="filter-header">
        <text class="filter-title">筛选条件</text>
        <view class="filter-close" bindtap="closeFilterPanel">
          <text class="close-text">完成</text>
        </view>
      </view>
      
      <view class="filter-body">
        <view class="filter-tip">
          <text class="tip-text">这里是您的所有作品，按创建时间排序</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 内容区域 -->
  <view class="content">
    <!-- 作品网格 -->
    <view class="works-grid">
      <view 
        wx:for="{{works}}" 
        wx:key="id"
        class="work-item"
        data-id="{{item.id}}"
        bindtap="onWorkTap"
      >
        <!-- 图片容器 -->
        <view class="image-container">
          <image
            class="work-image"
            src="{{item.imageUrl}}"
            mode="aspectFill"
            lazy-load="{{true}}"
            data-id="{{item.id}}"
            data-url="{{item.imageUrl}}"
            bindtap="onImageDetailTap"
            bindlongpress="onImageLongPress"
            show-menu-by-longpress="{{true}}"
          />
        </view>

        <!-- 作品信息 -->
        <view class="work-info">
          <view class="work-header">
            <view class="work-title" wx:if="{{item.title}}">{{item.title}}</view>
            <view class="work-badges">
              <!-- 只显示公开状态标签 -->
              <view class="public-badge {{item.isPublic ? 'public' : 'private'}}">
                <text class="public-text">{{item.isPublic ? '公开' : '私密'}}</text>
              </view>
            </view>
          </view>

          <view class="work-meta">
            <view class="meta-item">
              <text class="meta-label">类型</text>
              <text class="meta-value">{{item.processType}}</text>
            </view>
            <view class="meta-item">
              <text class="meta-label">比例</text>
              <text class="meta-value">{{item.aspectRatio}}</text>
            </view>
            <view class="meta-item">
              <text class="meta-label">创建时间</text>
              <text class="meta-value">{{item.createTime}}</text>
            </view>
          </view>

          <view class="work-stats">
            <view class="stat-item">
              <text class="stat-number">{{item.viewCount}}</text>
              <text class="stat-label">浏览</text>
            </view>
            <view class="stat-item">
              <text class="stat-number">{{item.likeCount}}</text>
              <text class="stat-label">点赞</text>
            </view>
            <view class="stat-item">
              <text class="stat-number">{{item.downloadCount}}</text>
              <text class="stat-label">下载</text>
            </view>
          </view>
          
          <!-- 操作按钮 -->
          <view class="work-actions">
            <view
              class="action-btn delete-btn"
              data-id="{{item.id}}"
              data-index="{{index}}"
              catchtap="onDeleteWork"
            >
              <text class="action-text">删除作品</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 加载状态 -->
    <view wx:if="{{loading}}" class="loading-state">
      <view class="loading-dot"></view>
      <text class="loading-tip">加载中</text>
    </view>

    <!-- 没有更多 -->
    <view wx:if="{{!hasMore && works.length > 0}}" class="end-tip">
      <text class="end-text">没有更多内容了</text>
    </view>

    <!-- 空状态 -->
    <view wx:if="{{works.length === 0 && !loading}}" class="empty">
      <view class="empty-icon">🎨</view>
      <text class="empty-title">还没有作品</text>
      <text class="empty-desc">快去创作第一个作品吧</text>
      <view class="empty-action">
        <button class="create-btn" bindtap="goToCreate">
          开始创作
        </button>
      </view>
    </view>
  </view>
</view>
