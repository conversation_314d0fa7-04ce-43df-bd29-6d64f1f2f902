<!--pages/help/help.wxml-->
<view class="page-container">
  <!-- 顶部导航栏 -->
  <view class="header">
    <view class="header-content">
      <view class="header-left">
        <text class="page-title">小助手</text>
      </view>
    </view>
  </view>

  <!-- 内容区域 -->
  <view class="content">
    <!-- 帮助分类 -->
    <view class="help-section">
      <view class="section-header">
        <text class="section-title">使用帮助</text>
      </view>
      
      <view class="categories-list">
        <view 
          wx:for="{{helpCategories}}" 
          wx:key="id"
          class="category-item"
        >
          <view 
            class="category-header"
            data-id="{{item.id}}"
            bindtap="toggleCategory"
          >
            <view class="category-left">
              <text class="category-icon">{{item.icon}}</text>
              <view class="category-info">
                <text class="category-title">{{item.title}}</text>
                <text class="category-desc">{{item.desc}}</text>
              </view>
            </view>
            <view class="category-arrow {{expandedCategory === item.id ? 'expanded' : ''}}">
              <text class="arrow-icon">›</text>
            </view>
          </view>
          
          <view class="category-content {{expandedCategory === item.id ? 'expanded' : ''}}">
            <view 
              wx:for="{{item.items}}" 
              wx:key="id"
              wx:for-item="helpItem"
              class="help-item"
            >
              <text class="help-question">{{helpItem.title}}</text>
              <text class="help-answer">{{helpItem.content}}</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 常见问题 -->
    <view class="faq-section">
      <view class="section-header">
        <text class="section-title">常见问题</text>
      </view>
      
      <view class="faq-list">
        <view 
          wx:for="{{faqList}}" 
          wx:key="id"
          class="faq-item"
          data-id="{{item.id}}"
          bindtap="toggleFaq"
        >
          <view class="faq-question">
            <text class="question-text">{{item.question}}</text>
            <view class="faq-arrow {{expandedFaq === item.id ? 'expanded' : ''}}">
              <text class="arrow-icon">›</text>
            </view>
          </view>
          
          <view class="faq-answer {{expandedFaq === item.id ? 'expanded' : ''}}">
            <text class="answer-text">{{item.answer}}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 联系我们 -->
    <view class="contact-section">
      <view class="section-header">
        <text class="section-title">联系我们</text>
      </view>
      
      <view class="contact-list">
        <view class="contact-item" data-type="邮箱" data-value="{{contactInfo.email}}" bindtap="copyContact">
          <text class="contact-icon">📧</text>
          <view class="contact-info">
            <text class="contact-label">邮箱</text>
            <text class="contact-value">{{contactInfo.email}}</text>
          </view>
          <text class="copy-hint">点击复制</text>
        </view>
      </view>
    </view>

    <!-- 法律条款 -->
    <view class="legal-section">
      <view class="legal-links">
        <text class="legal-link" bindtap="goToUserAgreement">用户协议</text>
        <text class="legal-separator">|</text>
        <text class="legal-link" bindtap="goToPrivacyPolicy">隐私政策</text>
      </view>
    </view>
  </view>
</view>
