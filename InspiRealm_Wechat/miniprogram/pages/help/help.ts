// pages/help/help.ts
Page({
  data: {
    // 帮助分类
    helpCategories: [
      {
        id: 'getting-started',
        title: '新手入门',
        icon: '🚀',
        desc: '快速了解如何使用假装会修图',
        items: [
          {
            id: 'how-to-register',
            title: '如何注册登录？',
            content: '点击个人中心的"微信登录"按钮，授权后即可完成注册登录。'
          },
          {
            id: 'how-to-process',
            title: '如何处理图片？',
            content: '进入"创作"页面，选择图片和处理类型，点击开始处理即可。'
          },
          {
            id: 'how-to-share',
            title: '如何分享作品？',
            content: '处理完成后，可以选择保存到相册或分享到广场供其他用户欣赏。'
          }
        ]
      },
      {
        id: 'features',
        title: '功能介绍',
        icon: '✨',
        desc: '了解各种AI处理功能',
        items: [
          {
            id: 'beautify',
            title: '图片美化功能',
            content: '自动优化图片的色彩、对比度和清晰度，让照片更加美观。'
          },
          {
            id: 'style-transfer',
            title: '风格转换功能',
            content: '将图片转换为不同的艺术风格，如油画、水彩、素描等。'
          },
          {
            id: 'cartoon',
            title: '卡通化功能',
            content: '将人像照片转换为卡通风格，支持多种卡通化效果。'
          }
        ]
      },
      {
        id: 'points',
        title: '积分系统',
        icon: '💰',
        desc: '了解积分获取和使用规则',
        items: [
          {
            id: 'earn-points',
            title: '如何获得积分？',
            content: '首次注册可获得积分奖励，每日签到可获得50积分。'
          },
          {
            id: 'use-points',
            title: '积分有什么用？',
            content: '积分用于生成图片，每次生成消耗10积分。'
          },
          {
            id: 'points-rules',
            title: '积分规则说明',
            content: '积分没有有效期，永久有效。生成图片每次消耗10积分。'
          }
        ]
      }
    ],
    
    // 常见问题
    faqList: [
      {
        id: 'processing-failed',
        question: '图片处理失败怎么办？',
        answer: '请检查网络连接，确保图片格式正确（支持JPG、PNG），图片大小不超过10MB。如问题持续，请联系客服。'
      },
      {
        id: 'points-missing',
        question: '积分突然减少了？',
        answer: '积分因为生成图片而消耗，每次生成消耗10积分。详细记录可在积分记录页面查看。'
      },
      {
        id: 'image-quality',
        question: '处理后的图片质量不好？',
        answer: '建议上传高质量的原图，在设置中开启"高质量模式"，选择合适的处理参数。'
      }
    ],
    
    // 联系方式
    contactInfo: {
      email: '<EMAIL>'
    },
    
    // 当前展开的分类
    expandedCategory: '',
    expandedFaq: ''
  },

  onLoad() {
    
  },

  /**
   * 切换帮助分类展开状态
   */
  toggleCategory(e: any) {
    const { id } = e.currentTarget.dataset
    const expandedCategory = this.data.expandedCategory === id ? '' : id
    this.setData({ expandedCategory })
  },

  /**
   * 切换FAQ展开状态
   */
  toggleFaq(e: any) {
    const { id } = e.currentTarget.dataset
    const expandedFaq = this.data.expandedFaq === id ? '' : id
    this.setData({ expandedFaq })
  },

  /**
   * 复制联系方式
   */
  copyContact(e: any) {
    const { type, value } = e.currentTarget.dataset
    
    wx.setClipboardData({
      data: value,
      success: () => {
        wx.showToast({
          title: `${type}已复制`,
          icon: 'success'
        })
      }
    })
  },



  /**
   * 跳转到用户协议
   */
  goToUserAgreement() {
    wx.showModal({
      title: '用户协议',
      content: '《用户服务协议》\n\n1. 服务说明\n本应用提供图片处理服务，用户可上传图片进行各种图片处理。\n\n2. 用户责任\n用户应确保上传的图片内容合法，不侵犯他人权益。\n\n3. 知识产权免责\n用户生成的图片如涉及侵权问题，平台不承担任何法律责任。用户应自行承担因上传内容或生成图片而产生的一切法律后果。\n\n4. 服务限制\n平台有权对违规内容进行处理，包括但不限于删除、限制访问等。\n\n5. 免责声明\n平台仅提供技术服务，不对用户生成内容的合法性、准确性承担责任。',
      showCancel: false,
      confirmText: '确定'
    })
  },

  /**
   * 跳转到隐私政策
   */
  goToPrivacyPolicy() {
    wx.showModal({
      title: '隐私政策',
      content: '《隐私保护政策》\n\n1. 信息收集\n我们仅收集必要的用户信息，包括微信授权信息和上传的图片数据。\n\n2. 信息使用\n收集的信息仅用于提供图片处理服务，不会用于其他商业目的。\n\n3. 信息安全\n我们采用行业标准的安全措施保护用户数据，但不能保证绝对安全。\n\n4. 信息共享\n除法律要求外，我们不会向第三方分享用户个人信息。\n\n5. 用户权利\n用户有权查看、修改或删除个人信息。\n\n6. 免责声明\n用户上传的图片内容及生成的图片如涉及侵权，平台不承担责任。用户应确保内容合法合规。',
      showCancel: false,
      confirmText: '确定'
    })
  }
})
