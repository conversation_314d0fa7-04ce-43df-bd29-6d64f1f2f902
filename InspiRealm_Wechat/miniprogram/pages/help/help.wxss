/* pages/help/help.wxss */

/* Instagram风格帮助页面 */
.page-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
  background-attachment: fixed;
}

/* Instagram风格顶部导航 */
.header {
  position: sticky;
  top: 0;
  z-index: 100;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-bottom: none;
  box-shadow: 0 2rpx 20rpx rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 40rpx;
  height: 100rpx;
}

.header-left .page-title {
  font-size: 40rpx;
  font-weight: 700;
  background: linear-gradient(45deg, #667eea, #764ba2, #f093fb);
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  letter-spacing: 1rpx;
}

/* 内容区域 */
.content {
  padding: 24rpx 32rpx 120rpx;
}

/* 通用区块样式 */
.help-section,
.faq-section,
.contact-section,
.feedback-section {
  margin-bottom: 48rpx;
}

.section-header {
  margin-bottom: 24rpx;
  display: flex;
  justify-content: flex-start;
}

.section-title {
  font-size: 36rpx;
  font-weight: 700;
  color: #ffffff;
  letter-spacing: 0.5rpx;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
  background: rgba(255, 255, 255, 0.1);
  padding: 16rpx 24rpx;
  border-radius: 12rpx;
  backdrop-filter: blur(10rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}

/* 帮助分类 */
.categories-list {
  background-color: #ffffff;
  border-radius: 16rpx;
  overflow: hidden;
}

.category-item {
  border-bottom: 1rpx solid #f5f5f5;
}

.category-item:last-child {
  border-bottom: none;
}

.category-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  transition: background-color 0.2s ease;
}

.category-header:active {
  background-color: #f8f9ff;
}

.category-left {
  display: flex;
  align-items: center;
  flex: 1;
}

.category-icon {
  font-size: 40rpx;
  margin-right: 24rpx;
}

.category-info {
  flex: 1;
}

.category-title {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 4rpx;
}

/* Instagram风格全局动画 */
@keyframes gradient-shift {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.category-desc {
  display: block;
  font-size: 24rpx;
  color: #999999;
  line-height: 1.4;
}

.category-arrow {
  width: 32rpx;
  height: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform 0.3s ease;
}

.category-arrow.expanded {
  transform: rotate(90deg);
}

.arrow-icon {
  font-size: 24rpx;
  color: #cccccc;
  font-weight: 300;
}

.category-content {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease;
}

.category-content.expanded {
  max-height: 1000rpx;
}

.help-item {
  padding: 24rpx 32rpx;
  border-top: 1rpx solid #f5f5f5;
}

.help-question {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  color: #333333;
  margin-bottom: 12rpx;
}

.help-answer {
  display: block;
  font-size: 26rpx;
  color: #666666;
  line-height: 1.5;
}

/* 常见问题 */
.faq-list {
  background-color: #ffffff;
  border-radius: 16rpx;
  overflow: hidden;
}

.faq-item {
  border-bottom: 1rpx solid #f5f5f5;
  transition: background-color 0.2s ease;
}

.faq-item:last-child {
  border-bottom: none;
}

.faq-item:active {
  background-color: #f8f9ff;
}

.faq-question {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
}

.question-text {
  flex: 1;
  font-size: 28rpx;
  font-weight: 500;
  color: #333333;
  line-height: 1.4;
}

.faq-arrow {
  width: 32rpx;
  height: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform 0.3s ease;
}

.faq-arrow.expanded {
  transform: rotate(90deg);
}

.faq-answer {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease;
}

.faq-answer.expanded {
  max-height: 500rpx;
}

.answer-text {
  display: block;
  padding: 0 32rpx 32rpx;
  font-size: 26rpx;
  color: #666666;
  line-height: 1.5;
}

/* 联系我们 */
.contact-list {
  background-color: #ffffff;
  border-radius: 16rpx;
  overflow: hidden;
}

.contact-item {
  display: flex;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid #f5f5f5;
  transition: background-color 0.2s ease;
}

.contact-item:last-child {
  border-bottom: none;
}

.contact-item:active {
  background-color: #f8f9ff;
}

.contact-icon {
  font-size: 40rpx;
  margin-right: 24rpx;
}

.contact-info {
  flex: 1;
}

.contact-label {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  color: #333333;
  margin-bottom: 4rpx;
}

.contact-value {
  display: block;
  font-size: 26rpx;
  color: #666666;
}

.copy-hint {
  font-size: 24rpx;
  color: #007aff;
}

/* 意见反馈 */
.feedback-form {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 32rpx;
}

.form-item {
  margin-bottom: 32rpx;
}

.form-item:last-child {
  margin-bottom: 0;
}

.form-label {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  color: #333333;
  margin-bottom: 16rpx;
}

.type-picker {
  
}

.picker-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
  border: 1rpx solid #e9ecef;
}

.picker-text {
  font-size: 28rpx;
  color: #333333;
}

.picker-arrow {
  font-size: 24rpx;
  color: #cccccc;
}

.feedback-textarea {
  width: 100%;
  min-height: 200rpx;
  padding: 24rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
  border: 1rpx solid #e9ecef;
  font-size: 28rpx;
  color: #333333;
  line-height: 1.5;
}

.contact-input {
  width: 100%;
  height: 80rpx;
  padding: 0 24rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
  border: 1rpx solid #e9ecef;
  font-size: 28rpx;
  color: #333333;
}

.submit-btn {
  width: 100%;
  height: 88rpx;
  background-color: #007aff;
  color: #ffffff;
  border-radius: 16rpx;
  font-size: 30rpx;
  font-weight: 500;
  border: none;
  margin-top: 32rpx;
}

.submit-btn:active {
  background-color: #0056cc;
  transform: scale(0.98);
}

.submit-btn::after {
  border: none;
}

/* 法律条款 */
.legal-section {
  margin-top: 48rpx;
}

.legal-links {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
}

.legal-link {
  font-size: 24rpx;
  color: #007aff;
  text-decoration: underline;
}

.legal-separator {
  font-size: 24rpx;
  color: #cccccc;
}
