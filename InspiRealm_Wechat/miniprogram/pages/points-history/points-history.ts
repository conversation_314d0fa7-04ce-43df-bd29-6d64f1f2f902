// pages/points-history/points-history.ts
import { userApi, errorHandler, utils } from '../../utils/api'

interface PointsRecord {
  id: number
  type: 'earn' | 'consume'
  amount: number
  reason: string
  description?: string
  createTime: string
  relatedId?: number
  relatedType?: string
}

Page({
  data: {
    records: [] as PointsRecord[],
    loading: false,
    hasMore: true,
    page: 1,
    pageSize: 20,
    total: 0,
    
    // 筛选条件
    activeType: '',

    // 筛选选项
    typeOptions: [
      { value: '', label: '全部记录' },
      { value: '1', label: '获得积分' },
      { value: '2', label: '消费积分' },
      { value: '3', label: '锁定积分' },
      { value: '4', label: '解锁积分' },
      { value: '5', label: '回退积分' }
    ],
    
    // 用户积分信息
    pointsInfo: {
      totalPoints: 0,
      availablePoints: 0,
      lockedPoints: 0,
      consumedPoints: 0
    },
    
    // UI状态
    showFilterPanel: false,
    refreshing: false
  },

  onLoad() {
    this.loadPointsInfo()
    this.loadPointsHistory(true)
  },

  onPullDownRefresh() {
    this.setData({ refreshing: true })
    Promise.all([
      this.loadPointsInfo(),
      this.loadPointsHistory(true)
    ]).finally(() => {
      this.setData({ refreshing: false })
      wx.stopPullDownRefresh()
    })
  },

  onReachBottom() {
    if (this.data.hasMore && !this.data.loading) {
      this.loadPointsHistory(false)
    }
  },

  /**
   * 加载积分信息
   */
  async loadPointsInfo() {
    try {
      const response = await userApi.getPointsInfo()
      // 根据API文档调整数据结构
      this.setData({
        pointsInfo: {
          availablePoints: response.data.availablePoints || 0,
          totalPoints: response.data.totalPoints || 0,
          consumedPoints: response.data.totalSpent || 0,
          lockedPoints: response.data.lockedPoints || 0
        }
      })
    } catch (error) {
      console.error('加载积分信息失败:', error)
    }
  },

  /**
   * 加载积分记录
   */
  async loadPointsHistory(reset: boolean = false) {
    if (this.data.loading) return

    this.setData({ loading: true })

    try {
      const page = reset ? 1 : this.data.page + 1
      const params = {
        page,
        size: this.data.pageSize,
        transactionType: this.data.activeType ? parseInt(this.data.activeType) : undefined
      }

      const response = await userApi.getPointsHistory(params)

      // 格式化时间和数据结构，适配前端显示
      const formattedRecords = response.data.records.map(record => {
        // 根据交易类型确定显示类型和金额符号
        // 1:获得 4:解锁 5:回退 显示为正数（绿色+号）
        // 2:消费 3:锁定 显示为负数（红色-号）
        const isPositive = record.transactionType === 1 || record.transactionType === 4 || record.transactionType === 5

        return {
          id: record.id,
          type: isPositive ? 'earn' : 'consume',
          amount: isPositive ? record.pointsAmount : -record.pointsAmount,
          reason: record.businessTypeDesc || record.transactionTypeDesc || '积分变动',
          description: record.description,
          createTime: utils.formatTime(record.createTime),
          relatedType: record.businessType,
          beforePoints: record.beforePoints,
          afterPoints: record.afterPoints,
          transactionType: record.transactionType,
          transactionTypeDesc: record.transactionTypeDesc
        }
      })

      // 调试信息：打印格式化后的记录
      console.log('格式化后的积分记录:', formattedRecords.map(r => ({
        type: r.type,
        amount: r.amount,
        transactionType: r.transactionType,
        reason: r.reason
      })))

      this.setData({
        records: reset ? formattedRecords : [...this.data.records, ...formattedRecords],
        page,
        total: response.data.total,
        hasMore: page * this.data.pageSize < response.data.total,
        loading: false
      })

    } catch (error) {
      console.error('加载积分记录失败:', error)
      this.setData({ loading: false })
      
      if (reset && this.data.records.length === 0) {
        errorHandler.showError(error as Error, '加载失败')
      }
    }
  },

  /**
   * 切换筛选面板
   */
  toggleFilterPanel() {
    this.setData({
      showFilterPanel: !this.data.showFilterPanel
    })
  },

  /**
   * 选择类型筛选
   */
  onTypeChange(e: any) {
    const { value } = e.currentTarget.dataset
    this.setData({
      activeType: value,
      showFilterPanel: false
    })
    this.loadPointsHistory(true)
  },

  /**
   * 关闭筛选面板
   */
  closeFilterPanel() {
    this.setData({
      showFilterPanel: false
    })
  },

  /**
   * 获取记录类型样式
   */
  getRecordTypeStyle(type: string) {
    return type === 'earn' ? {
      class: 'earn',
      icon: '+',
      color: '#10b981'
    } : {
      class: 'consume',
      icon: '-',
      color: '#ef4444'
    }
  },

  /**
   * 处理记录点击
   */
  onRecordTap(e: any) {
    const { record } = e.currentTarget.dataset
    
    if (record.relatedType && record.relatedId) {
      // 根据关联类型跳转到相应页面
      if (record.relatedType === 'image') {
        wx.navigateTo({
          url: `/pages/image-detail/image-detail?id=${record.relatedId}`
        })
      } else if (record.relatedType === 'process') {
        wx.showToast({
          title: '处理记录详情',
          icon: 'none'
        })
      }
    }
  },


})
