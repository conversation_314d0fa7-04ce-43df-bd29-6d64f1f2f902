<!--pages/points-history/points-history.wxml-->
<view class="page-container">
  <!-- 顶部导航栏 -->
  <view class="header">
    <view class="header-content">
      <view class="header-left">
        <text class="page-title">能量宝库</text>
      </view>
      <view class="header-right">
        <view class="filter-trigger" bindtap="toggleFilterPanel">
          <text class="filter-text">筛选</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 筛选面板 -->
  <view class="filter-overlay {{showFilterPanel ? 'active' : ''}}" bindtap="closeFilterPanel">
    <view class="filter-drawer" catchtap="">
      <view class="filter-header">
        <text class="filter-title">筛选条件</text>
        <view class="filter-close" bindtap="closeFilterPanel">
          <text class="close-text">完成</text>
        </view>
      </view>
      
      <view class="filter-body">
        <view class="filter-group">
          <text class="group-title">记录类型</text>
          <view class="option-list">
            <view 
              wx:for="{{typeOptions}}" 
              wx:key="value"
              class="option-item {{activeType === item.value ? 'selected' : ''}}"
              data-value="{{item.value}}"
              bindtap="onTypeChange"
            >
              <text class="option-text">{{item.label}}</text>
              <view class="option-check {{activeType === item.value ? 'checked' : ''}}"></view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 内容区域 -->
  <view class="content">
    <!-- 积分概览 -->
    <view class="points-overview">
      <view class="overview-header">
        <text class="overview-title">我的能量宝藏</text>
      </view>
      
      <view class="points-grid">
        <view class="points-item main">
          <text class="points-number">{{pointsInfo.availablePoints}}</text>
          <text class="points-label">可用能量</text>
        </view>
        <view class="points-item">
          <text class="points-number">{{pointsInfo.totalPoints}}</text>
          <text class="points-label">累计获得</text>
        </view>
        <view class="points-item">
          <text class="points-number">{{pointsInfo.consumedPoints}}</text>
          <text class="points-label">已消费</text>
        </view>
        <view class="points-item">
          <text class="points-number">{{pointsInfo.lockedPoints}}</text>
          <text class="points-label">冻结中</text>
        </view>
      </view>
      

    </view>

    <!-- 积分记录列表 -->
    <view class="records-section">
      <view class="section-header">
        <text class="section-title">能量流水</text>
      </view>
      
      <view class="records-list">
        <view 
          wx:for="{{records}}" 
          wx:key="id"
          class="record-item {{item.type}}"
          data-record="{{item}}"
          bindtap="onRecordTap"
        >
          <view class="record-left">
            <view class="record-icon {{item.type}}">
              <text class="icon-text">{{item.type === 'earn' ? '+' : '-'}}</text>
            </view>
            <view class="record-info">
              <text class="record-reason">{{item.reason}}</text>
              <text class="record-desc" wx:if="{{item.description}}">{{item.description}}</text>
              <text class="record-time">{{item.createTime}}</text>
            </view>
          </view>
          
          <view class="record-right">
            <text class="record-amount {{item.type}}">
              {{item.type === 'earn' ? '+' : ''}}{{item.amount}}
            </text>
            <view wx:if="{{item.relatedType}}" class="record-arrow">
              <text class="arrow-icon">›</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 加载状态 -->
    <view wx:if="{{loading}}" class="loading-state">
      <view class="loading-dot"></view>
      <text class="loading-tip">加载中</text>
    </view>

    <!-- 没有更多 -->
    <view wx:if="{{!hasMore && records.length > 0}}" class="end-tip">
      <text class="end-text">没有更多记录了</text>
    </view>

    <!-- 空状态 -->
    <view wx:if="{{records.length === 0 && !loading}}" class="empty">
      <view class="empty-icon">💰</view>
      <text class="empty-title">暂无积分记录</text>
      <text class="empty-desc">完成任务和创作作品可以获得积分</text>
    </view>
  </view>
</view>
