/* pages/points-history/points-history.wxss */

/* Instagram风格积分历史页面 */
.page-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
  background-attachment: fixed;
}

/* Instagram风格顶部导航 */
.header {
  position: sticky;
  top: 0;
  z-index: 100;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-bottom: none;
  box-shadow: 0 2rpx 20rpx rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 40rpx;
  height: 100rpx;
}

.header-left .page-title {
  font-size: 40rpx;
  font-weight: 800;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  letter-spacing: 1rpx;
  
}

.header-right {
  display: flex;
  align-items: center;
}

.filter-trigger {
  padding: 12rpx 0;
}

.filter-text {
  font-size: 28rpx;
  color: #666666;
  font-weight: 500;
}

.filter-trigger:active {
  opacity: 0.6;
}

/* 筛选面板样式 */
.filter-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.4);
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.filter-overlay.active {
  opacity: 1;
  visibility: visible;
}

.filter-drawer {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #ffffff;
  border-radius: 24rpx 24rpx 0 0;
  max-height: 80vh;
  transform: translateY(100%);
  transition: transform 0.3s ease;
}

.filter-overlay.active .filter-drawer {
  transform: translateY(0);
}

.filter-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 32rpx 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.filter-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1a1a1a;
}

.filter-close {
  padding: 8rpx 16rpx;
}

.close-text {
  font-size: 28rpx;
  color: #007aff;
  font-weight: 500;
}

.filter-body {
  padding: 0 32rpx 32rpx;
  max-height: 60vh;
  overflow-y: auto;
}

.filter-group {
  margin-bottom: 48rpx;
}

.group-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #1a1a1a;
  display: block;
  margin-bottom: 24rpx;
}

.option-list {
  display: flex;
  flex-direction: column;
  gap: 0;
}

.option-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
  transition: background-color 0.2s ease;
}

.option-item:last-child {
  border-bottom: none;
}

.option-item.selected {
  background-color: #f8f9ff;
}

.option-text {
  font-size: 28rpx;
  color: #333333;
}

.option-item.selected .option-text {
  color: #007aff;
  font-weight: 500;
}

.option-check {
  width: 40rpx;
  height: 40rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 50%;
  position: relative;
  transition: all 0.2s ease;
}

.option-check.checked {
  border-color: #007aff;
  background-color: #007aff;
}

.option-check.checked::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 12rpx;
  height: 12rpx;
  background-color: #ffffff;
  border-radius: 50%;
}

/* 内容区域 */
.content {
  padding: 24rpx 32rpx 120rpx;
}

/* Instagram风格积分概览 */
.points-overview {
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.9) 0%,
    rgba(255, 255, 255, 0.8) 50%,
    rgba(248, 250, 255, 0.85) 100%);
  backdrop-filter: blur(20rpx);
  border-radius: 24rpx;
  padding: 40rpx;
  margin: 24rpx 24rpx 32rpx 24rpx;
  box-shadow:
    0 8rpx 32rpx rgba(102, 126, 234, 0.2),
    0 2rpx 8rpx rgba(118, 75, 162, 0.1),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.8);
  border: 1rpx solid rgba(255, 255, 255, 0.6);
  position: relative;
}

.points-overview::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4rpx;
  background: linear-gradient(90deg, #667eea, #764ba2, #f093fb, #f5576c);
  background-size: 300% 100%;
  animation: gradient-shift 3s ease infinite;
  border-radius: 24rpx 24rpx 0 0;
}

/* Instagram风格全局动画 */
@keyframes gradient-shift {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.points-overview {
  animation: fadeInUp 0.6s ease forwards;
}

.overview-header {
  margin-bottom: 24rpx;
}

.overview-title {
  font-size: 36rpx;
  font-weight: 800;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  letter-spacing: 0.5rpx;
  
}

.points-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 24rpx;
  margin-bottom: 32rpx;
}

.points-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 24rpx 16rpx;
  background-color: #f8f9ff;
  border-radius: 12rpx;
}

.points-item.main {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #ffffff;
}

.points-number {
  font-size: 36rpx;
  font-weight: 700;
  margin-bottom: 8rpx;
}

.points-item.main .points-number {
  color: #ffffff;
}

.points-label {
  font-size: 24rpx;
  color: #666666;
}

.points-item.main .points-label {
  color: rgba(255, 255, 255, 0.9);
}



/* 记录列表 */
.records-section {
  background-color: #ffffff;
  border-radius: 16rpx;
  overflow: hidden;
}

.section-header {
  padding: 32rpx 32rpx 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.section-title {
  font-size: 36rpx;
  font-weight: 800;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  letter-spacing: 0.5rpx;
  
}

.records-list {
  
}

.record-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  border-bottom: 1rpx solid #f5f5f5;
  transition: background-color 0.2s ease;
}

.record-item:last-child {
  border-bottom: none;
}

.record-item:active {
  background-color: #f8f9ff;
}

.record-left {
  display: flex;
  align-items: center;
  flex: 1;
}

.record-icon {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
}

.record-icon.earn {
  background-color: #dcfce7;
}

.record-icon.consume {
  background-color: #fef2f2;
}

.icon-text {
  font-size: 28rpx;
  font-weight: 600;
}

.record-icon.earn .icon-text {
  color: #16a34a;
}

.record-icon.consume .icon-text {
  color: #dc2626;
}

.record-info {
  flex: 1;
}

.record-reason {
  display: block;
  font-size: 30rpx;
  font-weight: 500;
  color: #1a1a1a;
  margin-bottom: 4rpx;
}

.record-desc {
  display: block;
  font-size: 24rpx;
  color: #666666;
  line-height: 1.4;
  margin-bottom: 8rpx;
}

.record-time {
  display: block;
  font-size: 22rpx;
  color: #999999;
}

.record-right {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.record-amount {
  font-size: 32rpx;
  font-weight: 600;
}

.record-amount.earn {
  color: #16a34a;
}

.record-amount.consume {
  color: #dc2626;
}

.record-arrow {
  width: 32rpx;
  height: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.arrow-icon {
  font-size: 24rpx;
  color: #cccccc;
  font-weight: 300;
}

/* 加载状态 */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 80rpx 0;
}

.loading-dot {
  width: 48rpx;
  height: 48rpx;
  border: 3rpx solid #f0f0f0;
  border-top: 3rpx solid #007aff;
  border-radius: 50%;
  animation: loading-spin 1s linear infinite;
}

@keyframes loading-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-tip {
  font-size: 26rpx;
  color: #999999;
  margin-top: 24rpx;
}

/* 结束提示 */
.end-tip {
  text-align: center;
  padding: 60rpx 0;
}

.end-text {
  font-size: 26rpx;
  color: #cccccc;
}

/* 空状态 */
.empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 160rpx 0;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 32rpx;
  opacity: 0.3;
}

.empty-title {
  font-size: 32rpx;
  color: #666666;
  font-weight: 500;
  margin-bottom: 16rpx;
}

.empty-desc {
  font-size: 26rpx;
  color: #999999;
  text-align: center;
  line-height: 1.5;
}
