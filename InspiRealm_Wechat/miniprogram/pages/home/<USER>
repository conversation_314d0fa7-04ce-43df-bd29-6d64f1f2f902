// pages/home/<USER>

Page({
  data: {
    // 图片优化提示词
    imagePrompts: [
      {
        id: 1,
        title: "色彩增强",
        text: "让这张图片更加鲜艳明亮，增加温暖的色调，提升整体饱和度和对比度"
      },
      {
        id: 2,
        title: "人像美化",
        text: "美化人像，磨皮美白，增强眼部神采，让肌肤更加光滑自然"
      },
      {
        id: 3,
        title: "风景优化",
        text: "增强风景照的层次感，让天空更蓝，草地更绿，整体更有电影感"
      },
      {
        id: 4,
        title: "复古滤镜",
        text: "添加复古胶片效果，增加暖色调和颗粒感，营造怀旧氛围"
      },
      {
        id: 5,
        title: "清晰锐化",
        text: "提升图片清晰度和锐度，减少模糊，增强细节表现"
      },
      {
        id: 6,
        title: "艺术风格",
        text: "转换为油画风格，增加艺术感和质感，色彩更加丰富饱满"
      }
    ],
    
    // 文本生成图片提示词
    textPrompts: [
      {
        id: 1,
        title: "可爱动物",
        text: "一只毛茸茸的橘猫坐在樱花树下，粉色花瓣飘落，温暖的阳光透过树叶洒下，动漫风格，高清细腻"
      },
      {
        id: 2,
        title: "梦幻风景",
        text: "紫色薰衣草花田，远山如黛，夕阳西下，天空中飘着粉色云朵，唯美浪漫，油画质感"
      },
      {
        id: 3,
        title: "科幻场景",
        text: "未来城市夜景，霓虹灯闪烁，飞行汽车穿梭，高楼大厦直插云霄，赛博朋克风格，4K高清"
      },
      {
        id: 4,
        title: "美食摄影",
        text: "精美的日式料理，色彩丰富，摆盘精致，温暖的灯光，浅景深，专业美食摄影风格"
      },
      {
        id: 5,
        title: "人物肖像",
        text: "古装美女，长发飘逸，眼神深邃，穿着华丽的汉服，背景是古典园林，工笔画风格"
      },
      {
        id: 6,
        title: "抽象艺术",
        text: "色彩斑斓的抽象画，流动的线条，渐变的色彩，现代艺术风格，充满想象力和创意"
      }
    ]
  },

  onLoad: function() {
    this.checkTabBarIndex()
  },

  onShow: function() {
    this.checkTabBarIndex()
  },

  onPullDownRefresh: function() {
    setTimeout(function() {
      wx.stopPullDownRefresh()
    }, 1000)
  },

  checkTabBarIndex: function() {
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setData({
        selected: 0
      })
    }
  },

  copyPrompt: function(e) {
    var text = e.currentTarget.dataset.text
    
    wx.setClipboardData({
      data: text,
      success: function() {
        wx.showToast({
          title: '已复制到剪贴板',
          icon: 'success',
          duration: 2000
        })
      },
      fail: function() {
        wx.showToast({
          title: '复制失败',
          icon: 'error',
          duration: 2000
        })
      }
    })
  },

  goToProcess: function() {
    wx.navigateTo({
      url: '/pages/my-tasks/my-tasks'
    })
  },

  goToWorks: function() {
    wx.navigateTo({
      url: '/pages/my-works/my-works'
    })
  }
})
