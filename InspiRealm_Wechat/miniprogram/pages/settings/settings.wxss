/* pages/settings/settings.wxss */

/* Instagram风格设置页面 */
.page-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
  background-attachment: fixed;
}

/* Instagram风格顶部导航 */
.header {
  position: sticky;
  top: 0;
  z-index: 100;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-bottom: none;
  box-shadow: 0 2rpx 20rpx rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 40rpx;
  height: 100rpx;
}

.header-left .page-title {
  font-size: 40rpx;
  font-weight: 800;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  letter-spacing: 1rpx;
  
}

/* 内容区域 */
.content {
  padding: 24rpx 32rpx 120rpx;
}

/* Instagram风格用户信息卡片 - 渐变玻璃效果 */
.user-card {
  display: flex;
  align-items: center;
  padding: 40rpx;
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.9) 0%,
    rgba(255, 255, 255, 0.8) 50%,
    rgba(248, 250, 255, 0.85) 100%);
  backdrop-filter: blur(20rpx);
  border-radius: 24rpx;
  margin: 24rpx 24rpx 32rpx 24rpx;
  box-shadow:
    0 8rpx 32rpx rgba(102, 126, 234, 0.2),
    0 2rpx 8rpx rgba(118, 75, 162, 0.1),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.8);
  border: 1rpx solid rgba(255, 255, 255, 0.6);
  position: relative;
}

.user-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4rpx;
  background: linear-gradient(90deg, #667eea, #764ba2, #f093fb, #f5576c);
  background-size: 300% 100%;
  animation: gradient-shift 3s ease infinite;
  border-radius: 24rpx 24rpx 0 0;
}

/* Instagram风格全局动画 */
@keyframes gradient-shift {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.user-card {
  animation: fadeInUp 0.6s ease forwards;
}

.user-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  margin-right: 32rpx;
  background-color: #f0f0f0;
}

.user-info {
  flex: 1;
}

.user-name {
  display: block;
  font-size: 36rpx;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 8rpx;
}

.user-id {
  display: block;
  font-size: 24rpx;
  color: #999999;
}

/* 设置分组 */
.setting-group {
  margin-bottom: 32rpx;
}

.group-header {
  padding: 0 8rpx 16rpx;
}

.group-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #666666;
}

.group-content {
  background-color: #ffffff;
  border-radius: 16rpx;
  overflow: hidden;
}

/* 设置项 */
.setting-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  border-bottom: 1rpx solid #f5f5f5;
  transition: background-color 0.2s ease;
}

.setting-item:last-child {
  border-bottom: none;
}

.setting-item:active {
  background-color: #f8f9ff;
}

.setting-left {
  display: flex;
  align-items: center;
  flex: 1;
}

.setting-icon {
  font-size: 40rpx;
  margin-right: 24rpx;
  width: 40rpx;
  text-align: center;
}

.setting-info {
  flex: 1;
}

.setting-title {
  display: block;
  font-size: 30rpx;
  font-weight: 500;
  color: #1a1a1a;
  margin-bottom: 4rpx;
}

.setting-desc {
  display: block;
  font-size: 24rpx;
  color: #999999;
  line-height: 1.4;
}

.setting-right {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.setting-extra {
  font-size: 26rpx;
  color: #666666;
}

.setting-switch {
  transform: scale(0.8);
}

.setting-arrow {
  width: 32rpx;
  height: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.arrow-icon {
  font-size: 32rpx;
  color: #cccccc;
  font-weight: 300;
}

/* 退出登录按钮 */
.logout-section {
  margin-bottom: 32rpx;
}

.logout-btn {
  width: 100%;
  height: 88rpx;
  background-color: #ffffff;
  border: 1rpx solid #ff4757;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  outline: none;
}

.logout-btn:active {
  transform: scale(0.98);
  background-color: #fff5f5;
}

.logout-text {
  font-size: 30rpx;
  color: #ff4757;
  font-weight: 500;
}

.logout-btn::after {
  border: none;
}

/* 版本信息 */
.version-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 32rpx 0;
  gap: 8rpx;
}

.version-text {
  font-size: 24rpx;
  color: #cccccc;
}

.copyright {
  font-size: 22rpx;
  color: #cccccc;
}
