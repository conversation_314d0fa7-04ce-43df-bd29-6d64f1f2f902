<!--pages/settings/settings.wxml-->
<view class="page-container">
  <!-- 顶部导航栏 -->
  <view class="header">
    <view class="header-content">
      <view class="header-left">
        <text class="page-title">设置</text>
      </view>
    </view>
  </view>

  <!-- 内容区域 -->
  <view class="content">
    <!-- 用户信息卡片 -->
    <view wx:if="{{userInfo}}" class="user-card">
      <image class="user-avatar" src="{{userInfo.avatarUrl}}" mode="aspectFill" />
      <view class="user-info">
        <text class="user-name">{{userInfo.nickname}}</text>
        <text class="user-id">ID: {{userInfo.id}}</text>
      </view>
    </view>

    <!-- 设置分组 -->
    <view 
      wx:for="{{settingGroups}}" 
      wx:key="title"
      class="setting-group"
    >
      <view class="group-header">
        <text class="group-title">{{item.title}}</text>
      </view>
      
      <view class="group-content">
        <view 
          wx:for="{{item.items}}" 
          wx:key="id"
          wx:for-item="setting"
          class="setting-item"
          data-id="{{setting.id}}"
          bindtap="{{setting.switch ? '' : 'onSettingItemTap'}}"
        >
          <view class="setting-left">
            <text class="setting-icon">{{setting.icon}}</text>
            <view class="setting-info">
              <text class="setting-title">{{setting.title}}</text>
              <text class="setting-desc">{{setting.desc}}</text>
            </view>
          </view>
          
          <view class="setting-right">
            <!-- 额外信息 -->
            <text wx:if="{{setting.extra}}" class="setting-extra">{{setting.extra}}</text>
            
            <!-- 开关 -->
            <switch 
              wx:if="{{setting.switch}}"
              class="setting-switch"
              checked="{{setting.switchValue}}"
              data-id="{{setting.id}}"
              bindchange="onSwitchChange"
              catchtap=""
            />
            
            <!-- 箭头 -->
            <view wx:if="{{setting.arrow && !setting.switch}}" class="setting-arrow">
              <text class="arrow-icon">›</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 退出登录按钮 -->
    <view wx:if="{{userInfo}}" class="logout-section">
      <button class="logout-btn" bindtap="logout">
        <text class="logout-text">退出登录</text>
      </button>
    </view>

    <!-- 版本信息 -->
    <view class="version-info">
      <text class="version-text">假装会修图 v1.0.0</text>
      <text class="copyright">© 2024 假装会修图团队</text>
    </view>
  </view>
</view>
