// pages/settings/settings.ts
import { wechatAuth } from '../../utils/api'

Page({
  data: {
    // 设置项
    settingGroups: [
      {
        title: '账户设置',
        items: [
          {
            id: 'profile',
            title: '个人信息',
            desc: '修改头像、昵称等信息',
            icon: '👤',
            arrow: true
          },
          {
            id: 'privacy',
            title: '隐私设置',
            desc: '作品公开性、数据使用等',
            icon: '🔒',
            arrow: true
          }
        ]
      },
      {
        title: '应用设置',
        items: [
          {
            id: 'notifications',
            title: '消息通知',
            desc: '推送通知、系统消息等',
            icon: '🔔',
            arrow: true,
            switch: true,
            switchValue: true
          },
          {
            id: 'auto-save',
            title: '自动保存',
            desc: '处理后自动保存到相册',
            icon: '💾',
            arrow: false,
            switch: true,
            switchValue: false
          },
          {
            id: 'quality',
            title: '图片质量',
            desc: '高质量 (消耗更多流量)',
            icon: '🎨',
            arrow: true,
            switch: true,
            switchValue: true
          }
        ]
      },
      {
        title: '其他',
        items: [
          {
            id: 'cache',
            title: '清理缓存',
            desc: '清理临时文件和缓存数据',
            icon: '🗑️',
            arrow: true,
            extra: '约 25.6MB'
          },
          {
            id: 'about',
            title: '关于我们',
            desc: '版本信息、用户协议等',
            icon: 'ℹ️',
            arrow: true
          }
        ]
      }
    ],
    
    // 缓存大小
    cacheSize: '25.6MB',
    
    // 用户信息
    userInfo: null
  },

  onLoad() {
    this.loadUserInfo()
    this.calculateCacheSize()
  },

  /**
   * 加载用户信息
   */
  loadUserInfo() {
    const userInfo = wechatAuth.getLocalUserInfo()
    this.setData({ userInfo })
  },

  /**
   * 计算缓存大小
   */
  calculateCacheSize() {
    // 模拟计算缓存大小
    const size = (Math.random() * 50 + 10).toFixed(1)
    this.setData({ 
      cacheSize: `${size}MB`,
      'settingGroups[2].items[0].extra': `约 ${size}MB`
    })
  },

  /**
   * 处理设置项点击
   */
  onSettingItemTap(e: any) {
    const { id } = e.currentTarget.dataset
    
    switch (id) {
      case 'profile':
        this.goToProfile()
        break
      case 'privacy':
        this.showPrivacySettings()
        break
      case 'notifications':
        this.showNotificationSettings()
        break
      case 'quality':
        this.showQualitySettings()
        break
      case 'cache':
        this.clearCache()
        break
      case 'about':
        this.showAbout()
        break
      default:
        wx.showToast({
          title: '功能开发中',
          icon: 'none'
        })
    }
  },

  /**
   * 处理开关切换
   */
  onSwitchChange(e: any) {
    const { id } = e.currentTarget.dataset
    const { value } = e.detail
    
    console.log(`设置项 ${id} 切换为:`, value)
    
    // 更新本地状态
    const settingGroups = [...this.data.settingGroups]
    settingGroups.forEach(group => {
      group.items.forEach(item => {
        if (item.id === id) {
          item.switchValue = value
        }
      })
    })
    
    this.setData({ settingGroups })
    
    // 保存到本地存储
    wx.setStorageSync(`setting_${id}`, value)
    
    // 显示反馈
    wx.showToast({
      title: value ? '已开启' : '已关闭',
      icon: 'success'
    })
  },

  /**
   * 跳转到个人信息
   */
  goToProfile() {
    wx.navigateBack()
    // 触发个人中心的完善信息功能
    setTimeout(() => {
      const pages = getCurrentPages()
      const prevPage = pages[pages.length - 1]
      if (prevPage && prevPage.openProfileModal) {
        prevPage.openProfileModal()
      }
    }, 300)
  },

  /**
   * 显示隐私设置
   */
  showPrivacySettings() {
    wx.showActionSheet({
      itemList: ['作品默认公开', '作品默认私密', '仅好友可见'],
      success: (res) => {
        const options = ['公开', '私密', '好友可见']
        wx.showToast({
          title: `已设为${options[res.tapIndex]}`,
          icon: 'success'
        })
      }
    })
  },

  /**
   * 显示通知设置
   */
  showNotificationSettings() {
    wx.showModal({
      title: '消息通知',
      content: '是否允许接收推送通知？',
      success: (res) => {
        if (res.confirm) {
          wx.showToast({
            title: '已开启通知',
            icon: 'success'
          })
        }
      }
    })
  },

  /**
   * 显示质量设置
   */
  showQualitySettings() {
    wx.showActionSheet({
      itemList: ['高质量 (推荐)', '标准质量', '省流量模式'],
      success: (res) => {
        const options = ['高质量', '标准质量', '省流量']
        wx.showToast({
          title: `已设为${options[res.tapIndex]}`,
          icon: 'success'
        })
      }
    })
  },

  /**
   * 清理缓存
   */
  clearCache() {
    wx.showModal({
      title: '清理缓存',
      content: `确定要清理约 ${this.data.cacheSize} 的缓存数据吗？`,
      success: (res) => {
        if (res.confirm) {
          wx.showLoading({
            title: '清理中...'
          })
          
          // 模拟清理过程
          setTimeout(() => {
            wx.hideLoading()
            this.setData({ 
              cacheSize: '0MB',
              'settingGroups[2].items[0].extra': '约 0MB'
            })
            
            wx.showToast({
              title: '清理完成',
              icon: 'success'
            })
          }, 2000)
        }
      }
    })
  },

  /**
   * 显示关于信息
   */
  showAbout() {
    wx.showModal({
      title: '灵境域 v1.0.0',
      content: '一个专注于AI图像处理的创意平台\n\n© 2024 灵境域团队\n保留所有权利',
      showCancel: false,
      confirmText: '确定'
    })
  },

  /**
   * 退出登录
   */
  async logout() {
    wx.showModal({
      title: '确认退出',
      content: '退出登录后需要重新登录才能使用完整功能',
      success: async (res) => {
        if (res.confirm) {
          try {
            wx.showLoading({
              title: '退出中...'
            })
            
            await wechatAuth.logout()
            
            wx.hideLoading()
            wx.showToast({
              title: '已退出登录',
              icon: 'success'
            })
            
            // 返回个人中心并刷新状态
            wx.navigateBack()
            
          } catch (error) {
            wx.hideLoading()
            console.error('退出登录失败:', error)
            wx.showToast({
              title: '退出失败',
              icon: 'none'
            })
          }
        }
      }
    })
  }
})
