// pages/profile/profile.ts
import { userApi, wechat<PERSON><PERSON>, errorHandler } from '../../utils/api'
import { imageUtils } from '../../utils/util'

interface UserInfo {
  id: number
  openId: string
  nickname: string
  avatarUrl: string
  gender: number
  country?: string
  province?: string
  city?: string
  phone?: string
  email?: string
  status: number
  pointsInfo: {
    totalPoints: number
    availablePoints: number
    lockedPoints: number
    consumedPoints: number
  }
  createTime: string
  lastLoginTime: string
}

Page({
  data: {
    // 用户信息
    userInfo: null as UserInfo | null,
    isLoggedIn: false,

    // 登录状态
    loginLoading: false,

    // 信息完善模态框相关
    showProfileModal: false,
    tempUserInfo: {
      nickName: '',
      avatarUrl: ''
    },
    hasProfileChanges: false,

    // 加载状态
    loading: true,
    refreshing: false,

    // 统计数据
    stats: {
      totalImages: 0,
      publicImages: 0,
      totalLikes: 0,
      totalViews: 0
    },

    // 功能菜单
    menuItems: [
      {
        id: 'profile-info',
        title: '完善信息',
        icon: '👤',
        desc: '设置专属头像和个性昵称',
        arrow: true
      },
      {
        id: 'my-works',
        title: '作品展厅',
        icon: '🖼️',
        desc: '欣赏我创作的精美作品',
        arrow: true
      },
      {
        id: 'my-tasks',
        title: '创作工坊',
        icon: '⚡',
        desc: '查看图片处理进度',
        arrow: true
      },
      {
        id: 'points-history',
        title: '能量宝库',
        icon: '💎',
        desc: '管理我的能量积分宝藏',
        arrow: true
      },
      {
        id: 'help',
        title: '小助手',
        icon: '🤖',
        desc: '贴心帮助与意见反馈',
        arrow: true
      }
    ],

    // 签到状态
    checkinStatus: {
      hasCheckedIn: false,
      checkinDate: '',
      loading: false
    }
  },

  onLoad() {
    this.checkLoginAndLoadData()
  },

  onShow() {
    // 更新底部导航栏选中状态
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setData({
        selected: 2
      })
    }

    // 页面显示时检查登录状态
    this.checkLoginAndLoadData()

    // 检查签到状态
    this.checkCheckinStatus()
  },

  onPullDownRefresh() {
    this.setData({ refreshing: true })
    this.loadUserData().finally(() => {
      this.setData({ refreshing: false })
      wx.stopPullDownRefresh()
    })
  },

  /**
   * 检查登录状态并加载数据
   */
  checkLoginAndLoadData() {
    const isLoggedIn = wechatAuth.checkLoginStatus()
    const localUserInfo = wechatAuth.getLocalUserInfo()

    this.setData({
      isLoggedIn,
      userInfo: localUserInfo,
      loading: false
    })

    if (isLoggedIn) {
      this.loadUserData()
    }
  },

  /**
   * 加载用户数据
   */
  async loadUserData() {
    if (!this.data.isLoggedIn) return

    try {
      // 获取最新用户信息
      const userResponse = await userApi.getUserProfile()
      const userInfo = userResponse.data

      // 更新本地存储
      wx.setStorageSync('userInfo', userInfo)

      // 更新页面数据
      this.setData({
        userInfo,
        stats: {
          totalImages: userInfo.totalImages || 0,
          publicImages: userInfo.publicImages || 0,
          totalLikes: userInfo.totalLikes || 0,
          totalViews: userInfo.totalViews || 0
        }
      })

      console.log('用户信息更新完成:', userInfo)

    } catch (error) {
      console.error('加载用户数据失败:', error)
      // 如果是认证失败，清除登录状态
      if (error.message.includes('401') || error.message.includes('未登录')) {
        this.handleLogout()
      }
    }
  },

  /**
   * 处理微信登录 - 简化版本
   */
  async handleWeChatLogin() {
    console.log('🔐 登录按钮被点击了！')

    if (this.data.loginLoading) {
      console.log('⏳ 登录中，忽略重复点击')
      return
    }

    console.log('🚀 开始微信登录流程')

    try {
      this.setData({ loginLoading: true })

      // 直接使用简单的微信登录
      const loginResult = await wechatAuth.login()
      console.log('登录成功:', loginResult)

      this.setData({
        isLoggedIn: true,
        userInfo: loginResult.userInfo,
        loginLoading: false
      })

      wx.showToast({
        title: loginResult.isNewUser ? '注册成功' : '登录成功',
        icon: 'success'
      })

      // 加载用户数据
      this.loadUserData()

    } catch (error) {
      this.setData({ loginLoading: false })
      console.error('微信登录失败:', error)

      let errorMessage = '登录失败，请重试'
      if (error.message.includes('网络')) {
        errorMessage = '网络连接失败，请检查网络后重试'
      } else if (error.message.includes('获取微信授权码失败')) {
        errorMessage = '微信授权失败，请重试'
      }

      wx.showModal({
        title: '登录失败',
        content: errorMessage,
        confirmText: '重试',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            setTimeout(() => {
              this.handleWeChatLogin()
            }, 500)
          }
        }
      })
    }
  },



  /**
   * 选择头像
   */
  async onChooseAvatar(e: any) {
    const { avatarUrl } = e.detail
    console.log('选择头像:', avatarUrl)

    try {
      // 显示加载提示
      wx.showLoading({
        title: '处理头像中...',
        mask: true
      })

      // 将头像转换为base64格式
      const base64Avatar = await imageUtils.convertToBase64(avatarUrl)
      console.log('头像转换为base64成功')

      this.setData({
        'tempUserInfo.avatarUrl': base64Avatar
      })

      this.checkProfileChanges()

      wx.hideLoading()

    } catch (error) {
      wx.hideLoading()
      console.error('头像处理失败:', error)

      wx.showToast({
        title: '头像处理失败',
        icon: 'none'
      })

      // 如果转换失败，仍然使用原始URL作为备选方案
      this.setData({
        'tempUserInfo.avatarUrl': avatarUrl
      })
      this.checkProfileChanges()
    }
  },

  /**
   * 输入昵称
   */
  onNicknameInput(e: any) {
    const nickName = e.detail.value.trim()
    console.log('输入昵称:', nickName)

    this.setData({
      'tempUserInfo.nickName': nickName
    })

    this.checkProfileChanges()
  },

  /**
   * 检查信息是否有变化
   */
  checkProfileChanges() {
    const { tempUserInfo, userInfo } = this.data
    const hasChanges = tempUserInfo.nickName !== (userInfo && userInfo.nickname || '') ||
                      tempUserInfo.avatarUrl !== (userInfo && userInfo.avatarUrl || '')

    this.setData({
      hasProfileChanges: hasChanges
    })
  },

  /**
   * 打开信息完善模态框
   */
  openProfileModal() {
    this.setData({
      showProfileModal: true,
      tempUserInfo: {
        nickName: (this.data.userInfo && this.data.userInfo.nickname) || '',
        avatarUrl: (this.data.userInfo && this.data.userInfo.avatarUrl) || ''
      },
      hasProfileChanges: false
    })
  },

  /**
   * 关闭信息完善模态框
   */
  closeProfileModal() {
    this.setData({
      showProfileModal: false,
      tempUserInfo: {
        nickName: '',
        avatarUrl: ''
      },
      hasProfileChanges: false
    })
  },

  /**
   * 点击遮罩关闭模态框
   */
  onModalMaskTap() {
    this.closeProfileModal()
  },

  /**
   * 阻止模态框内容区域的点击事件冒泡
   */
  onModalContentTap() {
    // 阻止事件冒泡，防止点击内容区域时关闭模态框
  },

  /**
   * 保存个人信息
   */
  async saveProfile() {
    if (!this.data.hasProfileChanges) return

    try {
      errorHandler.showLoading('保存中...')

      const updateData = {
        nickname: this.data.tempUserInfo.nickName,
        avatarBase64: this.data.tempUserInfo.avatarUrl // 这里实际存储的是base64数据
      }

      const response = await userApi.updateUserInfo(updateData)

      // 更新本地用户信息
      const updatedUserInfo = response.data
      wx.setStorageSync('userInfo', updatedUserInfo)

      this.setData({
        userInfo: updatedUserInfo,
        showProfileModal: false,
        tempUserInfo: {
          nickName: '',
          avatarUrl: ''
        },
        hasProfileChanges: false
      })

      errorHandler.hideLoading()

      wx.showToast({
        title: '信息更新成功',
        icon: 'success'
      })

    } catch (error) {
      errorHandler.hideLoading()
      console.error('更新用户信息失败:', error)

      wx.showToast({
        title: '更新失败，请重试',
        icon: 'none'
      })
    }
  },

  /**
   * 处理登出
   */
  async handleLogout() {
    try {
      wx.showModal({
        title: '确认登出',
        content: '登出后将无法使用个人功能',
        success: async (res) => {
          if (res.confirm) {
            errorHandler.showLoading('登出中...')

            await wechatAuth.logout()

            this.setData({
              isLoggedIn: false,
              userInfo: null,
              stats: {
                totalImages: 0,
                publicImages: 0,
                totalLikes: 0,
                totalViews: 0
              }
            })

            errorHandler.hideLoading()

            wx.showToast({
              title: '已登出',
              icon: 'success'
            })
          }
        }
      })
    } catch (error) {
      errorHandler.hideLoading()
      console.error('登出失败:', error)
      wx.showToast({
        title: '登出失败',
        icon: 'none'
      })
    }
  },

  /**
   * 处理菜单项点击
   */
  onMenuItemTap(e: any) {
    const { id } = e.currentTarget.dataset

    if (!this.data.isLoggedIn) {
      wx.showModal({
        title: '需要登录',
        content: '请先登录后使用此功能',
        showCancel: false,
        confirmText: '确定'
      })
      return
    }

    switch (id) {
      case 'profile-info':
        this.openProfileModal()
        break
      case 'my-works':
        wx.navigateTo({
          url: '/pages/my-works/my-works'
        })
        break
      case 'my-tasks':
        wx.navigateTo({
          url: '/pages/my-tasks/my-tasks'
        })
        break
      case 'points-history':
        wx.navigateTo({
          url: '/pages/points-history/points-history'
        })
        break
      case 'help':
        wx.navigateTo({
          url: '/pages/help/help'
        })
        break
      default:
        wx.showToast({
          title: '功能开发中',
          icon: 'none'
        })
    }
  },

  /**
   * 跳转到创作页面
   */
  goToProcess() {
    wx.switchTab({
      url: '/pages/process/process'
    })
  },

  /**
   * 跳转到作品展厅页面
   */
  goToWorks() {
    wx.navigateTo({
      url: '/pages/my-works/my-works'
    })
  },

  /**
   * 检查签到状态
   */
  async checkCheckinStatus() {
    if (!this.data.isLoggedIn) return

    try {
      const response = await userApi.getCheckinStatus()
      this.setData({
        'checkinStatus.hasCheckedIn': response.data.hasCheckedIn,
        'checkinStatus.checkinDate': response.data.checkinDate
      })
    } catch (error) {
      console.error('检查签到状态失败:', error)
    }
  },

  /**
   * 每日签到
   */
  async handleCheckin() {
    if (!this.data.isLoggedIn) {
      wx.showModal({
        title: '需要登录',
        content: '请先登录后使用签到功能',
        showCancel: false,
        confirmText: '确定'
      })
      return
    }

    if (this.data.checkinStatus.hasCheckedIn) {
      wx.showToast({
        title: '今日已签到',
        icon: 'none'
      })
      return
    }

    if (this.data.checkinStatus.loading) return

    this.setData({
      'checkinStatus.loading': true
    })

    try {
      const response = await userApi.dailyCheckin()

      if (response.data.success) {
        this.setData({
          'checkinStatus.hasCheckedIn': true,
          'checkinStatus.checkinDate': new Date().toISOString().split('T')[0]
        })

        // 更新用户积分信息
        if (this.data.userInfo) {
          this.setData({
            'userInfo.pointsInfo.availablePoints': this.data.userInfo.pointsInfo.availablePoints + response.data.earnedPoints
          })
        }

        wx.showToast({
          title: `签到成功！获得${response.data.earnedPoints}积分`,
          icon: 'success',
          duration: 2000
        })
      } else if (response.data.alreadyCheckedIn) {
        this.setData({
          'checkinStatus.hasCheckedIn': true
        })
        wx.showToast({
          title: '今日已签到',
          icon: 'none'
        })
      } else {
        wx.showToast({
          title: '签到失败，请重试',
          icon: 'none'
        })
      }
    } catch (error) {
      console.error('签到失败:', error)
      wx.showToast({
        title: '签到失败，请重试',
        icon: 'none'
      })
    } finally {
      this.setData({
        'checkinStatus.loading': false
      })
    }
  }
})
