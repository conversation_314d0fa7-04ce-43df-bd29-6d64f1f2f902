<!--pages/profile/profile.wxml-->
<view class="profile-container">
  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-container">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 主要内容 -->
  <view wx:else class="main-content">
    <!-- 用户信息区域 -->
    <view class="user-section">
      <!-- 已登录状态 -->
      <view wx:if="{{isLoggedIn && userInfo}}" class="user-info">
        <view class="user-header">
          <image class="avatar" src="{{userInfo.avatarUrl}}" mode="aspectFill" />
          <view class="user-details">
            <view class="user-info-row">
              <view class="user-basic">
                <text class="nickname">{{userInfo.nickname}}</text>
                <text class="user-id">ID: {{userInfo.id}}</text>
              </view>
              <!-- 每日签到按钮 -->
              <view class="checkin-mini">
                <button
                  class="checkin-mini-btn {{checkinStatus.hasCheckedIn ? 'checked' : ''}}"
                  bindtap="handleCheckin"
                  disabled="{{checkinStatus.hasCheckedIn || checkinStatus.loading}}"
                  loading="{{checkinStatus.loading}}"
                  size="mini"
                >
                  {{checkinStatus.hasCheckedIn ? '已签到' : '签到+50'}}
                </button>
              </view>
            </view>
            <view class="points-info">
              <text class="points-label">可用积分</text>
              <text class="points-value">{{userInfo.pointsInfo.availablePoints}}</text>
            </view>
          </view>
        </view>

        <!-- 统计数据 -->
        <view class="stats-grid">
          <view class="stat-item">
            <text class="stat-number">{{stats.totalImages}}</text>
            <text class="stat-label">作品</text>
          </view>
          <view class="stat-item">
            <text class="stat-number">{{stats.totalLikes}}</text>
            <text class="stat-label">获赞</text>
          </view>
          <view class="stat-item">
            <text class="stat-number">{{stats.totalViews}}</text>
            <text class="stat-label">浏览</text>
          </view>
          <view class="stat-item">
            <text class="stat-number">{{userInfo.pointsInfo.totalPoints}}</text>
            <text class="stat-label">总积分</text>
          </view>
        </view>
      </view>

      <!-- 未登录状态 -->
      <view wx:else class="login-prompt">
        <view class="login-icon">👤</view>
        <text class="login-title">欢迎使用 假装会修图</text>
        <text class="login-desc">登录后可以创作和管理您的作品</text>

        <!-- 简化的微信登录 -->
        <button class="login-btn" bindtap="handleWeChatLogin" loading="{{loginLoading}}">
          {{loginLoading ? '登录中...' : '微信登录'}}
        </button>
      </view>
    </view>

    <!-- 功能菜单 -->
    <view class="menu-section">
      <view class="section-title">功能菜单</view>
      <view class="menu-list">
        <view
          wx:for="{{menuItems}}"
          wx:key="id"
          class="menu-item"
          data-id="{{item.id}}"
          bindtap="onMenuItemTap"
        >
          <view class="menu-left">
            <text class="menu-icon">{{item.icon}}</text>
            <view class="menu-info">
              <text class="menu-title">{{item.title}}</text>
              <text class="menu-desc">{{item.desc}}</text>
            </view>
          </view>
          <view wx:if="{{item.arrow}}" class="menu-arrow">
            <text class="arrow-icon">›</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 使用提示 -->
    <view class="usage-tip">
      <text class="tip-icon">💡</text>
      <text class="tip-text">如遇页面无法点击，请点击小程序右上角三个点，重新进入小程序</text>
    </view>

    <!-- 快捷操作 -->
    <view class="action-section">
      <view class="section-title">快捷操作</view>
      <view class="action-grid">
        <view class="action-item" bindtap="goToProcess">
          <view class="action-icon">✨</view>
          <text class="action-text">创作</text>
        </view>
        <view class="action-item" bindtap="goToWorks">
          <view class="action-icon">🎨</view>
          <text class="action-text">作品展厅</text>
        </view>
      </view>
    </view>

    <!-- 登出按钮 -->
    <view wx:if="{{isLoggedIn}}" class="logout-section">
      <button class="logout-btn" bindtap="handleLogout">
        <text class="logout-text">退出登录</text>
      </button>
    </view>

    <!-- 版本信息 -->
    <view class="footer">
      <text class="version-text">假装会修图 v1.0.0</text>
      <text class="copyright">© 2024 假装会修图团队</text>
    </view>
  </view>

  <!-- 信息完善模态框 -->
  <view wx:if="{{showProfileModal}}" class="modal-overlay" bindtap="onModalMaskTap">
    <view class="modal-content" catchtap="onModalContentTap">
      <!-- 模态框头部 -->
      <view class="modal-header">
        <text class="modal-title">完善个人信息</text>
        <view class="modal-close" bindtap="closeProfileModal">
          <text class="close-icon">✕</text>
        </view>
      </view>

      <!-- 模态框内容 -->
      <view class="modal-body">
        <view class="avatar-section">
          <text class="form-label">头像</text>
          <button class="avatar-btn" open-type="chooseAvatar" bindchooseavatar="onChooseAvatar">
            <image class="temp-avatar" src="{{tempUserInfo.avatarUrl || userInfo.avatarUrl}}" mode="aspectFill" />
            <text class="avatar-tip">点击更换头像</text>
          </button>
        </view>

        <view class="nickname-section">
          <text class="form-label">昵称</text>
          <input
            class="nickname-input"
            type="nickname"
            placeholder="请输入昵称"
            value="{{tempUserInfo.nickName || userInfo.nickname}}"
            bindinput="onNicknameInput"
            maxlength="20"
          />
        </view>
      </view>

      <!-- 模态框底部按钮 -->
      <view class="modal-footer">
        <button class="cancel-btn" bindtap="closeProfileModal">
          取消
        </button>
        <button class="save-btn" bindtap="saveProfile" disabled="{{!hasProfileChanges}}">
          保存
        </button>
      </view>
    </view>
  </view>
</view>
