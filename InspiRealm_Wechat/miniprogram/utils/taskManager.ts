// utils/taskManager.ts
// 任务管理工具类，用于本地存储和管理用户的AI处理任务

interface TaskInfo {
  taskNo: string
  originalImageUrl: string
  processType: string
  submitTime: string
  userId?: string
}

interface StoredTask extends TaskInfo {
  id: string
  localCreateTime: number
}

const STORAGE_KEY = 'user_tasks'
const MAX_TASKS = 50 // 最多存储50个任务

export class TaskManager {
  /**
   * 添加新任务到本地存储
   */
  static addTask(taskInfo: TaskInfo): string {
    const tasks = this.getAllTasks()
    const taskId = `task_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    
    const newTask: StoredTask = {
      id: taskId,
      ...taskInfo,
      localCreateTime: Date.now()
    }
    
    // 添加到数组开头（最新的在前面）
    tasks.unshift(newTask)
    
    // 限制存储数量
    if (tasks.length > MAX_TASKS) {
      tasks.splice(MAX_TASKS)
    }
    
    // 保存到本地存储
    try {
      wx.setStorageSync(STORAGE_KEY, tasks)
      console.log('任务已添加到本地存储:', taskId)
    } catch (error) {
      console.error('保存任务到本地存储失败:', error)
    }
    
    return taskId
  }
  
  /**
   * 获取所有本地存储的任务
   */
  static getAllTasks(): StoredTask[] {
    try {
      const tasks = wx.getStorageSync(STORAGE_KEY)
      return Array.isArray(tasks) ? tasks : []
    } catch (error) {
      console.error('读取本地任务失败:', error)
      return []
    }
  }
  
  /**
   * 根据ID获取特定任务
   */
  static getTaskById(taskId: string): StoredTask | null {
    const tasks = this.getAllTasks()
    return tasks.find(task => task.id === taskId) || null
  }
  
  /**
   * 删除指定任务
   */
  static removeTask(taskId: string): boolean {
    try {
      const tasks = this.getAllTasks()
      const filteredTasks = tasks.filter(task => task.id !== taskId)
      
      if (filteredTasks.length !== tasks.length) {
        wx.setStorageSync(STORAGE_KEY, filteredTasks)
        console.log('任务已从本地存储删除:', taskId)
        return true
      }
      return false
    } catch (error) {
      console.error('删除本地任务失败:', error)
      return false
    }
  }
  
  /**
   * 清理过期任务（超过7天的任务）
   */
  static cleanExpiredTasks(): number {
    try {
      const tasks = this.getAllTasks()
      const sevenDaysAgo = Date.now() - (7 * 24 * 60 * 60 * 1000)
      
      const validTasks = tasks.filter(task => task.localCreateTime > sevenDaysAgo)
      const removedCount = tasks.length - validTasks.length
      
      if (removedCount > 0) {
        wx.setStorageSync(STORAGE_KEY, validTasks)
        console.log(`已清理 ${removedCount} 个过期任务`)
      }
      
      return removedCount
    } catch (error) {
      console.error('清理过期任务失败:', error)
      return 0
    }
  }
  
  /**
   * 获取任务统计信息
   */
  static getTaskStats(): {
    total: number
    processing: number
    completed: number
    failed: number
  } {
    const tasks = this.getAllTasks()
    return {
      total: tasks.length,
      processing: 0, // 需要通过API查询实际状态
      completed: 0,
      failed: 0
    }
  }
}

// 在图片处理页面提交任务时调用此函数
export const recordNewTask = (taskNo: string, originalImageUrl: string, processType: string) => {
  const taskInfo: TaskInfo = {
    taskNo,
    originalImageUrl,
    processType,
    submitTime: new Date().toISOString()
  }
  
  return TaskManager.addTask(taskInfo)
}
