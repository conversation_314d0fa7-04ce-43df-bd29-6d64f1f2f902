export const formatTime = (date: Date) => {
  const year = date.getFullYear()
  const month = date.getMonth() + 1
  const day = date.getDate()
  const hour = date.getHours()
  const minute = date.getMinutes()
  const second = date.getSeconds()

  return (
    [year, month, day].map(formatNumber).join('/') +
    ' ' +
    [hour, minute, second].map(formatNumber).join(':')
  )
}

const formatNumber = (n: number) => {
  const s = n.toString()
  return s[1] ? s : '0' + s
}

/**
 * 图片处理工具
 */
export const imageUtils = {
  /**
   * 将图片转换为base64格式
   * @param filePath 图片文件路径
   * @returns Promise<string> base64格式的图片数据
   */
  convertToBase64(filePath: string): Promise<string> {
    return new Promise((resolve, reject) => {
      if (!filePath) {
        reject(new Error('文件路径不能为空'))
        return
      }

      wx.getFileSystemManager().readFile({
        filePath: filePath,
        encoding: 'base64',
        success: (res) => {
          // 检测图片格式
          const format = this.detectImageFormat(filePath)
          const base64Data = `data:image/${format};base64,${res.data}`
          console.log('图片转换为base64成功，格式:', format)
          resolve(base64Data)
        },
        fail: (error) => {
          console.error('图片转换base64失败:', error)
          reject(new Error('图片处理失败'))
        }
      })
    })
  },

  /**
   * 检测图片格式
   * @param filePath 图片文件路径
   * @returns 图片格式
   */
  detectImageFormat(filePath: string): string {
    const extension = filePath.toLowerCase().split('.').pop()
    switch (extension) {
      case 'png':
        return 'png'
      case 'gif':
        return 'gif'
      case 'webp':
        return 'webp'
      case 'jpg':
      case 'jpeg':
      default:
        return 'jpeg'
    }
  },

  /**
   * 从网络URL下载图片并转换为base64
   * @param imageUrl 图片网络地址
   * @returns Promise<string> base64格式的图片数据
   */
  downloadAndConvertToBase64(imageUrl: string): Promise<string> {
    return new Promise((resolve, reject) => {
      if (!imageUrl) {
        reject(new Error('图片URL不能为空'))
        return
      }

      // 如果已经是base64格式，直接返回
      if (imageUrl.startsWith('data:image/')) {
        resolve(imageUrl)
        return
      }

      wx.downloadFile({
        url: imageUrl,
        success: (res) => {
          if (res.statusCode === 200) {
            this.convertToBase64(res.tempFilePath)
              .then(resolve)
              .catch(reject)
          } else {
            reject(new Error('下载图片失败'))
          }
        },
        fail: (error) => {
          console.error('下载图片失败:', error)
          reject(new Error('下载图片失败'))
        }
      })
    })
  }
}
