/**
 * API 工具类
 * 处理与后端的网络请求
 */

// API 基础配置
const API_BASE_URL = 'https://test-pkqe.jdctools.com.cn/api/wechat'

// 开发环境检测
const isDevelopment = typeof wx !== 'undefined' && wx.getSystemInfoSync().platform === 'devtools'

// 开发环境下的模拟数据开关（当无法访问后端时自动启用）
let USE_MOCK_DATA = false

// 请求方法类型
type RequestMethod = 'GET' | 'POST' | 'PUT' | 'DELETE'

// 通用请求接口
interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
  timestamp: number
}

// 分页响应接口
interface PageResponse<T> {
  records: T[]
  total: number
  size: number
  current: number
  pages: number
}

// 广场图片接口
interface SquareImage {
  id: number
  imageUrl: string
  thumbnailUrl?: string
  title?: string
  aspectRatio: string
  processType: string
  viewCount: number
  likeCount: number
  downloadCount: number
  isLiked: boolean
  userInfo: {
    nickname: string
    avatarUrl?: string
  }
  createTime: string
}

// 用户相关接口
interface UserInfo {
  id: number
  openId: string
  nickname: string
  avatarUrl: string
  gender: number
  country?: string
  province?: string
  city?: string
  phone?: string
  email?: string
  status: number
  pointsInfo: {
    totalPoints: number
    availablePoints: number
    lockedPoints: number
    consumedPoints: number
  }
  createTime: string
  lastLoginTime: string
}

interface WeChatLoginRequest {
  code: string
  userInfo: {
    nickName: string
    avatarUrl: string
    gender: number
    country: string
    province: string
    city: string
  }
}

interface WeChatLoginResponse {
  token: string
  userInfo: UserInfo
  isNewUser: boolean
}

interface UpdateUserInfoRequest {
  nickname?: string
  avatarUrl?: string
  avatarBase64?: string
  gender?: number
  phone?: string
  email?: string
}

/**
 * 通用请求方法
 */
function request<T = any>(
  url: string,
  method: RequestMethod = 'GET',
  data?: any,
  header?: Record<string, string>,
  needAuth: boolean = true
): Promise<ApiResponse<T>> {
  return new Promise((resolve, reject) => {
    // 构建请求头
    const requestHeader: Record<string, string> = {
      'Content-Type': 'application/json',
      ...header
    }

    // 检查是否有token，如果有就添加（不管needAuth参数）
    const token = wx.getStorageSync('token')
    if (token) {
      requestHeader['Token'] = token
      console.log('🔐 请求添加Token:', url)
    } else {
      console.log('🔓 请求无Token:', url)
    }

    // 如果需要认证但没有token，则报错
    if (needAuth && !token) {
      reject(new Error('需要登录'))
      return
    }

    wx.request({
      url: `${API_BASE_URL}${url}`,
      method,
      data,
      header: requestHeader,
      success: (res) => {
        const response = res.data as ApiResponse<T>
        if (response.code === 200) {
          resolve(response)
        } else {
          console.error('API请求失败:', response.message)
          // 如果是401未授权，清除本地token
          if (response.code === 401) {
            wx.removeStorageSync('token')
            wx.removeStorageSync('userInfo')
          }
          reject(new Error(response.message || '请求失败'))
        }
      },
      fail: (error) => {
        console.error('网络请求失败:', error)

        // 如果是域名限制错误，自动启用模拟数据
        if (error.errMsg && error.errMsg.includes('url not in domain list')) {
          console.warn('检测到域名限制，自动启用模拟数据模式')
          USE_MOCK_DATA = true
          reject(new Error('DOMAIN_ERROR'))
        } else {
          reject(new Error('网络请求失败，请检查网络连接'))
        }
      }
    })
  })
}

// 模拟数据
const mockImages: SquareImage[] = [
  {
    id: 1,
    imageUrl: 'https://picsum.photos/400/600?random=1',
    thumbnailUrl: 'https://picsum.photos/200/300?random=1',
    title: '美丽的风景',
    aspectRatio: '4:3',
    processType: 'beautify',
    viewCount: 1234,
    likeCount: 89,
    downloadCount: 45,
    isLiked: false,
    userInfo: {
      nickname: '创意大师',
      avatarUrl: 'https://picsum.photos/100/100?random=101'
    },
    createTime: '2024-07-29T10:30:00'
  },
  {
    id: 2,
    imageUrl: 'https://picsum.photos/400/400?random=2',
    thumbnailUrl: 'https://picsum.photos/200/200?random=2',
    title: '艺术创作',
    aspectRatio: '1:1',
    processType: 'style_transfer',
    viewCount: 2567,
    likeCount: 156,
    downloadCount: 78,
    isLiked: true,
    userInfo: {
      nickname: '艺术家小王',
      avatarUrl: 'https://picsum.photos/100/100?random=102'
    },
    createTime: '2024-07-29T09:15:00'
  },
  {
    id: 3,
    imageUrl: 'https://picsum.photos/400/700?random=3',
    thumbnailUrl: 'https://picsum.photos/200/350?random=3',
    title: '卡通风格',
    aspectRatio: '3:4',
    processType: 'cartoon',
    viewCount: 890,
    likeCount: 67,
    downloadCount: 23,
    isLiked: false,
    userInfo: {
      nickname: '设计师小李',
      avatarUrl: 'https://picsum.photos/100/100?random=103'
    },
    createTime: '2024-07-29T08:45:00'
  }
]

/**
 * 广场相关API
 */
export const squareApi = {
  /**
   * 获取广场图片列表
   */
  getImages(params: {
    page?: number
    size?: number
    processType?: string
    aspectRatio?: string
  } = {}): Promise<ApiResponse<PageResponse<SquareImage>>> {

    // 开发环境使用模拟数据
    if (USE_MOCK_DATA) {
      return new Promise((resolve) => {
        setTimeout(() => {
          const page = params.page || 1
          const size = params.size || 10
          const start = (page - 1) * size
          const end = start + size

          let filteredImages = [...mockImages]

          // 模拟筛选
          if (params.processType) {
            filteredImages = filteredImages.filter(img => img.processType === params.processType)
          }
          if (params.aspectRatio) {
            filteredImages = filteredImages.filter(img => img.aspectRatio === params.aspectRatio)
          }

          // 模拟分页数据
          const records = []
          for (let i = 0; i < size; i++) {
            const mockIndex = (start + i) % filteredImages.length
            if (filteredImages[mockIndex]) {
              records.push({
                ...filteredImages[mockIndex],
                id: start + i + 1
              })
            }
          }

          resolve({
            code: 200,
            message: 'success',
            data: {
              records,
              total: 50, // 模拟总数
              size,
              current: page,
              pages: Math.ceil(50 / size)
            },
            timestamp: Date.now()
          })
        }, 500) // 模拟网络延迟
      })
    }

    // 生产环境使用真实API - 手动构建查询字符串
    const queryParts: string[] = []
    if (params.page) queryParts.push(`page=${params.page}`)
    if (params.size) queryParts.push(`size=${params.size}`)
    if (params.processType) queryParts.push(`processType=${encodeURIComponent(params.processType)}`)
    if (params.aspectRatio) queryParts.push(`aspectRatio=${encodeURIComponent(params.aspectRatio)}`)

    const queryString = queryParts.join('&')
    const url = `/square/images${queryString ? `?${queryString}` : ''}`

    return request<PageResponse<SquareImage>>(url, 'GET', undefined, undefined, false)
  },

  /**
   * 获取图片详情
   */
  getImageDetail(imageId: number): Promise<ApiResponse<SquareImage>> {
    if (USE_MOCK_DATA) {
      return new Promise((resolve) => {
        setTimeout(() => {
          const mockImage = mockImages.find(img => img.id === imageId) || mockImages[0]
          resolve({
            code: 200,
            message: 'success',
            data: mockImage,
            timestamp: Date.now()
          })
        }, 300)
      })
    }

    return request<SquareImage>(`/square/images/${imageId}`, 'GET', undefined, undefined, false)
  },

  /**
   * 点赞/取消点赞
   */
  toggleLike(imageId: number, isLike: boolean): Promise<ApiResponse<{
    imageId: number
    isLiked: boolean
    likeCount: number
  }>> {
    if (USE_MOCK_DATA) {
      return new Promise((resolve) => {
        setTimeout(() => {
          const mockLikeCount = Math.floor(Math.random() * 100) + 1
          resolve({
            code: 200,
            message: isLike ? '点赞成功' : '取消点赞成功',
            data: {
              imageId,
              isLiked: isLike,
              likeCount: mockLikeCount
            },
            timestamp: Date.now()
          })
        }, 200)
      })
    }

    return request<{
      imageId: number
      isLiked: boolean
      likeCount: number
    }>(`/square/images/${imageId}/like`, 'POST', { isLike })
  },

  /**
   * 获取图片详情
   */
  getImageDetail(imageId: number): Promise<ApiResponse<any>> {
    if (USE_MOCK_DATA) {
      return new Promise((resolve) => {
        setTimeout(() => {
          resolve({
            code: 200,
            message: 'success',
            data: {
              id: imageId,
              originalImageUrl: `https://picsum.photos/400/600?random=${imageId}`,
              imageUrl: `https://picsum.photos/400/600?random=${imageId + 100}`,
              thumbnailUrl: `https://picsum.photos/200/300?random=${imageId + 100}`,
              imageWidth: 400,
              imageHeight: 600,
              aspectRatio: '2:3',
              processType: 'style_transfer',
              prompt: '这是一个测试提示词，用于生成美丽的图片',
              negativePrompt: '低质量，模糊',
              viewCount: Math.floor(Math.random() * 1000) + 50,
              likeCount: Math.floor(Math.random() * 100) + 10,
              isLiked: Math.random() > 0.5,
              isPublic: true,
              auditStatus: 1,
              auditRemark: '审核通过',
              isDeleted: false,
              createTime: new Date().toISOString(),
              userInfo: {
                nickname: '测试用户',
                avatarUrl: `https://picsum.photos/100/100?random=${imageId + 200}`
              }
            },
            timestamp: Date.now()
          })
        }, 500)
      })
    }

    return request<any>(`/square/images/${imageId}`, 'GET', undefined, undefined, false)
  },

  /**
   * 图片预览（增加浏览量）
   */
  viewImage(imageId: number): Promise<ApiResponse<void>> {
    if (USE_MOCK_DATA) {
      return new Promise((resolve) => {
        setTimeout(() => {
          resolve({
            code: 200,
            message: 'success',
            data: null,
            timestamp: Date.now()
          })
        }, 100)
      })
    }

    return request<void>(`/square/images/${imageId}/view`, 'POST', undefined, undefined, false)
  }
}

/**
 * 错误处理工具
 */
export const errorHandler = {
  /**
   * 显示错误提示
   */
  showError(error: Error | string, title: string = '提示') {
    const message = typeof error === 'string' ? error : error.message
    wx.showModal({
      title,
      content: message,
      showCancel: false,
      confirmText: '确定'
    })
  },

  /**
   * 显示加载提示
   */
  showLoading(title: string = '加载中...') {
    wx.showLoading({
      title,
      mask: true
    })
  },

  /**
   * 隐藏加载提示
   */
  hideLoading() {
    wx.hideLoading()
  }
}

/**
 * 工具函数
 */
export const utils = {
  /**
   * 构建查询字符串（微信小程序兼容版本）
   */
  buildQueryString(params: Record<string, any>): string {
    const queryParts: string[] = []
    for (const key in params) {
      if (params.hasOwnProperty(key) && params[key] !== undefined && params[key] !== null && params[key] !== '') {
        queryParts.push(`${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`)
      }
    }
    return queryParts.join('&')
  },

  /**
   * 格式化时间
   */
  formatTime(dateString: string): string {
    const date = new Date(dateString)
    const now = new Date()
    const diff = now.getTime() - date.getTime()

    const minute = 60 * 1000
    const hour = 60 * minute
    const day = 24 * hour

    if (diff < minute) {
      return '刚刚'
    } else if (diff < hour) {
      return `${Math.floor(diff / minute)}分钟前`
    } else if (diff < day) {
      return `${Math.floor(diff / hour)}小时前`
    } else if (diff < 7 * day) {
      return `${Math.floor(diff / day)}天前`
    } else {
      return date.toLocaleDateString()
    }
  },

  /**
   * 格式化数字
   */
  formatNumber(num: number): string {
    if (num < 1000) {
      return num.toString()
    } else if (num < 10000) {
      return `${(num / 1000).toFixed(1)}k`
    } else {
      return `${(num / 10000).toFixed(1)}w`
    }
  },

  /**
   * 防抖函数
   */
  debounce<T extends (...args: any[]) => any>(
    func: T,
    wait: number
  ): (...args: Parameters<T>) => void {
    let timeout: number | null = null
    return (...args: Parameters<T>) => {
      if (timeout) clearTimeout(timeout)
      timeout = setTimeout(() => func(...args), wait)
    }
  }
}

/**
 * 用户认证相关API
 */
export const authApi = {
  /**
   * 微信登录
   */
  wechatLogin(loginData: WeChatLoginRequest): Promise<ApiResponse<WeChatLoginResponse>> {
    return request<WeChatLoginResponse>('/auth/login', 'POST', loginData, undefined, false)
  },

  /**
   * 登出
   */
  logout(): Promise<ApiResponse<void>> {
    return request<void>('/auth/logout', 'POST')
  }
}

/**
 * 用户信息相关API
 */
export const userApi = {
  /**
   * 获取用户资料
   */
  getUserProfile(): Promise<ApiResponse<UserInfo>> {
    return request<UserInfo>('/user/profile')
  },

  /**
   * 更新用户信息
   */
  updateUserInfo(updateData: UpdateUserInfoRequest): Promise<ApiResponse<UserInfo>> {
    return request<UserInfo>('/user/info', 'PUT', updateData)
  },

  /**
   * 获取用户基本信息
   */
  getUserInfo(): Promise<ApiResponse<UserInfo>> {
    return request<UserInfo>('/user/info')
  },

  /**
   * 获取我的作品（图片）
   */
  getMyWorks(params: {
    page: number
    size: number
    isPublic?: number
  }): Promise<ApiResponse<PageResponse<any>>> {
    const queryParts: string[] = []
    queryParts.push(`page=${params.page}`)
    queryParts.push(`size=${params.size}`)
    if (params.isPublic !== undefined) queryParts.push(`isPublic=${params.isPublic}`)

    const queryString = queryParts.join('&')
    const url = `/image/my-images${queryString ? `?${queryString}` : ''}`

    return request<PageResponse<any>>(url)
  },

  /**
   * 获取我的所有图片（包括任务信息）
   */
  getMyImages(params: {
    page: number
    size: number
    isPublic?: number
  }): Promise<ApiResponse<PageResponse<any>>> {
    const queryParts: string[] = []
    queryParts.push(`page=${params.page}`)
    queryParts.push(`size=${params.size}`)
    if (params.isPublic !== undefined) queryParts.push(`isPublic=${params.isPublic}`)

    const queryString = queryParts.join('&')
    const url = `/image/my-images${queryString ? `?${queryString}` : ''}`

    return request<PageResponse<any>>(url)
  },

  /**
   * 获取我的点赞
   */
  getMyLikes(params: {
    page: number
    size: number
  }): Promise<ApiResponse<PageResponse<any>>> {
    if (USE_MOCK_DATA) {
      return new Promise((resolve) => {
        setTimeout(() => {
          // 生成一些模拟的点赞数据
          const mockImages = Array.from({ length: Math.min(params.size, 5) }, (_, i) => ({
            id: i + 1,
            imageUrl: `https://picsum.photos/400/600?random=${i + 1}`,
            thumbnailUrl: `https://picsum.photos/200/300?random=${i + 1}`,
            title: `点赞的图片 ${i + 1}`,
            likeCount: Math.floor(Math.random() * 100) + 10,
            viewCount: Math.floor(Math.random() * 1000) + 50,
            createTime: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString(),
            isLiked: true
          }))

          resolve({
            code: 200,
            message: '操作成功',
            data: {
              records: mockImages,
              total: mockImages.length,
              size: params.size,
              current: params.page,
              pages: Math.ceil(mockImages.length / params.size)
            },
            timestamp: Date.now()
          })
        }, 500)
      })
    }

    return request<PageResponse<any>>('/user/likes', 'GET', params)
  },

  /**
   * 获取我的任务
   */
  getMyTasks(params: {
    page: number
    size: number
    status?: string
    processType?: string
  }): Promise<ApiResponse<PageResponse<any>>> {
    const queryParts: string[] = []
    queryParts.push(`page=${params.page}`)
    queryParts.push(`size=${params.size}`)
    if (params.status) queryParts.push(`status=${params.status}`)
    if (params.processType) queryParts.push(`processType=${encodeURIComponent(params.processType)}`)

    const queryString = queryParts.join('&')
    const url = `/image/my-tasks${queryString ? `?${queryString}` : ''}`

    return request<PageResponse<any>>(url)
  },

  /**
   * 查询单个任务状态
   */
  getTaskStatus(taskNo: string): Promise<ApiResponse<any>> {
    return request<any>(`/image/task/${taskNo}`)
  },

  /**
   * 获取积分信息
   */
  getPointsInfo(): Promise<ApiResponse<any>> {
    return request<any>('/points/balance')
  },

  /**
   * 获取积分记录
   */
  getPointsHistory(params: {
    page: number
    size: number
    transactionType?: number
  }): Promise<ApiResponse<PageResponse<any>>> {
    const queryParts: string[] = []
    queryParts.push(`page=${params.page}`)
    queryParts.push(`size=${params.size}`)
    if (params.transactionType) queryParts.push(`transactionType=${params.transactionType}`)

    const queryString = queryParts.join('&')
    const url = `/points/transactions${queryString ? `?${queryString}` : ''}`

    return request<PageResponse<any>>(url)
  },

  /**
   * 重新处理任务
   */
  retryTask(taskNo: string): Promise<ApiResponse<{
    taskNo: string
    pointsCost: number
    estimatedTime: number
    status: number
    statusDesc: string
    createTime: string
  }>> {
    return request<{
      taskNo: string
      pointsCost: number
      estimatedTime: number
      status: number
      statusDesc: string
      createTime: string
    }>(`/image/task/${taskNo}/retry`, 'POST')
  },

  /**
   * 删除任务
   */
  deleteTask(taskNo: string): Promise<ApiResponse<any>> {
    // 使用真实的API接口（如果后端支持）
    return request<any>(`/image/task/${taskNo}`, 'DELETE')
  },

  /**
   * 删除作品（图片）
   */
  deleteWork(workId: number): Promise<ApiResponse<any>> {
    return request<any>(`/image/${workId}`, 'DELETE')
  },

  /**
   * 切换作品公开状态
   */
  toggleWorkPublic(workId: number, isPublic: boolean): Promise<ApiResponse<any>> {
    return request<any>(`/image/${workId}/public`, 'POST', { isPublic: isPublic ? 1 : 0 })
  },

  /**
   * 每日签到
   */
  dailyCheckin(): Promise<ApiResponse<{
    success: boolean
    alreadyCheckedIn: boolean
    earnedPoints: number
    totalPoints: number
  }>> {
    return request<{
      success: boolean
      alreadyCheckedIn: boolean
      earnedPoints: number
      totalPoints: number
    }>('/points/daily-checkin', 'POST')
  },

  /**
   * 获取签到状态
   */
  getCheckinStatus(): Promise<ApiResponse<{
    hasCheckedIn: boolean
    checkinDate: string
  }>> {
    return request<{
      hasCheckedIn: boolean
      checkinDate: string
    }>('/points/checkin-status')
  }
}

/**
 * 图片处理相关API
 */
export const imageApi = {
  /**
   * 提交图片处理任务
   */
  processImage(data: {
    prompt: string
    imageUrl?: string
    imageBase64?: string
    aspectRatio?: string
    processType?: string
    textContent?: string
    style?: string
  }): Promise<ApiResponse<{
    taskNo: string
    pointsCost: number
    estimatedTime: number
    status: number
    statusDesc: string
    createTime: string
  }>> {
    return request<{
      taskNo: string
      pointsCost: number
      estimatedTime: number
      status: number
      statusDesc: string
      createTime: string
    }>('/image/process', 'POST', data)
  },

  /**
   * 润色提示词
   */
  polishPrompt(originalPrompt: string, imageBase64?: string): Promise<ApiResponse<string>> {
    const requestData: any = {
      originalPrompt: originalPrompt
    }

    // 如果有图片，添加到请求中
    if (imageBase64) {
      requestData.imageBase64 = imageBase64
    }

    return request<string>('/image/polish-prompt', 'POST', requestData)
  }
}

/**
 * 微信授权工具
 */
export const wechatAuth = {
  /**
   * 微信登录授权 - 使用新的授权方式
   */
  login(): Promise<WeChatLoginResponse> {
    return new Promise((resolve, reject) => {
      // 1. 获取微信授权码
      wx.login({
        success: async (loginRes) => {
          if (loginRes.code) {
            try {
              // 2. 直接使用code登录，后端会处理用户信息获取
              const response = await authApi.wechatLogin({
                code: loginRes.code,
                userInfo: {
                  nickName: '微信用户', // 默认昵称
                  avatarUrl: '', // 默认头像
                  gender: 0,
                  country: '',
                  province: '',
                  city: ''
                }
              })

              // 3. 保存登录信息
              wx.setStorageSync('token', response.data.token)
              wx.setStorageSync('userInfo', response.data.userInfo)

              resolve(response.data)
            } catch (error) {
              reject(error)
            }
          } else {
            reject(new Error('获取微信授权码失败'))
          }
        },
        fail: (error) => {
          console.error('微信登录失败:', error)
          reject(new Error('微信登录失败'))
        }
      })
    })
  },



  /**
   * 检查登录状态
   */
  checkLoginStatus(): boolean {
    const token = wx.getStorageSync('token')
    const userInfo = wx.getStorageSync('userInfo')
    return !!(token && userInfo)
  },

  /**
   * 登出
   */
  async logout(): Promise<void> {
    try {
      await authApi.logout()
    } catch (error) {
      console.error('服务端登出失败:', error)
    } finally {
      // 清除本地存储
      wx.removeStorageSync('token')
      wx.removeStorageSync('userInfo')
    }
  },

  /**
   * 获取本地用户信息
   */
  getLocalUserInfo(): UserInfo | null {
    return wx.getStorageSync('userInfo') || null
  }
}

/**
 * 全局登录检查工具
 */
export const loginUtils = {
  /**
   * 检查登录状态，未登录时跳转到个人页面
   */
  checkLoginWithRedirect(): boolean {
    if (!wechatAuth.checkLoginStatus()) {
      wx.showModal({
        title: '需要登录',
        content: '此功能需要登录后使用，是否前往登录？',
        confirmText: '去登录',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            wx.switchTab({
              url: '/pages/profile/profile'
            })
          }
        }
      })
      return false
    }
    return true
  },

  /**
   * 检查登录状态，未登录时显示提示
   */
  checkLoginWithToast(message: string = '请先登录'): boolean {
    if (!wechatAuth.checkLoginStatus()) {
      wx.showToast({
        title: message,
        icon: 'none',
        duration: 2000
      })
      return false
    }
    return true
  }
}

export default {
  squareApi,
  authApi,
  userApi,
  imageApi,
  wechatAuth,
  errorHandler,
  loginUtils,
  utils
}
