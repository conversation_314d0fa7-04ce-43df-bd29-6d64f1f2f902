# JDC视频生成门户网站前端架构设计

## 🏗️ 技术栈选型

### 核心框架
- **Next.js 14**: 使用App Router，支持SSR/SSG
- **React 18**: 最新稳定版本
- **TypeScript**: 类型安全开发

### 样式方案
- **Tailwind CSS**: 原子化CSS框架
- **Headless UI**: 无样式组件库
- **Framer Motion**: 动画库
- **Lucide React**: 图标库

### 状态管理
- **Zustand**: 轻量级状态管理
- **React Query (TanStack Query)**: 服务端状态管理
- **React Hook Form**: 表单状态管理

### 工具库
- **Axios**: HTTP客户端
- **Zod**: 数据验证
- **Day.js**: 日期处理
- **React Hot Toast**: 消息提示

## 📱 页面架构设计

### 🔓 未登录状态页面

#### 1. 首页 (`/`)
**功能定位**: 营销导向的门户首页
**主要内容**:
- Hero区域：主标题、副标题、CTA按钮
- 产品特色：核心功能亮点展示
- 视频展示：生成效果演示
- 用户评价：社会证明
- 快速开始：引导注册

#### 2. 产品介绍 (`/features`)
**功能定位**: 详细的产品功能介绍
**主要内容**:
- 功能列表：详细功能说明
- 技术优势：AI模型介绍
- 使用场景：应用案例
- 对比分析：竞品对比

#### 3. 案例展示 (`/showcase`)
**功能定位**: 用户作品展示和灵感库
**主要内容**:
- 作品画廊：精选用户作品
- 分类筛选：按风格、类型筛选
- 作品详情：展示生成参数
- 灵感推荐：相关作品推荐

#### 4. 定价方案 (`/pricing`)
**功能定位**: 套餐和积分购买
**主要内容**:
- 套餐对比：不同用户类型套餐
- 积分说明：积分使用规则
- 优惠活动：促销信息
- FAQ：常见问题

#### 5. 用户认证页面
- **登录页** (`/auth/login`)
- **注册页** (`/auth/register`)
- **密码重置** (`/auth/reset-password`)
- **邮箱验证** (`/auth/verify-email`)

### 🔒 已登录状态页面

#### 1. 工作台 (`/dashboard`)
**功能定位**: 用户主控制面板
**主要内容**:
- 快速统计：积分余额、任务数量
- 最近任务：最新生成的视频
- 快捷操作：快速开始新任务
- 系统通知：重要消息提醒

#### 2. 视频生成 (`/generate`)
**功能定位**: 核心功能页面
**主要内容**:
- 参数配置：提示词、模型选择、图片上传
- 预览区域：参数预览和历史记录
- 生成控制：开始生成、进度跟踪
- 结果展示：生成结果和下载

#### 3. 作品管理 (`/works`)
**功能定位**: 用户作品库管理
**主要内容**:
- 作品列表：网格/列表视图切换
- 搜索筛选：按时间、状态、标签筛选
- 批量操作：删除、分享、下载
- 作品详情：查看生成参数和编辑

#### 4. 用户设置 (`/settings`)
**功能定位**: 账户和偏好设置
**主要内容**:
- 个人信息：头像、昵称、联系方式
- 账户安全：密码修改、邮箱绑定
- 偏好设置：语言、主题、通知
- 积分管理：充值记录、消费明细

#### 5. 使用统计 (`/analytics`)
**功能定位**: 使用数据分析
**主要内容**:
- 使用概览：生成次数、成功率
- 趋势分析：时间维度统计
- 模型偏好：常用模型分析
- 积分流水：详细消费记录

## 🛣️ 路由设计

### App Router结构
```
app/
├── layout.tsx                 # 根布局
├── page.tsx                   # 首页
├── loading.tsx                # 全局加载组件
├── error.tsx                  # 全局错误组件
├── not-found.tsx              # 404页面
├── globals.css                # 全局样式
│
├── (marketing)/               # 营销页面组
│   ├── layout.tsx             # 营销页面布局
│   ├── features/
│   │   └── page.tsx           # 产品介绍
│   ├── showcase/
│   │   └── page.tsx           # 案例展示
│   └── pricing/
│       └── page.tsx           # 定价方案
│
├── auth/                      # 认证页面
│   ├── layout.tsx             # 认证页面布局
│   ├── login/
│   │   └── page.tsx           # 登录页
│   ├── register/
│   │   └── page.tsx           # 注册页
│   ├── reset-password/
│   │   └── page.tsx           # 密码重置
│   └── verify-email/
│       └── page.tsx           # 邮箱验证
│
├── (dashboard)/               # 用户工作区组
│   ├── layout.tsx             # 工作区布局
│   ├── dashboard/
│   │   └── page.tsx           # 工作台
│   ├── generate/
│   │   └── page.tsx           # 视频生成
│   ├── works/
│   │   ├── page.tsx           # 作品列表
│   │   └── [id]/
│   │       └── page.tsx       # 作品详情
│   ├── settings/
│   │   ├── page.tsx           # 设置首页
│   │   ├── profile/
│   │   │   └── page.tsx       # 个人信息
│   │   ├── security/
│   │   │   └── page.tsx       # 账户安全
│   │   └── billing/
│   │       └── page.tsx       # 积分管理
│   └── analytics/
│       └── page.tsx           # 使用统计
│
└── api/                       # API路由
    ├── auth/
    │   ├── login/
    │   ├── register/
    │   └── refresh/
    ├── user/
    │   ├── profile/
    │   └── settings/
    └── video/
        ├── generate/
        ├── query/
        └── list/
```

## 🧩 组件架构设计

### 组件分层结构
```
components/
├── ui/                        # 基础UI组件
│   ├── Button.tsx
│   ├── Input.tsx
│   ├── Modal.tsx
│   ├── Card.tsx
│   ├── Badge.tsx
│   ├── Avatar.tsx
│   ├── Dropdown.tsx
│   ├── Tabs.tsx
│   ├── Progress.tsx
│   └── Toast.tsx
│
├── forms/                     # 表单组件
│   ├── LoginForm.tsx
│   ├── RegisterForm.tsx
│   ├── ResetPasswordForm.tsx
│   ├── ProfileForm.tsx
│   └── VideoGenerateForm.tsx
│
├── layout/                    # 布局组件
│   ├── Header.tsx
│   ├── Footer.tsx
│   ├── Sidebar.tsx
│   ├── Navigation.tsx
│   └── Breadcrumb.tsx
│
├── features/                  # 功能组件
│   ├── auth/
│   │   ├── AuthGuard.tsx
│   │   ├── LoginButton.tsx
│   │   └── UserMenu.tsx
│   ├── video/
│   │   ├── VideoPlayer.tsx
│   │   ├── VideoCard.tsx
│   │   ├── VideoGrid.tsx
│   │   ├── GeneratePanel.tsx
│   │   └── ProgressTracker.tsx
│   ├── user/
│   │   ├── UserProfile.tsx
│   │   ├── PointsDisplay.tsx
│   │   └── ActivityFeed.tsx
│   └── dashboard/
│       ├── StatsCard.tsx
│       ├── RecentTasks.tsx
│       └── QuickActions.tsx
│
└── providers/                 # 上下文提供者
    ├── AuthProvider.tsx
    ├── ThemeProvider.tsx
    ├── QueryProvider.tsx
    └── ToastProvider.tsx

## 🗄️ 状态管理设计

### Zustand Store结构
```typescript
// stores/authStore.ts
interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (credentials: LoginCredentials) => Promise<void>;
  logout: () => void;
  refreshToken: () => Promise<void>;
}

// stores/videoStore.ts
interface VideoState {
  tasks: VideoTask[];
  currentTask: VideoTask | null;
  isGenerating: boolean;
  createTask: (params: GenerateParams) => Promise<void>;
  queryTask: (taskId: string) => Promise<void>;
  updateTask: (taskId: string, updates: Partial<VideoTask>) => void;
}

// stores/userStore.ts
interface UserState {
  profile: UserProfile | null;
  points: UserPoints | null;
  works: VideoWork[];
  updateProfile: (updates: Partial<UserProfile>) => Promise<void>;
  loadWorks: () => Promise<void>;
}
```

### React Query配置
```typescript
// lib/queryClient.ts
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5分钟
      cacheTime: 10 * 60 * 1000, // 10分钟
      retry: 3,
      refetchOnWindowFocus: false,
    },
    mutations: {
      retry: 1,
    },
  },
});

// hooks/useVideoTasks.ts
export const useVideoTasks = () => {
  return useQuery({
    queryKey: ['videoTasks'],
    queryFn: () => api.video.getTasks(),
    refetchInterval: 5000, // 5秒轮询
  });
};
```

## 🔌 API客户端设计

### Axios配置
```typescript
// lib/api.ts
const apiClient = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_URL,
  timeout: 30000,
});

// 请求拦截器
apiClient.interceptors.request.use((config) => {
  const token = getAccessToken();
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// 响应拦截器
apiClient.interceptors.response.use(
  (response) => response,
  async (error) => {
    if (error.response?.status === 401) {
      await refreshToken();
      return apiClient.request(error.config);
    }
    return Promise.reject(error);
  }
);
```

### API服务模块
```typescript
// services/authService.ts
export const authService = {
  login: (credentials: LoginCredentials) =>
    apiClient.post('/auth/login', credentials),
  register: (userData: RegisterData) =>
    apiClient.post('/auth/register', userData),
  refreshToken: () =>
    apiClient.post('/auth/refresh'),
  logout: () =>
    apiClient.post('/auth/logout'),
};

// services/videoService.ts
export const videoService = {
  generateVideo: (params: GenerateParams) =>
    apiClient.post('/video/generate', params),
  queryTask: (taskId: string) =>
    apiClient.get(`/video/query/${taskId}`),
  getTasks: (params?: TaskQueryParams) =>
    apiClient.get('/video/tasks', { params }),
  deleteTask: (taskId: string) =>
    apiClient.delete(`/video/tasks/${taskId}`),
};
```

## 🎨 设计系统

### 主题配置
```typescript
// tailwind.config.js
module.exports = {
  theme: {
    extend: {
      colors: {
        primary: {
          50: '#eff6ff',
          500: '#3b82f6',
          600: '#2563eb',
          700: '#1d4ed8',
        },
        gray: {
          50: '#f9fafb',
          100: '#f3f4f6',
          900: '#111827',
        },
      },
      fontFamily: {
        sans: ['Inter', 'sans-serif'],
      },
      animation: {
        'fade-in': 'fadeIn 0.5s ease-in-out',
        'slide-up': 'slideUp 0.3s ease-out',
      },
    },
  },
};
```

### 响应式设计断点
- **Mobile**: < 768px
- **Tablet**: 768px - 1024px
- **Desktop**: > 1024px
- **Large Desktop**: > 1440px
```