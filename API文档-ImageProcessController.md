# ImageProcessController API接口文档

## 接口概述
图片处理控制器，提供AI图片美化处理功能，包括任务创建、状态查询、图片管理等。

**基础路径**: `/api/wechat/image`  
**权限要求**: 需要微信用户登录（@WeChatAuth）

## 接口列表

### 1. 创建图片处理任务

#### 接口信息
- **接口路径**: `POST /api/wechat/image/process`
- **接口描述**: 提交图片处理请求，消耗积分，返回任务编号
- **权限要求**: 微信用户登录

#### 请求参数
```json
{
    "prompt": "美化一下这种图片,加上 我爱中国 四个字 尺寸[4:3]",
    "imageUrl": "https://example.com/image.png",
    "aspectRatio": "4:3",
    "processType": "beautify",
    "textContent": "我爱中国",
    "style": "realistic"
}
```

**参数说明**:
| 参数名 | 类型 | 必填 | 说明 | 验证规则 |
|--------|------|------|------|----------|
| prompt | String | 是 | 处理要求描述 | 最大1000个字符 |
| imageUrl | String | 否* | 原图片URL | 最大500个字符 |
| imageBase64 | String | 否* | 原图片base64 | Base64格式 |
| aspectRatio | String | 否 | 图片尺寸比例 | 最大20个字符 |
| processType | String | 否 | 处理类型 | 最大50个字符 |
| textContent | String | 否 | 添加的文字内容 | 最大200个字符 |
| style | String | 否 | 风格类型 | 最大50个字符 |

*注：imageUrl和imageBase64必须提供其中一种

#### 响应结果
**成功响应**:
```json
{
    "code": 200,
    "message": "任务创建成功",
    "data": {
        "taskNo": "TASK_20240729_A1B2C3D4",
        "pointsCost": 5,
        "estimatedTime": 30,
        "status": 1,
        "statusDesc": "待处理",
        "createTime": "2024-07-29 10:30:00"
    },
    "timestamp": 1690617600000
}
```

**失败响应**:
```json
{
    "code": 500,
    "message": "积分不足，无法处理图片",
    "data": null,
    "timestamp": 1690617600000
}
```

#### 业务逻辑
1. 验证请求参数的有效性
2. 锁定用户积分（默认5积分）
3. 创建处理任务记录
4. 异步调用AI图片处理服务
5. 返回任务编号和预计处理时间

---

### 2. 查询任务状态

#### 接口信息
- **接口路径**: `GET /api/wechat/image/task/{taskNo}`
- **接口描述**: 根据任务编号查询图片处理任务的状态和结果
- **权限要求**: 微信用户登录

#### 请求参数
| 参数名 | 类型 | 位置 | 必填 | 说明 |
|--------|------|------|------|------|
| taskNo | String | Path | 是 | 任务编号 |

#### 响应结果
**处理中响应**:
```json
{
    "code": 200,
    "message": "操作成功",
    "data": {
        "taskNo": "TASK_20240729_A1B2C3D4",
        "status": 2,
        "statusDesc": "处理中",
        "progress": 60,
        "errorMessage": null,
        "processedImage": null,
        "startTime": "2024-07-29 10:30:05",
        "endTime": null,
        "createTime": "2024-07-29 10:30:00"
    },
    "timestamp": 1690617600000
}
```

**处理成功响应**:
```json
{
    "code": 200,
    "message": "操作成功",
    "data": {
        "taskNo": "TASK_20240729_A1B2C3D4",
        "status": 3,
        "statusDesc": "处理成功",
        "progress": 100,
        "errorMessage": null,
        "processedImage": {
            "id": 1,
            "taskId": 1,
            "imageUrl": "https://minio.example.com/bucket/processed_image.jpg",
            "thumbnailUrl": "https://minio.example.com/bucket/thumbnail.jpg",
            "originalImageUrl": "https://example.com/original.png",
            "fileName": "user_1_20240729_103030_a1b2c3d4.jpg",
            "fileSize": 1024000,
            "imageWidth": 1024,
            "imageHeight": 768,
            "imageFormat": "jpg",
            "aspectRatio": "4:3",
            "processType": "beautify",
            "isPublic": 0,
            "likeCount": 0,
            "viewCount": 0,
            "createTime": "2024-07-29 10:30:30"
        },
        "startTime": "2024-07-29 10:30:05",
        "endTime": "2024-07-29 10:30:30",
        "createTime": "2024-07-29 10:30:00"
    },
    "timestamp": 1690617600000
}
```

**任务状态说明**:
| 状态码 | 状态描述 | 说明 |
|--------|----------|------|
| 1 | 待处理 | 任务已创建，等待处理 |
| 2 | 处理中 | 正在调用AI服务处理 |
| 3 | 处理成功 | 处理完成，图片已生成 |
| 4 | 处理失败 | 处理失败，积分已退回 |
| 5 | 已取消 | 任务已取消 |

---

### 3. 查询我的图片

#### 接口信息
- **接口路径**: `GET /api/wechat/image/my-images`
- **接口描述**: 分页查询当前用户的处理后图片
- **权限要求**: 微信用户登录

#### 请求参数
| 参数名 | 类型 | 必填 | 说明 | 默认值 |
|--------|------|------|------|--------|
| page | Integer | 否 | 页码 | 1 |
| size | Integer | 否 | 每页大小 | 10 |
| isPublic | Integer | 否 | 是否公开 0:私有 1:公开 | 无 |

#### 响应结果
**成功响应**:
```json
{
    "code": 200,
    "message": "操作成功",
    "data": {
        "records": [
            {
                "id": 1,
                "taskId": 1,
                "imageUrl": "https://minio.example.com/bucket/processed_image.jpg",
                "thumbnailUrl": "https://minio.example.com/bucket/thumbnail.jpg",
                "originalImageUrl": "https://example.com/original.png",
                "fileName": "user_1_20240729_103030_a1b2c3d4.jpg",
                "fileSize": 1024000,
                "imageWidth": 1024,
                "imageHeight": 768,
                "imageFormat": "jpg",
                "aspectRatio": "4:3",
                "processType": "beautify",
                "isPublic": 0,
                "likeCount": 0,
                "viewCount": 0,
                "createTime": "2024-07-29 10:30:30"
            }
        ],
        "total": 50,
        "size": 10,
        "current": 1,
        "pages": 5
    },
    "timestamp": 1690617600000
}
```

---

### 4. 查询我的任务

#### 接口信息
- **接口路径**: `GET /api/wechat/image/my-tasks`
- **接口描述**: 分页查询当前用户的所有处理任务
- **权限要求**: 微信用户登录

#### 请求参数
| 参数名 | 类型 | 必填 | 说明 | 默认值 |
|--------|------|------|------|--------|
| page | Integer | 否 | 页码 | 1 |
| size | Integer | 否 | 每页大小 | 10 |
| status | Integer | 否 | 任务状态 1:待处理 2:处理中 3:处理成功 4:处理失败 5:已取消 | 无 |
| processType | String | 否 | 处理类型 | 无 |

#### 响应结果
**成功响应**:
```json
{
    "code": 200,
    "message": "操作成功",
    "data": {
        "records": [
            {
                "taskNo": "TASK_20240729_A1B2C3D4",
                "originalImageUrl": "https://example.com/original.png",
                "resultImageUrl": "https://minio.example.com/bucket/processed_image.jpg",
                "processType": "beautify",
                "status": 3,
                "statusDesc": "处理成功",
                "progress": 100,
                "errorMessage": null,
                "submitTime": "2024-07-29 10:30:00",
                "completeTime": "2024-07-29 10:30:30",
                "processedImage": {
                    "id": 1,
                    "taskId": 1,
                    "imageUrl": "https://minio.example.com/bucket/processed_image.jpg",
                    "thumbnailUrl": "https://minio.example.com/bucket/thumbnail.jpg",
                    "originalImageUrl": "https://example.com/original.png",
                    "fileName": "user_1_20240729_103030_a1b2c3d4.jpg",
                    "fileSize": 1024000,
                    "imageWidth": 1024,
                    "imageHeight": 768,
                    "imageFormat": "jpg",
                    "aspectRatio": "4:3",
                    "processType": "beautify",
                    "isPublic": 0,
                    "likeCount": 0,
                    "viewCount": 0,
                    "createTime": "2024-07-29 10:30:30"
                }
            }
        ],
        "total": 25,
        "size": 10,
        "current": 1,
        "pages": 3
    },
    "timestamp": 1690617600000
}
```

**状态说明**:
| 状态码 | 状态描述 | 说明 |
|--------|----------|------|
| 1 | 待处理 | 任务已创建，等待处理 |
| 2 | 处理中 | 正在处理中 |
| 3 | 处理成功 | 处理完成，图片已生成 |
| 4 | 处理失败 | 处理失败，积分已退回 |
| 5 | 已取消 | 任务已取消 |

---

### 5. 设置图片公开状态

#### 接口信息
- **接口路径**: `POST /api/wechat/image/{imageId}/public`
- **接口描述**: 设置图片是否公开到广场
- **权限要求**: 微信用户登录

#### 请求参数
**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| imageId | Long | 是 | 图片ID |

**请求体**:
```json
{
    "isPublic": 1
}
```

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| isPublic | Integer | 是 | 是否公开 0:私有 1:公开 |

#### 响应结果
**成功响应**:
```json
{
    "code": 200,
    "message": "图片已公开到广场",
    "data": null,
    "timestamp": 1690617600000
}
```

**失败响应**:
```json
{
    "code": 500,
    "message": "设置失败，图片不存在或无权限",
    "data": null,
    "timestamp": 1690617600000
}
```

---

## 业务流程说明

### 图片处理完整流程
1. **提交任务**: 用户提交图片和处理要求
2. **积分锁定**: 系统锁定用户积分（5积分）
3. **任务创建**: 创建处理任务记录，状态为"待处理"
4. **异步处理**: 
   - 更新状态为"处理中"
   - 调用Sora Image API进行AI处理
   - 下载处理后的图片到MinIO存储
   - 生成缩略图
5. **处理完成**: 
   - 保存图片信息到数据库
   - 确认消费积分
   - 更新任务状态为"成功"
6. **处理失败**: 
   - 回退锁定的积分
   - 更新任务状态为"失败"

### 积分安全机制
- **预扣除**: 任务创建时先锁定积分
- **确认消费**: 处理成功后确认消费积分
- **自动退款**: 处理失败时自动退回积分

---

## 使用示例

### JavaScript示例（微信小程序）
```javascript
// 创建图片处理任务
wx.request({
    url: 'https://api.example.com/api/wechat/image/process',
    method: 'POST',
    header: {
        'Token': wx.getStorageSync('token'),
        'Content-Type': 'application/json'
    },
    data: {
        prompt: '美化一下这种图片,加上 我爱中国 四个字 尺寸[4:3]',
        imageUrl: 'https://example.com/image.png',
        aspectRatio: '4:3',
        processType: 'beautify',
        textContent: '我爱中国',
        style: 'realistic'
    },
    success: function(res) {
        if (res.data.code === 200) {
            const taskNo = res.data.data.taskNo;
            console.log('任务创建成功', taskNo);
            
            // 轮询查询任务状态
            checkTaskStatus(taskNo);
        } else {
            console.error('任务创建失败', res.data.message);
        }
    }
});

// 查询任务状态
function checkTaskStatus(taskNo) {
    wx.request({
        url: `https://api.example.com/api/wechat/image/task/${taskNo}`,
        method: 'GET',
        header: {
            'Token': wx.getStorageSync('token')
        },
        success: function(res) {
            if (res.data.code === 200) {
                const status = res.data.data.status;
                if (status === 3) {
                    // 处理成功
                    console.log('图片处理成功', res.data.data.processedImage);
                } else if (status === 4) {
                    // 处理失败
                    console.error('图片处理失败', res.data.data.errorMessage);
                } else if (status === 1 || status === 2) {
                    // 继续轮询
                    setTimeout(() => checkTaskStatus(taskNo), 3000);
                }
            }
        }
    });
}

// 查询我的图片
wx.request({
    url: 'https://api.example.com/api/wechat/image/my-images',
    method: 'GET',
    data: {
        page: 1,
        size: 10,
        isPublic: 0 // 只查询私有图片
    },
    header: {
        'Token': wx.getStorageSync('token')
    },
    success: function(res) {
        if (res.data.code === 200) {
            console.log('我的图片', res.data.data);
        }
    }
});

// 设置图片公开状态
wx.request({
    url: 'https://api.example.com/api/wechat/image/123/public',
    method: 'POST',
    header: {
        'Token': wx.getStorageSync('token'),
        'Content-Type': 'application/json'
    },
    data: {
        isPublic: 1
    },
    success: function(res) {
        if (res.data.code === 200) {
            wx.showToast({
                title: res.data.message,
                icon: 'success'
            });
        }
    }
});
```

### cURL示例
```bash
# 创建图片处理任务
curl -X POST "https://api.example.com/api/wechat/image/process" \
  -H "Token: abc123def456ghi789" \
  -H "Content-Type: application/json" \
  -d '{
    "prompt": "美化一下这种图片,加上 我爱中国 四个字 尺寸[4:3]",
    "imageUrl": "https://example.com/image.png",
    "aspectRatio": "4:3",
    "processType": "beautify",
    "textContent": "我爱中国",
    "style": "realistic"
  }'

# 查询任务状态
curl -X GET "https://api.example.com/api/wechat/image/task/TASK_20240729_A1B2C3D4" \
  -H "Token: abc123def456ghi789"

# 查询我的图片
curl -X GET "https://api.example.com/api/wechat/image/my-images?page=1&size=10&isPublic=0" \
  -H "Token: abc123def456ghi789"

# 设置图片公开状态
curl -X POST "https://api.example.com/api/wechat/image/123/public" \
  -H "Token: abc123def456ghi789" \
  -H "Content-Type: application/json" \
  -d '{"isPublic": 1}'
```

---

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 401 | 未登录或Token无效 |
| 500 | 处理要求描述不能为空 |
| 500 | 必须提供图片URL或Base64数据 |
| 500 | 图片URL和Base64数据只能提供其中一种 |
| 500 | 积分不足，无法处理图片 |
| 500 | 任务不存在 |
| 500 | 无权限查看该任务 |
| 500 | 设置失败，图片不存在或无权限 |

---

## 注意事项

1. **积分消费**: 每次图片处理消耗5积分，处理失败会自动退回
2. **任务状态**: 建议使用轮询方式查询任务状态，间隔3-5秒
3. **图片格式**: 支持jpg、png、gif、bmp、webp等常见格式
4. **文件大小**: 建议上传图片不超过10MB
5. **处理时间**: 通常需要30-60秒，复杂任务可能更长
6. **权限控制**: 只能查看和管理自己的任务和图片
7. **存储期限**: 图片在MinIO中永久存储，URL有效期7天

---

**文档版本**: v1.0  
**创建时间**: 2024-07-29  
**更新时间**: 2024-07-29
