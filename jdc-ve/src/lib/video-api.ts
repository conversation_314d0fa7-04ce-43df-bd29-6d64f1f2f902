import { api } from './api';

// 视频生成相关API

export interface ImageBase64Data {
  base64: string;
  filename: string;
  mimeType: string;
}

export interface VideoGenerateRequest {
  prompt: string;
  model?: string;
  enhancePrompt?: boolean;
  enableUpsample?: boolean;
  inputImages?: ImageBase64Data[];
  imageUrls?: string[];
  aspectRatio?: string;
  duration?: number;
}

export interface VideoGenerateResponse {
  taskId: string;
  taskNo: string;
  status: number;
  statusDesc: string;
  estimatedTime: number;
  pointsCost: number;
  createTime: string;
}

export interface VideoTaskStatus {
  taskId: string;
  taskNo: string;
  status: number;
  statusDesc: string;
  progress: number;
  videoUrl?: string;
  thumbnailUrl?: string;
  errorMessage?: string;
  createTime: string;
  completeTime?: string;
}

export interface VideoTaskListRequest {
  page: number;
  pageSize: number;
  status?: number;
  keyword?: string;
}

export interface VideoTaskListResponse {
  list: VideoTaskStatus[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

// 视频生成API
export const videoApi = {
  // 上传图片
  uploadImages: async (files: File[]): Promise<{ imageUrls: string[]; count: number }> => {
    const formData = new FormData();
    files.forEach(file => {
      formData.append('files', file);
    });

    // 使用api.ts的upload方法，保持一致的代理和错误处理
    return api.upload('/api/web/video/upload-images', formData);
  },

  // 创建视频生成任务
  generateVideo: async (data: VideoGenerateRequest): Promise<VideoGenerateResponse> => {
    return api.post<VideoGenerateResponse>('/api/web/video/generate', {
      prompt: data.prompt,
      model: data.model || 'veo3',
      enhancePrompt: data.enhancePrompt || true,
      enableUpsample: data.enableUpsample || false,
      inputImages: data.inputImages || [],
      imageUrls: data.imageUrls || [],
      aspectRatio: data.aspectRatio || '16:9',
      duration: data.duration || 5
    });
  },

  // 查询任务状态
  queryTaskStatus: async (taskId: string): Promise<VideoTaskStatus> => {
    return api.get<VideoTaskStatus>(`/api/web/video/query/${taskId}`);
  },

  // 获取任务列表
  getTaskList: async (data: VideoTaskListRequest): Promise<VideoTaskListResponse> => {
    return api.post<VideoTaskListResponse>('/api/web/video/list', data);
  },

  // 删除任务
  deleteTask: async (taskId: string): Promise<void> => {
    return api.delete<void>(`/api/web/video/delete/${taskId}`);
  },

  // 批量删除任务
  batchDeleteTasks: async (taskIds: string[]): Promise<void> => {
    return api.post<void>('/api/web/video/batch-delete', { taskIds });
  }
};
