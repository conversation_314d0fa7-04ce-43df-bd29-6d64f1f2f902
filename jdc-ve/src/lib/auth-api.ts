import { api } from './api';
import { RegisterData, LoginData, User } from '@/types';

// 认证相关API
export const authApi = {
  // 发送验证码
  sendVerificationCode: async (email: string, type: number = 1) => {
    return api.post('/api/web/auth/send-verification-code', {
      email,
      type, // 1:注册验证 2:密码重置 3:邮箱变更
    });
  },

  // 用户注册
  register: async (data: RegisterData) => {
    return api.post<{
      userId: number;
      username: string;
      email: string;
      createTime: string;
    }>('/api/web/auth/register', {
      username: data.username,
      email: data.email,
      password: data.password,
      confirmPassword: data.confirmPassword,
      verificationCode: data.verificationCode,
    });
  },

  // 用户登录
  login: async (data: LoginData) => {
    return api.post<{
      userInfo: User;
      accessToken: string;
      refreshToken: string;
      expiresIn: number;
    }>('/api/web/auth/login', {
      email: data.email,
      password: data.password,
      rememberMe: data.rememberMe || false,
    });
  },

  // 刷新Token
  refreshToken: async (refreshToken: string) => {
    return api.post<{
      accessToken: string;
      expiresIn: number;
    }>('/api/web/auth/refresh-token', {
      refreshToken,
    });
  },

  // 用户登出
  logout: async () => {
    return api.post('/api/web/auth/logout');
  },

  // 重置密码
  resetPassword: async (email: string, verificationCode: string, newPassword: string) => {
    return api.post('/api/web/auth/reset-password', {
      email,
      verificationCode,
      newPassword,
    });
  },
};