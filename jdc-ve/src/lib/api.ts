import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { ApiResponse } from '@/types';

// API基础配置 - 使用相对路径，通过Next.js代理转发
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || '';

// 创建axios实例
const apiClient: AxiosInstance = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Token管理
const getAccessToken = (): string | null => {
  if (typeof window !== 'undefined') {
    return localStorage.getItem('access_token');
  }
  return null;
};

const setAccessToken = (token: string): void => {
  if (typeof window !== 'undefined') {
    localStorage.setItem('access_token', token);
  }
};

const removeAccessToken = (): void => {
  if (typeof window !== 'undefined') {
    localStorage.removeItem('access_token');
    localStorage.removeItem('refresh_token');
  }
};

const getRefreshToken = (): string | null => {
  if (typeof window !== 'undefined') {
    return localStorage.getItem('refresh_token');
  }
  return null;
};

const setRefreshToken = (token: string): void => {
  if (typeof window !== 'undefined') {
    localStorage.setItem('refresh_token', token);
  }
};

// 请求拦截器
apiClient.interceptors.request.use(
  (config: AxiosRequestConfig) => {
    const token = getAccessToken();
    if (token && config.headers) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器 - 统一处理认证状态码
apiClient.interceptors.response.use(
  async (response: AxiosResponse) => {
    // 检查成功响应中的业务状态码
    const responseData = response.data;
    if (responseData && responseData.code) {
      const authCode = responseData.code;
      const originalRequest = response.config;

      // 处理认证相关的业务状态码
      switch (authCode) {
        case 4002: // TOKEN_EXPIRED - Token过期
          if (!originalRequest._retry) {
            originalRequest._retry = true;

            try {
              const refreshToken = getRefreshToken();
              if (refreshToken) {
                console.log('Token过期，尝试刷新...');
                const refreshResponse = await axios.post('/api/web/auth/refresh-token', {
                  refreshToken,
                });

                const { accessToken, refreshToken: newRefreshToken } = refreshResponse.data.data;
                setAccessToken(accessToken);
                setRefreshToken(newRefreshToken);

                // 重新发送原始请求
                originalRequest.headers.Authorization = `Bearer ${accessToken}`;
                return apiClient(originalRequest);
              }
            } catch (refreshError) {
              console.error('刷新Token失败:', refreshError);
              handleAuthError(authCode, responseData.message || 'Token已过期，请重新登录');
              return Promise.reject(new Error(responseData.message || 'Token已过期'));
            }
          }
          handleAuthError(authCode, responseData.message || 'Token已过期，请重新登录');
          return Promise.reject(new Error(responseData.message || 'Token已过期'));

        case 4001: // TOKEN_INVALID - Token无效
        case 4003: // SESSION_INVALID - 会话失效
        case 4004: // TOKEN_MISSING - 未提供Token
        case 4005: // TOKEN_FORMAT_ERROR - Token格式错误
        case 4006: // NOT_LOGGED_IN - 未登录
        case 4007: // ACCOUNT_DISABLED - 账户被禁用
          console.warn(`认证错误 [${authCode}]:`, responseData.message);
          handleAuthError(authCode, responseData.message || '认证失败，请重新登录');
          return Promise.reject(new Error(responseData.message || '认证失败'));

        case 403: // PERMISSION_DENIED - 权限不足
          console.warn('权限不足:', responseData.message);
          showErrorMessage(responseData.message || '权限不足，无法访问该资源', 'warning');
          return Promise.reject(new Error(responseData.message || '权限不足'));

        case 401: // 兼容旧的401错误处理
          if (!originalRequest._retry) {
            originalRequest._retry = true;

            try {
              const refreshToken = getRefreshToken();
              if (refreshToken) {
                const refreshResponse = await axios.post('/api/web/auth/refresh-token', {
                  refreshToken,
                });

                const { accessToken, refreshToken: newRefreshToken } = refreshResponse.data.data;
                setAccessToken(accessToken);
                setRefreshToken(newRefreshToken);

                // 重新发送原始请求
                originalRequest.headers.Authorization = `Bearer ${accessToken}`;
                return apiClient(originalRequest);
              }
            } catch (refreshError) {
              handleAuthError(401, '认证失败，请重新登录');
              return Promise.reject(new Error('认证失败'));
            }
          }
          handleAuthError(401, '认证失败，请重新登录');
          return Promise.reject(new Error('认证失败'));
      }
    }

    return response;
  },
  async (error) => {
    // 处理HTTP错误状态码
    if (error.response?.status === 401) {
      const originalRequest = error.config;
      if (!originalRequest._retry) {
        originalRequest._retry = true;

        try {
          const refreshToken = getRefreshToken();
          if (refreshToken) {
            const response = await axios.post('/api/web/auth/refresh-token', {
              refreshToken,
            });

            const { accessToken, refreshToken: newRefreshToken } = response.data.data;
            setAccessToken(accessToken);
            setRefreshToken(newRefreshToken);

            // 重新发送原始请求
            originalRequest.headers.Authorization = `Bearer ${accessToken}`;
            return apiClient(originalRequest);
          }
        } catch (refreshError) {
          handleAuthError(401, '认证失败，请重新登录');
          return Promise.reject(refreshError);
        }
      }
      handleAuthError(401, '认证失败，请重新登录');
    }

    return Promise.reject(error);
  }
);

/**
 * 处理认证错误的统一函数
 * @param authCode 认证状态码
 * @param message 错误消息
 */
const handleAuthError = (authCode: number, message: string) => {
  console.warn(`认证错误 [${authCode}]:`, message);

  // 显示错误提示
  showErrorMessage(message, 'error');

  // 清除本地token
  removeAccessToken();

  // 延迟跳转到登录页面，确保用户能看到错误提示
  setTimeout(() => {
    if (typeof window !== 'undefined') {
      window.location.href = '/auth/login';
    }
  }, 2000); // 增加延迟时间，确保用户能看到Toast
};

/**
 * 显示错误消息的统一函数
 * @param message 错误消息
 * @param type 消息类型
 */
const showErrorMessage = (message: string, type: 'error' | 'warning' | 'info' = 'error') => {
  console.error('错误提示:', message);

  if (typeof window !== 'undefined') {
    showToast(message, type);
  }
};

/**
 * 美观的Toast通知组件
 */
const showToast = (message: string, type: 'error' | 'warning' | 'info' = 'error') => {
  // 创建toast容器（如果不存在）
  let toastContainer = document.getElementById('toast-container');
  if (!toastContainer) {
    toastContainer = document.createElement('div');
    toastContainer.id = 'toast-container';
    toastContainer.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      z-index: 10000;
      pointer-events: none;
    `;
    document.body.appendChild(toastContainer);
  }

  // 创建toast元素
  const toast = document.createElement('div');
  const toastId = 'toast-' + Date.now();
  toast.id = toastId;

  // 根据类型设置样式
  const typeStyles = {
    error: {
      bg: '#fee2e2',
      border: '#fecaca',
      text: '#991b1b',
      icon: '❌'
    },
    warning: {
      bg: '#fef3c7',
      border: '#fde68a',
      text: '#92400e',
      icon: '⚠️'
    },
    info: {
      bg: '#dbeafe',
      border: '#bfdbfe',
      text: '#1e40af',
      icon: 'ℹ️'
    }
  };

  const style = typeStyles[type];

  toast.style.cssText = `
    background: ${style.bg};
    border: 1px solid ${style.border};
    color: ${style.text};
    padding: 12px 16px;
    border-radius: 8px;
    margin-bottom: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 14px;
    line-height: 1.4;
    max-width: 400px;
    word-wrap: break-word;
    pointer-events: auto;
    cursor: pointer;
    transform: translateX(100%);
    transition: transform 0.3s ease-in-out, opacity 0.3s ease-in-out;
    opacity: 0;
    display: flex;
    align-items: center;
    gap: 8px;
  `;

  toast.innerHTML = `
    <span style="font-size: 16px;">${style.icon}</span>
    <span style="flex: 1;">${message}</span>
    <span style="font-size: 18px; opacity: 0.6; margin-left: 8px;">×</span>
  `;

  // 添加到容器
  toastContainer.appendChild(toast);

  // 动画显示
  setTimeout(() => {
    toast.style.transform = 'translateX(0)';
    toast.style.opacity = '1';
  }, 10);

  // 点击关闭
  toast.addEventListener('click', () => {
    removeToast(toastId);
  });

  // 自动关闭
  setTimeout(() => {
    removeToast(toastId);
  }, type === 'error' ? 5000 : 3000); // 错误消息显示更久
};

/**
 * 移除Toast
 */
const removeToast = (toastId: string) => {
  const toast = document.getElementById(toastId);
  if (toast) {
    toast.style.transform = 'translateX(100%)';
    toast.style.opacity = '0';
    setTimeout(() => {
      toast.remove();
    }, 300);
  }
};

// API响应包装器 - 适配后端JsonResult格式
const handleApiResponse = <T>(response: AxiosResponse<any>): T => {
  const { data } = response;
  // 后端使用JsonResult格式: {code: 200, message: "success", data: {...}}
  if (data.code === 200) {
    return data.data;
  } else {
    throw new Error(data.message || '请求失败');
  }
};

// 通用API方法
export const api = {
  get: async <T>(url: string, config?: AxiosRequestConfig): Promise<T> => {
    const response = await apiClient.get(url, config);
    return handleApiResponse(response);
  },

  post: async <T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> => {
    const response = await apiClient.post(url, data, config);
    return handleApiResponse(response);
  },

  put: async <T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> => {
    const response = await apiClient.put(url, data, config);
    return handleApiResponse(response);
  },

  delete: async <T>(url: string, config?: AxiosRequestConfig): Promise<T> => {
    const response = await apiClient.delete(url, config);
    return handleApiResponse(response);
  },

  upload: async <T>(url: string, formData: FormData, config?: AxiosRequestConfig): Promise<T> => {
    const response = await apiClient.post(url, formData, {
      ...config,
      headers: {
        'Content-Type': 'multipart/form-data',
        ...config?.headers,
      },
    });
    return handleApiResponse(response);
  },
};

// 导出token管理方法
export {
  getAccessToken,
  setAccessToken,
  removeAccessToken,
  getRefreshToken,
  setRefreshToken,
};

export default apiClient;