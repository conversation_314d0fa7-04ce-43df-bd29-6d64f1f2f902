# WebAuth认证状态码集成示例

## 概述

本文档展示了如何在前端项目中使用优化后的WebAuth认证机制。系统已经在API拦截器层面统一处理了所有认证状态码，前端组件无需额外处理认证逻辑。

## 认证状态码说明

| 状态码 | 含义 | 前端处理 |
|--------|------|----------|
| 200 | 认证成功 | 正常处理业务逻辑 |
| 401 | 认证失败（通用） | 自动跳转登录页 |
| 403 | 权限不足 | 显示权限不足提示 |
| 4001 | Token无效 | 自动跳转登录页 |
| 4002 | Token过期 | 自动刷新Token，失败则跳转登录页 |
| 4003 | 会话失效 | 自动跳转登录页 |
| 4004 | 未提供Token | 自动跳转登录页 |
| 4005 | Token格式错误 | 自动跳转登录页 |
| 4006 | 未登录 | 自动跳转登录页 |
| 4007 | 账户被禁用 | 自动跳转登录页 |
| 500 | 认证服务异常 | 显示服务异常提示 |

## 前端组件使用示例

### 1. 基础API调用

```typescript
import { api } from '@/lib/api';

const UserProfile: React.FC = () => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(false);

  const loadUserProfile = async () => {
    setLoading(true);
    try {
      // API调用，认证错误会被自动处理
      const userData = await api.get('/api/web/user/profile');
      setUser(userData);
    } catch (error) {
      // 这里只需要处理业务逻辑错误
      console.error('加载用户信息失败:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div>
      <button onClick={loadUserProfile} disabled={loading}>
        {loading ? '加载中...' : '加载用户信息'}
      </button>
      {user && <div>用户名: {user.username}</div>}
    </div>
  );
};
```

### 2. 表单提交

```typescript
import { api } from '@/lib/api';

const VideoGenerationForm: React.FC = () => {
  const [prompt, setPrompt] = useState('');
  const [submitting, setSubmitting] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setSubmitting(true);
    
    try {
      // 提交表单，认证错误会被自动处理
      const result = await api.post('/api/web/video/generate', {
        prompt,
        model: 'veo3'
      });
      
      console.log('视频生成任务创建成功:', result);
      // 处理成功逻辑
      
    } catch (error) {
      // 只需要处理业务逻辑错误
      console.error('视频生成失败:', error);
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <form onSubmit={handleSubmit}>
      <input
        value={prompt}
        onChange={(e) => setPrompt(e.target.value)}
        placeholder="输入视频生成提示词"
      />
      <button type="submit" disabled={submitting}>
        {submitting ? '生成中...' : '生成视频'}
      </button>
    </form>
  );
};
```

### 3. 列表数据加载

```typescript
import { api } from '@/lib/api';

const VideoTaskList: React.FC = () => {
  const [tasks, setTasks] = useState([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    loadTasks();
  }, []);

  const loadTasks = async () => {
    setLoading(true);
    try {
      // 加载任务列表，认证错误会被自动处理
      const taskData = await api.get('/api/web/video/tasks', {
        params: { page: 1, pageSize: 10 }
      });
      setTasks(taskData.list);
    } catch (error) {
      // 只需要处理业务逻辑错误
      console.error('加载任务列表失败:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div>
      {loading ? (
        <div>加载中...</div>
      ) : (
        <ul>
          {tasks.map(task => (
            <li key={task.id}>{task.prompt}</li>
          ))}
        </ul>
      )}
    </div>
  );
};
```

## 自动处理机制

### Token刷新流程

1. 当API返回4002（Token过期）时，系统会自动：
   - 使用RefreshToken调用刷新接口
   - 更新本地存储的AccessToken
   - 重新发送原始请求
   - 如果刷新失败，清除Token并跳转登录页

### 自动登出流程

1. 当遇到需要重新登录的错误时，系统会自动：
   - 显示错误提示信息
   - 清除本地存储的所有Token
   - 1.5秒后跳转到登录页面

### 权限不足处理

1. 当API返回403（权限不足）时，系统会：
   - 显示权限不足的错误提示
   - 不会清除Token或跳转登录页
   - 用户可以继续使用其他有权限的功能

## 注意事项

1. **错误处理**: 组件中的catch块只需要处理业务逻辑错误，认证错误已被自动处理
2. **用户体验**: 系统会在跳转登录页前显示错误提示，确保用户了解发生了什么
3. **Token管理**: 无需手动管理Token的刷新和清除，系统会自动处理
4. **通知组件**: 可以根据项目使用的UI库调整`showErrorMessage`函数的实现

## 自定义通知

如果项目使用了特定的通知组件（如antd的message、react-toastify等），可以修改`api.ts`中的`showErrorMessage`函数：

```typescript
// 使用antd的message
import { message } from 'antd';

const showErrorMessage = (message: string) => {
  message.error(message);
};

// 使用react-toastify
import { toast } from 'react-toastify';

const showErrorMessage = (message: string) => {
  toast.error(message);
};
```

这样就完成了WebAuth认证状态码的前端集成，所有认证相关的错误都会被统一处理，大大简化了组件代码的复杂度。
