'use client';

import React from 'react';
import Navbar from './Navbar';
import { useAuthStore } from '@/store/auth';

interface MainLayoutProps {
  children: React.ReactNode;
  showNavbar?: boolean;
}

const MainLayout: React.FC<MainLayoutProps> = ({ 
  children, 
  showNavbar = true 
}) => {
  // 初始化认证状态检查
  React.useEffect(() => {
    const { checkAuth } = useAuthStore.getState();
    checkAuth();
  }, []);

  return (
    <div className="min-h-screen bg-gray-50">
      {showNavbar && <Navbar />}
      <main className={showNavbar ? 'pt-0' : ''}>
        {children}
      </main>
    </div>
  );
};

export default MainLayout;
