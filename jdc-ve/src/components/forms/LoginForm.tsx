'use client';

import React from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Mail, Lock, Eye, EyeOff } from 'lucide-react';
import { Button, Input } from '@/components/ui';
import { LoginCredentials } from '@/types';

const loginSchema = z.object({
  email: z
    .string()
    .min(1, '请输入邮箱地址')
    .email('请输入有效的邮箱地址'),
  password: z
    .string()
    .min(1, '请输入密码')
    .min(6, '密码至少需要6位字符'),
  rememberMe: z.boolean().optional(),
});

type LoginFormData = z.infer<typeof loginSchema>;

interface LoginFormProps {
  onSubmit: (data: LoginCredentials) => Promise<void>;
  isLoading?: boolean;
  error?: string;
}

const LoginForm: React.FC<LoginFormProps> = ({
  onSubmit,
  isLoading = false,
  error,
}) => {
  const {
    register,
    handleSubmit,
    formState: { errors },
    setError,
  } = useForm<LoginFormData>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      email: '',
      password: '',
      rememberMe: false,
    },
  });

  const handleFormSubmit = async (data: LoginFormData) => {
    try {
      await onSubmit(data);
    } catch (err) {
      setError('root', {
        message: err instanceof Error ? err.message : '登录失败，请重试',
      });
    }
  };

  return (
    <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
      {/* 邮箱输入 */}
      <Input
        {...register('email')}
        type="email"
        label="邮箱地址"
        placeholder="请输入您的邮箱地址"
        leftIcon={<Mail className="h-4 w-4" />}
        error={errors.email?.message}
        fullWidth
        disabled={isLoading}
      />

      {/* 密码输入 */}
      <Input
        {...register('password')}
        type="password"
        label="密码"
        placeholder="请输入您的密码"
        leftIcon={<Lock className="h-4 w-4" />}
        error={errors.password?.message}
        fullWidth
        disabled={isLoading}
      />

      {/* 记住我 */}
      <div className="flex items-center justify-between">
        <label className="flex items-center">
          <input
            {...register('rememberMe')}
            type="checkbox"
            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            disabled={isLoading}
          />
          <span className="ml-2 text-sm text-gray-600">记住我</span>
        </label>
        <a
          href="/auth/reset-password"
          className="text-sm text-blue-600 hover:text-blue-500"
        >
          忘记密码？
        </a>
      </div>

      {/* 错误信息 */}
      {(error || errors.root?.message) && (
        <div className="bg-red-50 border border-red-200 rounded-md p-3">
          <p className="text-sm text-red-600">
            {error || errors.root?.message}
          </p>
        </div>
      )}

      {/* 提交按钮 */}
      <Button
        type="submit"
        fullWidth
        size="lg"
        loading={isLoading}
        disabled={isLoading}
      >
        {isLoading ? '登录中...' : '登录'}
      </Button>

      {/* 注册链接 */}
      <div className="text-center">
        <span className="text-sm text-gray-600">
          还没有账户？{' '}
          <a
            href="/auth/register"
            className="text-blue-600 hover:text-blue-500 font-medium"
          >
            立即注册
          </a>
        </span>
      </div>
    </form>
  );
};

export default LoginForm;