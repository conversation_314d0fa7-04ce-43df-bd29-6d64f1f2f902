'use client';

import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Mail, Lock, User, Shield } from 'lucide-react';
import { Button, Input } from '@/components/ui';
import { RegisterData } from '@/types';

const registerSchema = z.object({
  username: z
    .string()
    .min(1, '请输入用户名')
    .min(3, '用户名至少需要3位字符')
    .max(20, '用户名不能超过20位字符')
    .regex(/^[a-zA-Z0-9_]+$/, '用户名只能包含字母、数字和下划线'),
  email: z
    .string()
    .min(1, '请输入邮箱地址')
    .email('请输入有效的邮箱地址'),
  password: z
    .string()
    .min(1, '请输入密码')
    .min(6, '密码至少需要6位字符'),
  confirmPassword: z
    .string()
    .min(1, '请确认密码'),
  verificationCode: z
    .string()
    .min(1, '请输入验证码')
    .length(6, '验证码为6位数字'),
  agreeTerms: z
    .boolean()
    .refine(val => val === true, '请同意服务条款和隐私政策'),
}).refine((data) => data.password === data.confirmPassword, {
  message: '两次输入的密码不一致',
  path: ['confirmPassword'],
});

type RegisterFormData = z.infer<typeof registerSchema>;

interface RegisterFormProps {
  onSubmit: (data: RegisterData) => Promise<void>;
  onSendVerificationCode: (email: string) => Promise<void>;
  isLoading?: boolean;
  isSendingCode?: boolean;
  error?: string;
}

const RegisterForm: React.FC<RegisterFormProps> = ({
  onSubmit,
  onSendVerificationCode,
  isLoading = false,
  isSendingCode = false,
  error,
}) => {
  const [codeSent, setCodeSent] = useState(false);
  const [countdown, setCountdown] = useState(0);

  const {
    register,
    handleSubmit,
    formState: { errors },
    setError,
    watch,
    getValues,
  } = useForm<RegisterFormData>({
    resolver: zodResolver(registerSchema),
    defaultValues: {
      username: '',
      email: '',
      password: '',
      confirmPassword: '',
      verificationCode: '',
      agreeTerms: false,
    },
  });

  const watchedEmail = watch('email');

  const handleSendCode = async () => {
    const email = getValues('email');
    if (!email || !z.string().email().safeParse(email).success) {
      setError('email', { message: '请输入有效的邮箱地址' });
      return;
    }

    try {
      await onSendVerificationCode(email);
      setCodeSent(true);
      setCountdown(60);

      const timer = setInterval(() => {
        setCountdown((prev) => {
          if (prev <= 1) {
            clearInterval(timer);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    } catch (err) {
      setError('verificationCode', {
        message: err instanceof Error ? err.message : '发送验证码失败',
      });
    }
  };

  const handleFormSubmit = async (data: RegisterFormData) => {
    try {
      await onSubmit(data);
    } catch (err) {
      setError('root', {
        message: err instanceof Error ? err.message : '注册失败，请重试',
      });
    }
  };

  return (
    <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
      {/* 用户名输入 */}
      <Input
        {...register('username')}
        type="text"
        label="用户名"
        placeholder="请输入用户名（3-20位字符）"
        leftIcon={<User className="h-4 w-4" />}
        error={errors.username?.message}
        fullWidth
        disabled={isLoading}
      />

      {/* 邮箱输入 */}
      <Input
        {...register('email')}
        type="email"
        label="邮箱地址"
        placeholder="请输入您的邮箱地址"
        leftIcon={<Mail className="h-4 w-4" />}
        error={errors.email?.message}
        fullWidth
        disabled={isLoading}
      />

      {/* 密码输入 */}
      <Input
        {...register('password')}
        type="password"
        label="密码"
        placeholder="请输入密码（至少8位，包含大小写字母和数字）"
        leftIcon={<Lock className="h-4 w-4" />}
        error={errors.password?.message}
        fullWidth
        disabled={isLoading}
      />

      {/* 确认密码 */}
      <Input
        {...register('confirmPassword')}
        type="password"
        label="确认密码"
        placeholder="请再次输入密码"
        leftIcon={<Lock className="h-4 w-4" />}
        error={errors.confirmPassword?.message}
        fullWidth
        disabled={isLoading}
      />

      {/* 验证码输入 */}
      <div className="space-y-2">
        <label className="block text-sm font-medium text-gray-700">
          邮箱验证码
        </label>
        <div className="flex gap-3">
          <div className="flex-1">
            <Input
              {...register('verificationCode')}
              type="text"
              placeholder="请输入6位验证码"
              leftIcon={<Shield className="h-4 w-4" />}
              error={errors.verificationCode?.message}
              disabled={isLoading}
            />
          </div>
          <Button
            type="button"
            variant="outline"
            onClick={handleSendCode}
            disabled={
              isLoading ||
              isSendingCode ||
              countdown > 0 ||
              !watchedEmail ||
              !z.string().email().safeParse(watchedEmail).success
            }
            className="whitespace-nowrap"
          >
            {countdown > 0 ? `${countdown}s` : codeSent ? '重新发送' : '发送验证码'}
          </Button>
        </div>
      </div>

      {/* 服务条款同意 */}
      <div className="space-y-2">
        <label className="flex items-start">
          <input
            {...register('agreeTerms')}
            type="checkbox"
            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mt-0.5"
            disabled={isLoading}
          />
          <span className="ml-2 text-sm text-gray-600">
            我已阅读并同意{' '}
            <a
              href="/terms"
              target="_blank"
              className="text-blue-600 hover:text-blue-500"
            >
              服务条款
            </a>
            {' '}和{' '}
            <a
              href="/privacy"
              target="_blank"
              className="text-blue-600 hover:text-blue-500"
            >
              隐私政策
            </a>
          </span>
        </label>
        {errors.agreeTerms && (
          <p className="text-sm text-red-600">{errors.agreeTerms.message}</p>
        )}
      </div>

      {/* 错误信息 */}
      {(error || errors.root?.message) && (
        <div className="bg-red-50 border border-red-200 rounded-md p-3">
          <p className="text-sm text-red-600">
            {error || errors.root?.message}
          </p>
        </div>
      )}

      {/* 提交按钮 */}
      <Button
        type="submit"
        fullWidth
        size="lg"
        loading={isLoading}
        disabled={isLoading}
      >
        {isLoading ? '注册中...' : '立即注册'}
      </Button>

      {/* 登录链接 */}
      <div className="text-center">
        <span className="text-sm text-gray-600">
          已有账户？{' '}
          <a
            href="/auth/login"
            className="text-blue-600 hover:text-blue-500 font-medium"
          >
            立即登录
          </a>
        </span>
      </div>
    </form>
  );
};

export default RegisterForm;