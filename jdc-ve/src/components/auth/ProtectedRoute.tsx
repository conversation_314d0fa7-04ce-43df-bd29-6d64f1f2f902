'use client';

import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuthStore } from '@/store/auth';
import { Loader2 } from 'lucide-react';

interface ProtectedRouteProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
  redirectTo?: string;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  fallback,
  redirectTo = '/auth/login',
}) => {
  const router = useRouter();
  const { isAuthenticated, isLoading, checkAuth } = useAuthStore();
  const [isChecking, setIsChecking] = useState(true);

  useEffect(() => {
    const verifyAuth = async () => {
      try {
        const isValid = await checkAuth();
        if (!isValid) {
          router.push(redirectTo);
          return;
        }
      } catch (error) {
        console.error('认证检查失败:', error);
        router.push(redirectTo);
        return;
      }
      setIsChecking(false);
    };

    verifyAuth();
  }, [checkAuth, router, redirectTo]);

  // 显示加载状态
  if (isChecking || isLoading) {
    return (
      fallback || (
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-purple-600" />
            <p className="text-gray-600">验证登录状态...</p>
          </div>
        </div>
      )
    );
  }

  // 未认证时不渲染内容（会被重定向）
  if (!isAuthenticated) {
    return null;
  }

  // 已认证，渲染受保护的内容
  return <>{children}</>;
};

export default ProtectedRoute;
