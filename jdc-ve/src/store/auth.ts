import React from 'react';
import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { User } from '@/types';
import { authApi } from '@/lib/auth-api';
import { setAccessToken, setRefreshToken, removeAccessToken } from '@/lib/api';

interface AuthState {
  // 状态
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  
  // 操作
  login: (email: string, password: string, rememberMe?: boolean) => Promise<void>;
  logout: () => Promise<void>;
  refreshAuth: () => Promise<void>;
  updateUser: (user: Partial<User>) => void;
  checkAuth: () => Promise<boolean>;
  clearAuth: () => void;
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      // 初始状态
      user: null,
      isAuthenticated: false,
      isLoading: false,

      // 登录
      login: async (email: string, password: string, rememberMe = false) => {
        set({ isLoading: true });
        
        try {
          const result = await authApi.login({ email, password, rememberMe });

          // 保存Token
          setAccessToken(result.accessToken);
          setRefreshToken(result.refreshToken);

          // 更新状态
          set({
            user: result.userInfo,
            isAuthenticated: true,
            isLoading: false,
          });
          
        } catch (error) {
          set({ isLoading: false });
          throw error;
        }
      },

      // 登出
      logout: async () => {
        try {
          await authApi.logout();
        } catch (error) {
          console.error('登出API调用失败:', error);
        } finally {
          // 清理本地状态
          removeAccessToken();
          set({
            user: null,
            isAuthenticated: false,
            isLoading: false,
          });
        }
      },

      // 刷新认证状态
      refreshAuth: async () => {
        const refreshToken = localStorage.getItem('refresh_token'); // 修复key名称
        if (!refreshToken) {
          get().clearAuth();
          return;
        }

        try {
          const result = await authApi.refreshToken(refreshToken);
          setAccessToken(result.accessToken);
          
          // 如果有用户信息，保持认证状态
          if (get().user) {
            set({ isAuthenticated: true });
          }
        } catch (error) {
          console.error('刷新Token失败:', error);
          get().clearAuth();
        }
      },

      // 更新用户信息
      updateUser: (userData: Partial<User>) => {
        const currentUser = get().user;
        if (currentUser) {
          set({
            user: { ...currentUser, ...userData }
          });
        }
      },

      // 检查认证状态
      checkAuth: async (): Promise<boolean> => {
        const state = get();

        // 如果已经在检查中，避免重复检查
        if (state.isLoading) {
          return state.isAuthenticated;
        }

        const accessToken = localStorage.getItem('access_token'); // 修复key名称
        const user = state.user;

        if (!accessToken || !user) {
          get().clearAuth();
          return false;
        }

        // 设置认证状态
        set({ isAuthenticated: true });
        return true;
      },

      // 清理认证状态
      clearAuth: () => {
        removeAccessToken();
        set({
          user: null,
          isAuthenticated: false,
          isLoading: false,
        });
      },
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        user: state.user,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
);

// 自动检查认证状态的Hook
export const useAuthCheck = () => {
  const { checkAuth, refreshAuth } = useAuthStore();
  
  React.useEffect(() => {
    const initAuth = async () => {
      const isValid = await checkAuth();
      if (isValid) {
        // 尝试刷新Token以确保有效性
        await refreshAuth();
      }
    };
    
    initAuth();
  }, [checkAuth, refreshAuth]);
};
