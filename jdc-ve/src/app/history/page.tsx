'use client';

import React, { useState } from 'react';
import ProtectedRoute from '@/components/auth/ProtectedRoute';
import MainLayout from '@/components/layout/MainLayout';
import { Card, CardContent, Button, Input, useToast, ToastProvider } from '@/components/ui';
import { videoApi } from '@/lib/video-api';
import {
  Search,
  Filter,
  Download,
  Trash2,
  Play,
  Clock,
  CheckCircle,
  XCircle,
  MoreHorizontal,
  Calendar,
  Eye,
  Share2,
  Copy,
  ExternalLink
} from 'lucide-react';

interface VideoTask {
  id: string;
  title: string;
  prompt: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  progress?: number;
  videoUrl?: string;
  thumbnailUrl?: string;
  createdAt: string;
  completedAt?: string;
  pointsCost: number;
}

// 内部组件，在ToastProvider内部使用
const HistoryPageContent: React.FC = () => {
  const { success, error: showError } = useToast();
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [selectedTasks, setSelectedTasks] = useState<string[]>([]);
  const [tasks, setTasks] = useState<VideoTask[]>([]);
  const [loading, setLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [previewVideo, setPreviewVideo] = useState<string | null>(null);
  const [showDropdown, setShowDropdown] = useState<string | null>(null);

  // 加载任务列表
  const loadTasks = async (page = 1, keyword = '', status?: number) => {
    setLoading(true);
    try {
      const result = await videoApi.getTaskList({
        page,
        pageSize: 10,
        keyword: keyword || undefined,
        status: status !== undefined ? status : undefined
      });

      console.log('API返回的完整数据:', result);
      console.log('任务列表:', result.list || result.tasks || result.data);
      console.log('数据类型:', typeof result);

      // 尝试不同的数据结构
      let taskList = result.list || result.tasks || result.data || [];

      // 如果result.data是对象且包含tasks，则使用result.data.tasks
      if (result.data && typeof result.data === 'object' && result.data.tasks) {
        taskList = result.data.tasks;
      }

      // 确保每个任务都有id字段（用于React key）
      taskList = taskList.map((task: any) => ({
        ...task,
        id: task.id || task.taskId || task.taskNo
      }));

      const currentPageNum = result.page || result.data?.page || result.currentPage || page;
      const totalPagesNum = result.totalPages || result.data?.totalPages || result.totalPage || Math.ceil((result.total || result.data?.total || 0) / 10);

      setTasks(taskList);
      setCurrentPage(currentPageNum);
      setTotalPages(totalPagesNum);

      console.log('设置的任务数据:', taskList);
      console.log('任务数量:', taskList.length);
    } catch (error) {
      console.error('加载任务列表失败:', error);
      showError('加载失败', '无法加载任务列表，请重试');
    } finally {
      setLoading(false);
    }
  };

  // 初始加载
  React.useEffect(() => {
    loadTasks();
  }, []);

  // 点击外部关闭下拉菜单
  React.useEffect(() => {
    const handleClickOutside = () => {
      setShowDropdown(null);
    };

    if (showDropdown) {
      document.addEventListener('click', handleClickOutside);
      return () => document.removeEventListener('click', handleClickOutside);
    }
  }, [showDropdown]);

  // 搜索和筛选变化时重新加载
  React.useEffect(() => {
    const statusValue = statusFilter === 'all' ? undefined : parseInt(statusFilter);
    loadTasks(1, searchTerm, statusValue);
  }, [searchTerm, statusFilter]);

  // 原来的模拟数据，作为备用
  const [backupTasks] = useState<VideoTask[]>([
    {
      id: '1',
      title: '可爱小猫在花园玩耍',
      prompt: '一只橘色的小猫在阳光明媚的花园里追逐蝴蝶，画面温馨可爱',
      status: 'completed',
      videoUrl: '/sample-video.mp4',
      thumbnailUrl: '/sample-thumbnail.jpg',
      createdAt: '2025-01-05 14:30',
      completedAt: '2025-01-05 14:35',
      pointsCost: 50
    },
    {
      id: '2',
      title: '城市夜景延时摄影',
      prompt: '现代城市夜景，车流如河，霓虹闪烁，展现都市繁华',
      status: 'processing',
      progress: 65,
      createdAt: '2025-01-05 13:15',
      pointsCost: 80
    },
    {
      id: '3',
      title: '海浪拍打礁石',
      prompt: '汹涌的海浪拍打着坚硬的礁石，溅起白色的浪花',
      status: 'completed',
      videoUrl: '/sample-video2.mp4',
      thumbnailUrl: '/sample-thumbnail2.jpg',
      createdAt: '2025-01-05 11:20',
      completedAt: '2025-01-05 11:28',
      pointsCost: 50
    },
    {
      id: '4',
      title: '森林中的小径',
      prompt: '阳光透过茂密的树叶洒在蜿蜒的小径上，鸟儿在枝头歌唱',
      status: 'failed',
      createdAt: '2025-01-04 16:45',
      pointsCost: 50
    }
  ]);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'processing':
        return <Clock className="h-5 w-5 text-blue-500 animate-pulse" />;
      case 'failed':
        return <XCircle className="h-5 w-5 text-red-500" />;
      default:
        return <Clock className="h-5 w-5 text-gray-500" />;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'completed': return '已完成';
      case 'processing': return '处理中';
      case 'failed': return '失败';
      case 'pending': return '等待中';
      default: return '未知';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'text-green-600 bg-green-100';
      case 'processing': return 'text-blue-600 bg-blue-100';
      case 'failed': return 'text-red-600 bg-red-100';
      case 'pending': return 'text-yellow-600 bg-yellow-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  // 预览视频
  const handlePreviewVideo = (videoUrl: string) => {
    setPreviewVideo(videoUrl);
  };

  // 下载视频
  const handleDownloadVideo = (videoUrl: string, taskNo: string) => {
    const link = document.createElement('a');
    link.href = videoUrl;
    link.download = `${taskNo}.mp4`;
    link.target = '_blank';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    success('下载开始', '视频下载已开始');
  };

  // 复制链接
  const handleCopyLink = async (videoUrl: string) => {
    try {
      await navigator.clipboard.writeText(videoUrl);
      success('复制成功', '视频链接已复制到剪贴板');
    } catch (error) {
      showError('复制失败', '无法复制链接');
    }
  };

  // 分享视频
  const handleShareVideo = (task: VideoTask) => {
    if (navigator.share && task.videoUrl) {
      navigator.share({
        title: `AI生成视频 - ${task.taskNo}`,
        text: task.prompt,
        url: task.videoUrl,
      }).catch(console.error);
    } else {
      handleCopyLink(task.videoUrl || '');
    }
  };

  // 由于搜索和筛选已在API层面处理，这里直接使用tasks
  const filteredTasks = tasks || [];

  const handleSelectTask = (taskId: string) => {
    setSelectedTasks(prev => 
      prev.includes(taskId) 
        ? prev.filter(id => id !== taskId)
        : [...prev, taskId]
    );
  };

  const handleSelectAll = () => {
    if (selectedTasks.length === filteredTasks.length) {
      setSelectedTasks([]);
    } else {
      setSelectedTasks(filteredTasks.map(task => task.id || task.taskId || task.taskNo));
    }
  };

  const handleDeleteSelected = async () => {
    if (selectedTasks.length === 0) return;

    const confirmDelete = window.confirm(`确定要删除选中的 ${selectedTasks.length} 个任务吗？`);
    if (confirmDelete) {
      try {
        await videoApi.batchDeleteTasks(selectedTasks);
        success('删除成功', `已删除 ${selectedTasks.length} 个任务`);
        setSelectedTasks([]);
        // 重新加载任务列表
        loadTasks(currentPage, searchTerm, statusFilter === 'all' ? undefined : parseInt(statusFilter));
      } catch (error) {
        console.error('删除任务失败:', error);
        showError('删除失败', '删除任务时发生错误，请重试');
      }
    }
  };

  const handleDownload = (videoUrl: string, title: string) => {
    try {
      const link = document.createElement('a');
      link.href = videoUrl;
      link.download = `${title}.mp4`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      success('下载开始', '视频下载已开始');
    } catch (error) {
      console.error('下载失败:', error);
      showError('下载失败', '无法下载视频，请重试');
    }
  };

  return (
    <ProtectedRoute>
      <MainLayout>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* 页面标题 */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              历史记录
            </h1>
            <p className="text-gray-600">
              管理您的视频生成任务和历史记录
            </p>
          </div>

          {/* 搜索和筛选 */}
          <Card className="mb-6">
            <CardContent className="p-6">
              <div className="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0 md:space-x-4">
                {/* 搜索框 */}
                <div className="flex-1 max-w-md">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    <Input
                      type="text"
                      placeholder="搜索视频标题或描述..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>

                {/* 状态筛选 */}
                <div className="flex items-center space-x-4">
                  <div className="flex items-center space-x-2">
                    <Filter className="h-4 w-4 text-gray-500" />
                    <select
                      value={statusFilter}
                      onChange={(e) => setStatusFilter(e.target.value)}
                      className="border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                    >
                      <option value="all">全部状态</option>
                      <option value="completed">已完成</option>
                      <option value="processing">处理中</option>
                      <option value="failed">失败</option>
                      <option value="pending">等待中</option>
                    </select>
                  </div>

                  {/* 批量操作 */}
                  {selectedTasks.length > 0 && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleDeleteSelected}
                      className="text-red-600 hover:text-red-700"
                    >
                      <Trash2 className="h-4 w-4 mr-1" />
                      删除选中 ({selectedTasks.length})
                    </Button>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 任务列表 */}
          <Card>
            <CardContent className="p-0">
              {/* 表头 */}
              <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    checked={selectedTasks.length === (filteredTasks?.length || 0) && (filteredTasks?.length || 0) > 0}
                    onChange={handleSelectAll}
                    className="mr-4"
                  />
                  <span className="text-sm font-medium text-gray-700">
                    全选 ({filteredTasks.length} 个任务)
                  </span>
                </div>
              </div>

              {/* 任务列表 */}
              <div className="divide-y divide-gray-200">
                {loading ? (
                  <div className="px-6 py-12 text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600 mx-auto mb-4"></div>
                    <p className="text-gray-600">加载中...</p>
                  </div>
                ) : filteredTasks.map((task) => (
                  <div key={task.id} className="px-6 py-6 hover:bg-gray-50 transition-colors">
                    <div className="flex items-start space-x-4">
                      {/* 选择框 */}
                      <input
                        type="checkbox"
                        checked={selectedTasks.includes(task.id)}
                        onChange={() => handleSelectTask(task.id)}
                        className="flex-shrink-0 mt-1"
                      />

                      {/* 缩略图/视频预览 */}
                      <div className="flex-shrink-0 w-20 h-20 bg-gray-200 rounded-lg overflow-hidden relative group cursor-pointer">
                        {task.thumbnailUrl ? (
                          <img
                            src={task.thumbnailUrl}
                            alt={task.prompt}
                            className="w-full h-full object-cover"
                            onClick={() => task.videoUrl && handlePreviewVideo(task.videoUrl)}
                          />
                        ) : (
                          <div className="w-full h-full flex items-center justify-center">
                            <Play className="h-8 w-8 text-gray-400" />
                          </div>
                        )}
                        {task.videoUrl && (
                          <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 flex items-center justify-center transition-all">
                            <Play className="h-6 w-6 text-white opacity-0 group-hover:opacity-100 transition-opacity" />
                          </div>
                        )}
                      </div>

                      {/* 任务信息 */}
                      <div className="flex-1 min-w-0">
                        <div className="flex items-start justify-between">
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center space-x-2 mb-2">
                              <h3 className="text-sm font-medium text-gray-900 truncate">
                                任务 #{task.taskNo}
                              </h3>
                              <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(task.status)}`}>
                                {getStatusText(task.status)}
                              </span>
                            </div>

                            <p className="text-sm text-gray-600 line-clamp-2 mb-3">
                              {task.prompt}
                            </p>

                            <div className="flex items-center space-x-4 text-xs text-gray-500">
                              <span className="flex items-center">
                                <Calendar className="h-3 w-3 mr-1" />
                                {task.createTime}
                              </span>
                              <span className="flex items-center">
                                <span className="w-2 h-2 bg-purple-500 rounded-full mr-1"></span>
                                {task.pointsCost} 积分
                              </span>
                              {task.duration && (
                                <span>{task.duration}秒</span>
                              )}
                            </div>

                            {/* 进度条 */}
                            {task.status === 2 && task.progress && (
                              <div className="flex items-center space-x-2 mt-2">
                                <div className="flex-1 bg-gray-200 rounded-full h-2">
                                  <div
                                  className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                                  style={{ width: `${task.progress}%` }}
                                />
                              </div>
                              <span className="text-xs text-gray-500">{task.progress}%</span>
                            </div>
                          )}
                          </div>
                        </div>

                        {/* 操作按钮区域 */}
                        <div className="flex items-center justify-between mt-3 pt-3 border-t border-gray-100">
                          <div className="flex items-center space-x-2">
                            {task.status === 3 && task.videoUrl && (
                              <>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => handlePreviewVideo(task.videoUrl!)}
                                  className="text-blue-600 hover:text-blue-700"
                                >
                                  <Eye className="h-4 w-4 mr-1" />
                                  预览
                                </Button>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => handleDownloadVideo(task.videoUrl!, task.taskNo)}
                                  className="text-green-600 hover:text-green-700"
                                >
                                  <Download className="h-4 w-4 mr-1" />
                                  下载
                                </Button>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => handleShareVideo(task)}
                                  className="text-purple-600 hover:text-purple-700"
                                >
                                  <Share2 className="h-4 w-4 mr-1" />
                                  分享
                                </Button>
                              </>
                            )}
                            {task.status === 2 && (
                              <span className="text-sm text-blue-600 flex items-center">
                                <Clock className="h-4 w-4 mr-1 animate-spin" />
                                生成中...
                              </span>
                            )}
                          </div>

                          <div className="relative">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => setShowDropdown(showDropdown === task.id ? null : task.id)}
                              className="text-gray-400 hover:text-gray-600"
                            >
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>

                            {showDropdown === task.id && (
                              <div className="absolute right-0 top-8 bg-white border border-gray-200 rounded-lg shadow-lg py-1 z-10 min-w-32">
                                {task.videoUrl && (
                                  <button
                                    onClick={() => {
                                      handleCopyLink(task.videoUrl!);
                                      setShowDropdown(null);
                                    }}
                                    className="w-full px-3 py-2 text-left text-sm text-gray-700 hover:bg-gray-100 flex items-center"
                                  >
                                    <Copy className="h-4 w-4 mr-2" />
                                    复制链接
                                  </button>
                                )}
                                <button
                                  onClick={() => {
                                    window.open(`/video/generate?prompt=${encodeURIComponent(task.prompt)}`, '_blank');
                                    setShowDropdown(null);
                                  }}
                                  className="w-full px-3 py-2 text-left text-sm text-gray-700 hover:bg-gray-100 flex items-center"
                                >
                                  <ExternalLink className="h-4 w-4 mr-2" />
                                  重新生成
                                </button>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {/* 空状态 */}
              {filteredTasks.length === 0 && (
                <div className="px-6 py-12 text-center">
                  <Play className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    {searchTerm || statusFilter !== 'all' ? '没有找到匹配的任务' : '还没有视频任务'}
                  </h3>
                  <p className="text-gray-500 mb-4">
                    {searchTerm || statusFilter !== 'all' 
                      ? '尝试调整搜索条件或筛选器' 
                      : '开始创建您的第一个AI视频吧！'
                    }
                  </p>
                  {!searchTerm && statusFilter === 'all' && (
                    <Button onClick={() => window.location.href = '/video/generate'}>
                      创建视频
                    </Button>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* 视频预览模态框 */}
        {previewVideo && (
          <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50" onClick={() => setPreviewVideo(null)}>
            <div className="relative max-w-4xl max-h-full p-4" onClick={(e) => e.stopPropagation()}>
              <button
                onClick={() => setPreviewVideo(null)}
                className="absolute -top-2 -right-2 bg-white rounded-full p-2 shadow-lg hover:bg-gray-100 z-10"
              >
                <XCircle className="h-6 w-6 text-gray-600" />
              </button>
              <video
                src={previewVideo}
                controls
                autoPlay
                className="max-w-full max-h-full rounded-lg"
                style={{ maxHeight: '80vh' }}
              />
            </div>
          </div>
        )}
      </MainLayout>
    </ProtectedRoute>
  );
};

// 主导出组件，包装ToastProvider
const HistoryPage: React.FC = () => {
  return (
    <ToastProvider>
      <HistoryPageContent />
    </ToastProvider>
  );
};

export default HistoryPage;
