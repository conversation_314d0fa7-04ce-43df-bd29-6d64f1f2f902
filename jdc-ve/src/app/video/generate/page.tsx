'use client';

import React, { useState } from 'react';
import ProtectedRoute from '@/components/auth/ProtectedRoute';
import MainLayout from '@/components/layout/MainLayout';
import { Card, CardContent, Button, Input, useToast, ToastProvider } from '@/components/ui';
import { videoApi } from '@/lib/video-api';
import { 
  Upload, 
  Wand2, 
  Settings, 
  Play,
  Image as ImageIcon,
  X,
  Loader2
} from 'lucide-react';

interface ImageData {
  file: File;
  base64: string;
  preview: string;
}

interface GenerateFormData {
  prompt: string;
  enhancePrompt: boolean;
  enableUpsample: boolean;
  inputImages: ImageData[];
}

// 内部组件，在ToastProvider内部使用
const VideoGeneratePageContent: React.FC = () => {
  const { success, error: showError } = useToast();
  const [formData, setFormData] = useState<GenerateFormData>({
    prompt: '',
    enhancePrompt: true,
    enableUpsample: false,
    inputImages: []
  });
  const [isGenerating, setIsGenerating] = useState(false);
  const [dragActive, setDragActive] = useState(false);
  const [uploadedImageUrls, setUploadedImageUrls] = useState<string[]>([]);

  // 组件卸载时清理预览URL内存
  React.useEffect(() => {
    return () => {
      formData.inputImages.forEach(img => {
        if (img.preview) {
          URL.revokeObjectURL(img.preview);
        }
      });
    };
  }, []);

  // 将文件转换为Base64
  const fileToBase64 = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => {
        const result = reader.result as string;
        // 移除data:image/jpeg;base64,前缀，只保留base64数据
        const base64 = result.split(',')[1];
        resolve(base64);
      };
      reader.onerror = error => reject(error);
    });
  };

  // 处理图片文件
  const processImageFiles = async (files: File[]): Promise<ImageData[]> => {
    try {
      console.log('开始上传图片，文件数量:', files.length);
      console.log('文件信息:', files.map(f => ({ name: f.name, size: f.size, type: f.type })));

      // 先上传图片到服务器
      const uploadResult = await videoApi.uploadImages(files);
      console.log('上传结果:', uploadResult);

      // 保存上传后的URL
      setUploadedImageUrls(prev => [...prev, ...uploadResult.imageUrls]);

      // 创建本地预览数据
      const imagePromises = files.map(async (file, index) => {
        const preview = URL.createObjectURL(file);
        return {
          file,
          base64: '', // 不再需要base64数据
          preview,
          uploadedUrl: uploadResult.imageUrls[index] // 保存上传后的URL
        };
      });

      const result = await Promise.all(imagePromises);
      success('图片上传成功', `成功上传 ${uploadResult.count} 张图片`);
      return result;
    } catch (error) {
      console.error('图片上传失败详细信息:', error);
      console.error('错误类型:', typeof error);
      console.error('错误消息:', error instanceof Error ? error.message : String(error));

      const errorMessage = error instanceof Error ? error.message : '未知错误';
      showError('上传失败', `图片上传失败: ${errorMessage}`);
      throw error;
    }
  };

  const handleInputChange = (field: keyof GenerateFormData, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  };

  const handleDrop = async (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    const files = Array.from(e.dataTransfer.files).filter(file =>
      file.type.startsWith('image/')
    );

    if (files.length > 0) {
      try {
        const newImages = await processImageFiles(files);
        setFormData(prev => ({
          ...prev,
          inputImages: [...prev.inputImages, ...newImages].slice(0, 5) // 最多5张图片
        }));
      } catch (error) {
        console.error('图片处理失败:', error);
        alert('图片处理失败，请重试');
      }
    }
  };

  const handleFileSelect = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []).filter(file =>
      file.type.startsWith('image/')
    );

    if (files.length > 0) {
      try {
        const newImages = await processImageFiles(files);
        setFormData(prev => ({
          ...prev,
          inputImages: [...prev.inputImages, ...newImages].slice(0, 5)
        }));
      } catch (error) {
        console.error('图片处理失败:', error);
        alert('图片处理失败，请重试');
      }
    }

    // 清空input值，允许重复选择同一文件
    e.target.value = '';
  };

  const removeImage = (index: number) => {
    setFormData(prev => {
      // 释放预览URL内存
      const imageToRemove = prev.inputImages[index];
      if (imageToRemove?.preview) {
        URL.revokeObjectURL(imageToRemove.preview);
      }

      return {
        ...prev,
        inputImages: prev.inputImages.filter((_, i) => i !== index)
      };
    });

    // 同步删除对应的上传URL
    setUploadedImageUrls(prev => {
      const newUrls = prev.filter((_, i) => i !== index);
      console.log('删除图片后的URL列表:', newUrls);
      return newUrls;
    });
  };

  const handleGenerate = async () => {
    if (!formData.prompt.trim()) {
      showError('输入错误', '请输入视频描述');
      return;
    }

    setIsGenerating(true);

    try {
      // 准备发送给后端的数据
      const generateData = {
        prompt: formData.prompt,
        enhancePrompt: formData.enhancePrompt,
        enableUpsample: formData.enableUpsample,
        imageUrls: uploadedImageUrls // 使用上传后的URL而不是base64
      };

      console.log('提交的数据:', generateData);
      console.log('当前图片预览数量:', formData.inputImages.length);
      console.log('当前上传URL数量:', uploadedImageUrls.length);

      // 调用真实的视频生成API
      const result = await videoApi.generateVideo(generateData);

      success('任务创建成功', `视频生成任务已提交！任务编号：${result.taskNo}`);

      // 清理预览URL内存
      formData.inputImages.forEach(img => {
        if (img.preview) {
          URL.revokeObjectURL(img.preview);
        }
      });

      // 重置表单
      setFormData({
        prompt: '',
        enhancePrompt: true,
        enableUpsample: false,
        inputImages: []
      });
      setUploadedImageUrls([]);

      // 可以跳转到历史记录页面
      setTimeout(() => {
        window.location.href = '/history';
      }, 2000);

    } catch (error) {
      console.error('生成失败:', error);
      const errorMessage = error instanceof Error ? error.message : '生成失败，请重试';
      showError('生成失败', errorMessage);
    } finally {
      setIsGenerating(false);
    }
  };

  return (
    <ProtectedRoute>
      <MainLayout>
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* 页面标题 */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              AI视频生成
            </h1>
            <p className="text-gray-600">
              基于Veo3模型，将您的创意转化为精美视频
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* 主要生成区域 */}
            <div className="lg:col-span-2 space-y-6">
              {/* 视频描述 */}
              <Card>
                <CardContent className="p-6">
                  <h2 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                    <Wand2 className="h-5 w-5 mr-2 text-purple-600" />
                    视频描述
                  </h2>
                  
                  <div className="relative">
                    <textarea
                      value={formData.prompt}
                      onChange={(e) => handleInputChange('prompt', e.target.value)}
                      placeholder="描述您想要生成的视频内容，例如：一只可爱的小猫在花园里玩耍，阳光明媚，画面温馨..."
                      className="w-full h-32 p-4 pr-12 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                      maxLength={2000}
                    />
                    {formData.prompt && (
                      <button
                        type="button"
                        onClick={() => handleInputChange('prompt', '')}
                        className="absolute top-3 right-3 p-1 text-gray-400 hover:text-gray-600 transition-colors"
                        title="清除内容"
                      >
                        <X className="h-4 w-4" />
                      </button>
                    )}
                  </div>

                  <div className="flex justify-between items-center mt-2">
                    <span className="text-sm text-gray-500">
                      {formData.prompt.length}/2000 字符
                    </span>
                    <div className="flex items-center space-x-4">
                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          checked={formData.enhancePrompt}
                          onChange={(e) => handleInputChange('enhancePrompt', e.target.checked)}
                          className="mr-2"
                        />
                        <span className="text-sm text-gray-700">智能优化描述</span>
                      </label>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* 图片上传 */}
              <Card>
                <CardContent className="p-6">
                  <h2 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                    <ImageIcon className="h-5 w-5 mr-2 text-blue-600" />
                    参考图片 (可选)
                  </h2>
                  
                  {/* 拖拽上传区域 */}
                  <div
                    className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
                      dragActive 
                        ? 'border-purple-500 bg-purple-50' 
                        : 'border-gray-300 hover:border-gray-400'
                    }`}
                    onDragEnter={handleDrag}
                    onDragLeave={handleDrag}
                    onDragOver={handleDrag}
                    onDrop={handleDrop}
                  >
                    <Upload className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-600 mb-2">
                      拖拽图片到此处，或
                      <label className="text-purple-600 hover:text-purple-700 cursor-pointer ml-1">
                        点击选择
                        <input
                          type="file"
                          multiple
                          accept="image/*"
                          onChange={handleFileSelect}
                          className="hidden"
                        />
                      </label>
                    </p>
                    <p className="text-sm text-gray-500">
                      支持 JPG、PNG 格式，最多5张图片
                    </p>
                  </div>

                  {/* 已上传的图片 */}
                  {formData.inputImages.length > 0 && (
                    <div className="mt-4">
                      <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                        {formData.inputImages.map((imageData, index) => (
                          <div key={index} className="relative group">
                            <img
                              src={imageData.preview}
                              alt={`上传图片 ${index + 1}`}
                              className="w-full h-24 object-cover rounded-lg"
                            />
                            <button
                              onClick={() => removeImage(index)}
                              className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity"
                            >
                              <X className="h-4 w-4" />
                            </button>
                            <div className="absolute bottom-1 left-1 bg-black bg-opacity-50 text-white text-xs px-1 rounded">
                              {imageData.file.name.length > 10
                                ? imageData.file.name.substring(0, 10) + '...'
                                : imageData.file.name
                              }
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* 生成按钮 */}
              <div className="flex justify-center">
                <Button
                  onClick={handleGenerate}
                  disabled={isGenerating || !formData.prompt.trim()}
                  size="lg"
                  className="px-8"
                >
                  {isGenerating ? (
                    <>
                      <Loader2 className="h-5 w-5 mr-2 animate-spin" />
                      生成中...
                    </>
                  ) : (
                    <>
                      <Play className="h-5 w-5 mr-2" />
                      开始生成视频
                    </>
                  )}
                </Button>
              </div>
            </div>

            {/* 侧边栏设置 */}
            <div className="space-y-6">
              {/* 生成设置 */}
              <Card>
                <CardContent className="p-6">
                  <h2 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                    <Settings className="h-5 w-5 mr-2 text-green-600" />
                    生成设置
                  </h2>
                  
                  <div className="space-y-4">
                    <label className="flex items-center justify-between">
                      <span className="text-sm text-gray-700">高清增强</span>
                      <input
                        type="checkbox"
                        checked={formData.enableUpsample}
                        onChange={(e) => handleInputChange('enableUpsample', e.target.checked)}
                        className="toggle"
                      />
                    </label>
                    
                    <div className="text-xs text-gray-500">
                      启用后将消耗更多积分，但视频质量更高
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* 积分消耗提示 */}
              <Card>
                <CardContent className="p-6">
                  <h3 className="text-sm font-semibold text-gray-900 mb-2">
                    积分消耗预估
                  </h3>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-600">基础生成</span>
                      <span className="font-medium">50 积分</span>
                    </div>
                    {formData.enableUpsample && (
                      <div className="flex justify-between">
                        <span className="text-gray-600">高清增强</span>
                        <span className="font-medium">+30 积分</span>
                      </div>
                    )}
                    <hr className="my-2" />
                    <div className="flex justify-between font-semibold">
                      <span>总计</span>
                      <span className="text-purple-600">
                        {50 + (formData.enableUpsample ? 30 : 0)} 积分
                      </span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* 使用提示 */}
              <Card>
                <CardContent className="p-6">
                  <h3 className="text-sm font-semibold text-gray-900 mb-2">
                    使用提示
                  </h3>
                  <ul className="text-xs text-gray-600 space-y-1">
                    <li>• 详细的描述能生成更好的视频</li>
                    <li>• 参考图片有助于提升视频质量</li>
                    <li>• 生成时间约为3-5分钟</li>
                    <li>• 可在历史记录中查看进度</li>
                  </ul>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </MainLayout>
    </ProtectedRoute>
  );
};

// 主导出组件，包装ToastProvider
const VideoGeneratePage: React.FC = () => {
  return (
    <ToastProvider>
      <VideoGeneratePageContent />
    </ToastProvider>
  );
};

export default VideoGeneratePage;
