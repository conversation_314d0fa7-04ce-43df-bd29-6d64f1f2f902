'use client';

import React from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui';
import { Home, ArrowLeft, Search, Video } from 'lucide-react';

const NotFoundPage: React.FC = () => {
  const router = useRouter();

  const handleGoBack = () => {
    if (window.history.length > 1) {
      router.back();
    } else {
      router.push('/');
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 to-pink-100 flex items-center justify-center p-4">
      <div className="max-w-md w-full text-center">
        {/* 404 图标 */}
        <div className="mb-8">
          <div className="relative">
            {/* 大数字 404 */}
            <div className="text-8xl font-bold text-purple-200 select-none">
              404
            </div>
            {/* 视频图标装饰 */}
            <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
              <Video className="h-16 w-16 text-purple-400" />
            </div>
          </div>
        </div>

        {/* 错误信息 */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            页面未找到
          </h1>
          <p className="text-gray-600 mb-2">
            抱歉，您访问的页面不存在或已被移动。
          </p>
          <p className="text-gray-500 text-sm">
            请检查URL是否正确，或使用下方按钮返回。
          </p>
        </div>

        {/* 操作按钮 */}
        <div className="space-y-4">
          {/* 主要操作 */}
          <div className="flex flex-col sm:flex-row gap-3 justify-center">
            <Button
              onClick={handleGoBack}
              variant="outline"
              leftIcon={<ArrowLeft className="h-4 w-4" />}
            >
              返回上页
            </Button>
            
            <Link href="/">
              <Button
                leftIcon={<Home className="h-4 w-4" />}
              >
                回到首页
              </Button>
            </Link>
          </div>

          {/* 次要操作 */}
          <div className="flex flex-col sm:flex-row gap-3 justify-center">
            <Link href="/dashboard">
              <Button
                variant="ghost"
                size="sm"
                leftIcon={<Video className="h-4 w-4" />}
              >
                工作台
              </Button>
            </Link>
            
            <Link href="/video/generate">
              <Button
                variant="ghost"
                size="sm"
                leftIcon={<Video className="h-4 w-4" />}
              >
                创建视频
              </Button>
            </Link>
          </div>
        </div>

        {/* 搜索建议 */}
        <div className="mt-12 p-6 bg-white rounded-lg shadow-sm border border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900 mb-3 flex items-center justify-center">
            <Search className="h-5 w-5 mr-2 text-purple-600" />
            您可能在寻找
          </h2>
          
          <div className="space-y-2 text-sm">
            <Link 
              href="/dashboard" 
              className="block text-purple-600 hover:text-purple-700 hover:underline"
            >
              • 用户工作台 - 查看您的项目和统计
            </Link>
            <Link 
              href="/video/generate" 
              className="block text-purple-600 hover:text-purple-700 hover:underline"
            >
              • 视频生成 - 创建新的AI视频
            </Link>
            <Link 
              href="/history" 
              className="block text-purple-600 hover:text-purple-700 hover:underline"
            >
              • 历史记录 - 管理您的视频任务
            </Link>
            <Link 
              href="/profile" 
              className="block text-purple-600 hover:text-purple-700 hover:underline"
            >
              • 个人中心 - 管理账户信息
            </Link>
          </div>
        </div>

        {/* 联系支持 */}
        <div className="mt-8 text-center">
          <p className="text-sm text-gray-500 mb-2">
            如果问题持续存在，请联系我们的技术支持
          </p>
          <div className="flex justify-center space-x-4 text-xs">
            <a 
              href="mailto:<EMAIL>" 
              className="text-purple-600 hover:text-purple-700 hover:underline"
            >
              邮箱支持
            </a>
            <span className="text-gray-300">|</span>
            <a 
              href="/help" 
              className="text-purple-600 hover:text-purple-700 hover:underline"
            >
              帮助中心
            </a>
          </div>
        </div>

        {/* 装饰元素 */}
        <div className="absolute top-10 left-10 w-20 h-20 bg-purple-200 rounded-full opacity-20 animate-pulse"></div>
        <div className="absolute bottom-10 right-10 w-16 h-16 bg-pink-200 rounded-full opacity-20 animate-pulse delay-1000"></div>
        <div className="absolute top-1/3 right-20 w-12 h-12 bg-blue-200 rounded-full opacity-20 animate-pulse delay-500"></div>
      </div>
    </div>
  );
};

export default NotFoundPage;
