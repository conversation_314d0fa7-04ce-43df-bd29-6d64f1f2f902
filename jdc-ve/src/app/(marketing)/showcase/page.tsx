'use client';

import React, { useState } from 'react';
import { Play, Heart, Download, Share2, Filter, Grid, List } from 'lucide-react';
import { Card, CardContent, Button } from '@/components/ui';

export default function ShowcasePage() {
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [selectedCategory, setSelectedCategory] = useState('all');

  const categories = [
    { id: 'all', name: '全部', count: 48 },
    { id: 'nature', name: '自然风光', count: 12 },
    { id: 'urban', name: '城市建筑', count: 8 },
    { id: 'people', name: '人物肖像', count: 10 },
    { id: 'abstract', name: '抽象艺术', count: 6 },
    { id: 'animals', name: '动物世界', count: 7 },
    { id: 'fantasy', name: '奇幻场景', count: 5 }
  ];

  const showcaseItems = [
    {
      id: 1,
      title: '梦幻森林中的精灵',
      description: '一个神秘的精灵在月光下的森林中翩翩起舞',
      category: 'fantasy',
      videoUrl: '/videos/sample1.mp4',
      thumbnailUrl: '/images/thumb1.jpg',
      prompt: '一个美丽的精灵在月光照耀的神秘森林中优雅地舞蹈，周围有萤火虫飞舞',
      model: 'Veo3',
      duration: '8秒',
      resolution: '1080P',
      likes: 234,
      downloads: 89,
      author: '创作者A',
      createdAt: '2024-01-15'
    },
    {
      id: 2,
      title: '未来城市的日出',
      description: '科幻风格的未来城市在日出时分的壮丽景象',
      category: 'urban',
      videoUrl: '/videos/sample2.mp4',
      thumbnailUrl: '/images/thumb2.jpg',
      prompt: '未来科幻城市的天际线，日出时分，高楼大厦反射着金色阳光',
      model: 'Veo3',
      duration: '10秒',
      resolution: '4K',
      likes: 456,
      downloads: 123,
      author: '创作者B',
      createdAt: '2024-01-14'
    },
    {
      id: 3,
      title: '海浪拍打礁石',
      description: '汹涌的海浪撞击海岸礁石的震撼场面',
      category: 'nature',
      videoUrl: '/videos/sample3.mp4',
      thumbnailUrl: '/images/thumb3.jpg',
      prompt: '巨大的海浪猛烈撞击海岸的黑色礁石，水花四溅，场面壮观',
      model: 'Veo3',
      duration: '6秒',
      resolution: '2K',
      likes: 189,
      downloads: 67,
      author: '创作者C',
      createdAt: '2024-01-13'
    },
    {
      id: 4,
      title: '抽象色彩流动',
      description: '彩色液体在黑色背景中的流动艺术',
      category: 'abstract',
      videoUrl: '/videos/sample4.mp4',
      thumbnailUrl: '/images/thumb4.jpg',
      prompt: '彩色液体在黑色背景中缓慢流动，形成美丽的抽象图案',
      model: 'Veo3',
      duration: '12秒',
      resolution: '1080P',
      likes: 312,
      downloads: 98,
      author: '创作者D',
      createdAt: '2024-01-12'
    },
    {
      id: 5,
      title: '雄鹰翱翔天空',
      description: '一只雄鹰在蓝天白云中自由翱翔',
      category: 'animals',
      videoUrl: '/videos/sample5.mp4',
      thumbnailUrl: '/images/thumb5.jpg',
      prompt: '一只威武的雄鹰在蔚蓝天空中展翅翱翔，白云朵朵',
      model: 'Veo3',
      duration: '8秒',
      resolution: '4K',
      likes: 278,
      downloads: 145,
      author: '创作者E',
      createdAt: '2024-01-11'
    },
    {
      id: 6,
      title: '优雅的芭蕾舞者',
      description: '一位芭蕾舞者在舞台上的优美表演',
      category: 'people',
      videoUrl: '/videos/sample6.mp4',
      thumbnailUrl: '/images/thumb6.jpg',
      prompt: '一位优雅的芭蕾舞者在聚光灯下的舞台上表演经典舞蹈动作',
      model: 'Veo3',
      duration: '10秒',
      resolution: '2K',
      likes: 567,
      downloads: 234,
      author: '创作者F',
      createdAt: '2024-01-10'
    }
  ];

  const filteredItems = selectedCategory === 'all'
    ? showcaseItems
    : showcaseItems.filter(item => item.category === selectedCategory);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-purple-600 to-pink-600 text-white py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              精彩作品展示
            </h1>
            <p className="text-xl mb-8 max-w-3xl mx-auto opacity-90">
              探索由AI生成的精美视频作品，获取创作灵感，发现无限可能
            </p>
            <Button variant="secondary" size="lg">
              开始创作
            </Button>
          </div>
        </div>
      </section>

      {/* Filter and View Controls */}
      <section className="py-8 bg-white border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-4">
            {/* Categories */}
            <div className="flex flex-wrap gap-2">
              {categories.map((category) => (
                <button
                  key={category.id}
                  onClick={() => setSelectedCategory(category.id)}
                  className={`px-4 py-2 rounded-full text-sm font-medium transition-colors ${
                    selectedCategory === category.id
                      ? 'bg-blue-600 text-white'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }`}
                >
                  {category.name} ({category.count})
                </button>
              ))}
            </div>

            {/* View Mode Toggle */}
            <div className="flex items-center gap-4">
              <div className="flex bg-gray-100 rounded-lg p-1">
                <button
                  onClick={() => setViewMode('grid')}
                  className={`p-2 rounded-md transition-colors ${
                    viewMode === 'grid'
                      ? 'bg-white text-blue-600 shadow-sm'
                      : 'text-gray-500 hover:text-gray-700'
                  }`}
                >
                  <Grid className="h-4 w-4" />
                </button>
                <button
                  onClick={() => setViewMode('list')}
                  className={`p-2 rounded-md transition-colors ${
                    viewMode === 'list'
                      ? 'bg-white text-blue-600 shadow-sm'
                      : 'text-gray-500 hover:text-gray-700'
                  }`}
                >
                  <List className="h-4 w-4" />
                </button>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Showcase Grid/List */}
      <section className="py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {viewMode === 'grid' ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredItems.map((item) => (
                <Card key={item.id} hover variant="elevated" className="overflow-hidden">
                  <div className="relative">
                    {/* Video Thumbnail */}
                    <div className="aspect-video bg-gray-200 relative group cursor-pointer">
                      <div className="absolute inset-0 bg-black bg-opacity-40 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
                        <Play className="h-12 w-12 text-white" />
                      </div>
                      <div className="absolute top-2 right-2 bg-black bg-opacity-60 text-white text-xs px-2 py-1 rounded">
                        {item.duration}
                      </div>
                    </div>

                    {/* Content */}
                    <CardContent>
                      <h3 className="text-lg font-semibold text-gray-900 mb-2">
                        {item.title}
                      </h3>
                      <p className="text-gray-600 text-sm mb-3 line-clamp-2">
                        {item.description}
                      </p>

                      {/* Meta Info */}
                      <div className="flex items-center justify-between text-xs text-gray-500 mb-3">
                        <span>{item.model}</span>
                        <span>{item.resolution}</span>
                      </div>

                      {/* Actions */}
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3 text-sm text-gray-500">
                          <span className="flex items-center gap-1">
                            <Heart className="h-4 w-4" />
                            {item.likes}
                          </span>
                          <span className="flex items-center gap-1">
                            <Download className="h-4 w-4" />
                            {item.downloads}
                          </span>
                        </div>
                        <Button size="sm" variant="ghost">
                          <Share2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </CardContent>
                  </div>
                </Card>
              ))}
            </div>
          ) : (
            <div className="space-y-4">
              {filteredItems.map((item) => (
                <Card key={item.id} hover className="overflow-hidden">
                  <div className="flex">
                    {/* Video Thumbnail */}
                    <div className="w-48 h-32 bg-gray-200 relative group cursor-pointer flex-shrink-0">
                      <div className="absolute inset-0 bg-black bg-opacity-40 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
                        <Play className="h-8 w-8 text-white" />
                      </div>
                      <div className="absolute top-2 right-2 bg-black bg-opacity-60 text-white text-xs px-2 py-1 rounded">
                        {item.duration}
                      </div>
                    </div>

                    {/* Content */}
                    <CardContent className="flex-1">
                      <div className="flex justify-between items-start mb-2">
                        <h3 className="text-xl font-semibold text-gray-900">
                          {item.title}
                        </h3>
                        <div className="flex items-center gap-2">
                          <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">
                            {item.model}
                          </span>
                          <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">
                            {item.resolution}
                          </span>
                        </div>
                      </div>

                      <p className="text-gray-600 mb-3">
                        {item.description}
                      </p>

                      <div className="text-sm text-gray-500 mb-3">
                        <strong>提示词：</strong>{item.prompt}
                      </div>

                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-4 text-sm text-gray-500">
                          <span>作者：{item.author}</span>
                          <span>{item.createdAt}</span>
                          <span className="flex items-center gap-1">
                            <Heart className="h-4 w-4" />
                            {item.likes}
                          </span>
                          <span className="flex items-center gap-1">
                            <Download className="h-4 w-4" />
                            {item.downloads}
                          </span>
                        </div>
                        <div className="flex gap-2">
                          <Button size="sm" variant="outline">
                            查看详情
                          </Button>
                          <Button size="sm" variant="ghost">
                            <Share2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </div>
                </Card>
              ))}
            </div>
          )}
        </div>
      </section>

      {/* Load More */}
      <section className="py-8 text-center">
        <Button variant="outline" size="lg">
          加载更多作品
        </Button>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-blue-600 to-purple-600">
        <div className="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl font-bold text-white mb-4">
            创作属于您的精彩作品
          </h2>
          <p className="text-xl text-blue-100 mb-8">
            受到启发了吗？立即开始您的AI视频创作之旅
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button variant="secondary" size="lg">
              立即创作
            </Button>
            <Button variant="outline" size="lg" className="text-white border-white hover:bg-white hover:text-blue-600">
              了解更多
            </Button>
          </div>
        </div>
      </section>
    </div>
  );
}