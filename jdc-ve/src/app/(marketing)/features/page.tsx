'use client';

import React from 'react';
import {
  Sparkles,
  Zap,
  Users,
  Video,
  Image as ImageIcon,
  Settings,
  Download,
  Share2,
  Clock,
  Star
} from 'lucide-react';
import { Card, CardHeader, CardContent, Button } from '@/components/ui';

export default function FeaturesPage() {
  const features = [
    {
      icon: <Sparkles className="h-8 w-8" />,
      title: '智能提示词增强',
      description: 'AI自动分析和优化您的描述，生成更精准、更具创意的视频内容',
      details: [
        '自然语言理解',
        '创意词汇扩展',
        '风格自动匹配',
        '场景智能补充'
      ]
    },
    {
      icon: <Video className="h-8 w-8" />,
      title: 'Veo3 专业模型',
      description: '采用Google最新的Veo3模型，提供业界领先的视频生成质量',
      details: [
        '最新Veo3技术',
        '超高画质输出',
        '精准动作控制',
        '自然场景理解'
      ]
    },
    {
      icon: <ImageIcon className="h-8 w-8" />,
      title: '图片转视频',
      description: '上传静态图片，AI智能生成动态视频效果',
      details: [
        '单图片动画化',
        '多图片序列',
        '风格保持一致',
        '运动轨迹控制'
      ]
    },
    {
      icon: <Settings className="h-8 w-8" />,
      title: '精细参数控制',
      description: '提供丰富的参数设置，让您完全掌控视频生成过程',
      details: [
        '视频比例调节',
        '时长自定义',
        '帧率控制',
        '质量等级选择'
      ]
    },
    {
      icon: <Zap className="h-8 w-8" />,
      title: '极速生成',
      description: '优化的算法和强大的计算资源，确保快速生成高质量视频',
      details: [
        '平均生成时间 < 2分钟',
        '并发处理支持',
        '优先级队列',
        '实时进度跟踪'
      ]
    },
    {
      icon: <Download className="h-8 w-8" />,
      title: '多格式导出',
      description: '支持多种视频格式和分辨率，适配不同平台需求',
      details: [
        'MP4/MOV/AVI格式',
        '4K/2K/1080P分辨率',
        '可调节码率',
        '批量下载'
      ]
    }
  ];

  const advantages = [
    {
      icon: <Clock className="h-6 w-6" />,
      title: '节省时间',
      description: '传统视频制作需要数小时甚至数天，AI生成只需几分钟'
    },
    {
      icon: <Star className="h-6 w-6" />,
      title: '专业品质',
      description: '媲美专业制作团队的视频质量，无需专业技能'
    },
    {
      icon: <Users className="h-6 w-6" />,
      title: '适用广泛',
      description: '适合营销、教育、娱乐等多个领域的视频内容创作'
    },
    {
      icon: <Share2 className="h-6 w-6" />,
      title: '易于分享',
      description: '一键分享到各大社交平台，扩大内容影响力'
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-blue-600 to-purple-600 text-white py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              强大的AI视频生成功能
            </h1>
            <p className="text-xl mb-8 max-w-3xl mx-auto opacity-90">
              探索我们的核心功能特性，了解如何通过AI技术革新您的视频创作流程
            </p>
            <Button variant="secondary" size="lg">
              立即体验
            </Button>
          </div>
        </div>
      </section>

      {/* Features Grid */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              核心功能特性
            </h2>
            <p className="text-lg text-gray-600">
              每一个功能都经过精心设计，为您提供最佳的创作体验
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <Card key={index} hover variant="elevated">
                <CardContent>
                  <div className="text-blue-600 mb-4">
                    {feature.icon}
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-3">
                    {feature.title}
                  </h3>
                  <p className="text-gray-600 mb-4">
                    {feature.description}
                  </p>
                  <ul className="space-y-2">
                    {feature.details.map((detail, idx) => (
                      <li key={idx} className="flex items-center text-sm text-gray-500">
                        <div className="w-1.5 h-1.5 bg-blue-600 rounded-full mr-2"></div>
                        {detail}
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Advantages Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              为什么选择我们？
            </h2>
            <p className="text-lg text-gray-600">
              相比传统视频制作方式，AI视频生成具有显著优势
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {advantages.map((advantage, index) => (
              <div key={index} className="text-center">
                <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <div className="text-blue-600">
                    {advantage.icon}
                  </div>
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  {advantage.title}
                </h3>
                <p className="text-gray-600">
                  {advantage.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Technical Specs */}
      <section className="py-20 bg-gray-100">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              技术规格
            </h2>
            <p className="text-lg text-gray-600">
              了解我们的技术参数和性能指标
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <Card>
              <CardContent>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                  支持的视频格式
                </h3>
                <ul className="space-y-2 text-gray-600">
                  <li>• MP4 (推荐)</li>
                  <li>• MOV</li>
                  <li>• AVI</li>
                  <li>• WebM</li>
                </ul>
              </CardContent>
            </Card>

            <Card>
              <CardContent>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                  分辨率选项
                </h3>
                <ul className="space-y-2 text-gray-600">
                  <li>• 4K (3840×2160)</li>
                  <li>• 2K (2560×1440)</li>
                  <li>• 1080P (1920×1080)</li>
                  <li>• 720P (1280×720)</li>
                </ul>
              </CardContent>
            </Card>

            <Card>
              <CardContent>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                  性能指标
                </h3>
                <ul className="space-y-2 text-gray-600">
                  <li>• 生成时间: 1-3分钟</li>
                  <li>• 并发处理: 支持</li>
                  <li>• 成功率: &gt;95%</li>
                  <li>• 可用性: 99.9%</li>
                </ul>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-blue-600">
        <div className="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl font-bold text-white mb-4">
            准备体验强大的AI视频生成功能？
          </h2>
          <p className="text-xl text-blue-100 mb-8">
            立即注册，免费体验所有功能特性
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button variant="secondary" size="lg">
              免费开始
            </Button>
            <Button variant="outline" size="lg" className="text-white border-white hover:bg-white hover:text-blue-600">
              查看定价
            </Button>
          </div>
        </div>
      </section>
    </div>
  );
}