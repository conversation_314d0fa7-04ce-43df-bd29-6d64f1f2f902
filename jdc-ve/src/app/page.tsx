'use client';

import React, { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { <PERSON>, Spark<PERSON>, Users, Zap } from 'lucide-react';
import { <PERSON><PERSON>, Card, CardHeader, CardContent } from '@/components/ui';
import { useAuthStore } from '@/store/auth';

export default function Home() {
  const router = useRouter();
  const { isAuthenticated, checkAuth } = useAuthStore();

  useEffect(() => {
    const initAuth = async () => {
      const isValid = await checkAuth();
      if (isValid) {
        // 已登录用户直接跳转到工作台
        router.push('/dashboard');
      }
    };

    initAuth();
  }, [checkAuth, router]);

  // 如果已登录，显示加载状态
  if (isAuthenticated) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">正在跳转到工作台...</p>
        </div>
      </div>
    );
  }
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Hero Section */}
      <section className="relative overflow-hidden">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
          <div className="text-center">
            <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
              AI视频生成
              <span className="text-blue-600 block">创意无限</span>
            </h1>
            <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
              基于Google最新Veo3模型，将您的创意想法转化为精美的视频内容。
              简单输入描述，即可生成专业级视频作品。
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button
                size="lg"
                leftIcon={<Play className="h-5 w-5" />}
                onClick={() => window.location.href = '/auth/login'}
              >
                立即体验
              </Button>
              <Button
                variant="outline"
                size="lg"
                onClick={() => window.location.href = '/showcase'}
              >
                查看案例展示
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              强大功能特性
            </h2>
            <p className="text-lg text-gray-600">
              为您提供专业级的AI视频生成体验
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <Card hover className="text-center">
              <CardContent>
                <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <Sparkles className="h-6 w-6 text-blue-600" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                  智能提示词增强
                </h3>
                <p className="text-gray-600">
                  AI自动优化您的描述，生成更精准、更具创意的视频内容
                </p>
              </CardContent>
            </Card>

            <Card hover className="text-center">
              <CardContent>
                <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <Zap className="h-6 w-6 text-green-600" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                  极速生成
                </h3>
                <p className="text-gray-600">
                  采用最新的AI模型，快速生成高质量视频，节省您的宝贵时间
                </p>
              </CardContent>
            </Card>

            <Card hover className="text-center">
              <CardContent>
                <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <Users className="h-6 w-6 text-purple-600" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                  Veo3技术
                </h3>
                <p className="text-gray-600">
                  采用Google最新Veo3模型，提供业界领先的视频生成质量
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-blue-600">
        <div className="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl font-bold text-white mb-4">
            准备开始您的创作之旅？
          </h2>
          <p className="text-xl text-blue-100 mb-8">
            立即注册，获得免费积分，体验AI视频生成的魅力
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button
              variant="secondary"
              size="lg"
              onClick={() => window.location.href = '/auth/register'}
            >
              免费注册
            </Button>
            <Button
              variant="outline"
              size="lg"
              className="text-white border-white hover:bg-white hover:text-blue-600"
              onClick={() => window.location.href = '/auth/login'}
            >
              立即登录
            </Button>
          </div>
        </div>
      </section>
    </div>
  );
}
