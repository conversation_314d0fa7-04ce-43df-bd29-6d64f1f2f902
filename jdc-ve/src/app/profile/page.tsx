'use client';

import React, { useState } from 'react';
import ProtectedRoute from '@/components/auth/ProtectedRoute';
import MainLayout from '@/components/layout/MainLayout';
import { useAuthStore } from '@/store/auth';
import { Card, CardContent, Button, Input } from '@/components/ui';
import { 
  User, 
  Mail, 
  Phone, 
  Calendar,
  MapPin,
  Edit3,
  Save,
  X,
  Camera,
  Shield,
  CreditCard,
  Settings
} from 'lucide-react';

const ProfilePage: React.FC = () => {
  const { user, updateUser } = useAuthStore();
  const [isEditing, setIsEditing] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState({
    nickname: user?.nickname || '',
    phone: user?.phone || '',
    birthday: user?.birthday || '',
    country: user?.country || '',
    province: user?.province || '',
    city: user?.city || '',
  });

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSave = async () => {
    setIsLoading(true);
    
    try {
      // TODO: 调用更新用户信息API
      console.log('更新用户信息:', formData);
      
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // 更新本地状态
      updateUser(formData);
      
      setIsEditing(false);
      alert('个人信息更新成功！');
      
    } catch (error) {
      console.error('更新失败:', error);
      alert('更新失败，请重试');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    setFormData({
      nickname: user?.nickname || '',
      phone: user?.phone || '',
      birthday: user?.birthday || '',
      country: user?.country || '',
      province: user?.province || '',
      city: user?.city || '',
    });
    setIsEditing(false);
  };

  const handleAvatarChange = () => {
    // TODO: 实现头像上传
    console.log('上传头像');
  };

  return (
    <ProtectedRoute>
      <MainLayout>
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* 页面标题 */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              个人中心
            </h1>
            <p className="text-gray-600">
              管理您的个人信息和账户设置
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* 左侧：个人信息 */}
            <div className="lg:col-span-2 space-y-6">
              {/* 基本信息卡片 */}
              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between mb-6">
                    <h2 className="text-xl font-semibold text-gray-900">
                      基本信息
                    </h2>
                    {!isEditing ? (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setIsEditing(true)}
                        leftIcon={<Edit3 className="h-4 w-4" />}
                      >
                        编辑
                      </Button>
                    ) : (
                      <div className="flex space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={handleCancel}
                          leftIcon={<X className="h-4 w-4" />}
                        >
                          取消
                        </Button>
                        <Button
                          size="sm"
                          onClick={handleSave}
                          disabled={isLoading}
                          leftIcon={<Save className="h-4 w-4" />}
                        >
                          {isLoading ? '保存中...' : '保存'}
                        </Button>
                      </div>
                    )}
                  </div>

                  {/* 头像区域 */}
                  <div className="flex items-center space-x-6 mb-8">
                    <div className="relative">
                      <div className="w-20 h-20 bg-purple-100 rounded-full flex items-center justify-center">
                        {user?.avatarUrl ? (
                          <img
                            src={user.avatarUrl}
                            alt="头像"
                            className="w-full h-full rounded-full object-cover"
                          />
                        ) : (
                          <User className="h-10 w-10 text-purple-600" />
                        )}
                      </div>
                      {isEditing && (
                        <button
                          onClick={handleAvatarChange}
                          className="absolute -bottom-1 -right-1 bg-purple-600 text-white rounded-full p-2 hover:bg-purple-700 transition-colors"
                        >
                          <Camera className="h-3 w-3" />
                        </button>
                      )}
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900">
                        {user?.nickname || user?.username || '用户'}
                      </h3>
                      <p className="text-gray-500">{user?.email}</p>
                      <p className="text-sm text-gray-400 mt-1">
                        注册时间：{user?.createTime || '2025-01-01'}
                      </p>
                    </div>
                  </div>

                  {/* 信息表单 */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        昵称
                      </label>
                      {isEditing ? (
                        <Input
                          value={formData.nickname}
                          onChange={(e) => handleInputChange('nickname', e.target.value)}
                          placeholder="请输入昵称"
                        />
                      ) : (
                        <p className="text-gray-900 py-2">
                          {user?.nickname || '未设置'}
                        </p>
                      )}
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        邮箱地址
                      </label>
                      <div className="flex items-center py-2">
                        <Mail className="h-4 w-4 text-gray-400 mr-2" />
                        <span className="text-gray-900">{user?.email}</span>
                        {user?.emailVerified && (
                          <span className="ml-2 text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full">
                            已验证
                          </span>
                        )}
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        手机号码
                      </label>
                      {isEditing ? (
                        <Input
                          value={formData.phone}
                          onChange={(e) => handleInputChange('phone', e.target.value)}
                          placeholder="请输入手机号码"
                          leftIcon={<Phone className="h-4 w-4" />}
                        />
                      ) : (
                        <p className="text-gray-900 py-2">
                          {user?.phone || '未设置'}
                        </p>
                      )}
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        生日
                      </label>
                      {isEditing ? (
                        <Input
                          type="date"
                          value={formData.birthday}
                          onChange={(e) => handleInputChange('birthday', e.target.value)}
                          leftIcon={<Calendar className="h-4 w-4" />}
                        />
                      ) : (
                        <p className="text-gray-900 py-2">
                          {user?.birthday || '未设置'}
                        </p>
                      )}
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        国家/地区
                      </label>
                      {isEditing ? (
                        <Input
                          value={formData.country}
                          onChange={(e) => handleInputChange('country', e.target.value)}
                          placeholder="请输入国家/地区"
                          leftIcon={<MapPin className="h-4 w-4" />}
                        />
                      ) : (
                        <p className="text-gray-900 py-2">
                          {user?.country || '未设置'}
                        </p>
                      )}
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        省份/城市
                      </label>
                      {isEditing ? (
                        <div className="flex space-x-2">
                          <Input
                            value={formData.province}
                            onChange={(e) => handleInputChange('province', e.target.value)}
                            placeholder="省份"
                            className="flex-1"
                          />
                          <Input
                            value={formData.city}
                            onChange={(e) => handleInputChange('city', e.target.value)}
                            placeholder="城市"
                            className="flex-1"
                          />
                        </div>
                      ) : (
                        <p className="text-gray-900 py-2">
                          {user?.province && user?.city 
                            ? `${user.province} ${user.city}` 
                            : '未设置'
                          }
                        </p>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* 安全设置 */}
              <Card>
                <CardContent className="p-6">
                  <h2 className="text-xl font-semibold text-gray-900 mb-6">
                    安全设置
                  </h2>
                  
                  <div className="space-y-4">
                    <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                      <div className="flex items-center">
                        <Shield className="h-5 w-5 text-blue-600 mr-3" />
                        <div>
                          <h3 className="font-medium text-gray-900">修改密码</h3>
                          <p className="text-sm text-gray-500">定期更新密码以保护账户安全</p>
                        </div>
                      </div>
                      <Button variant="outline" size="sm">
                        修改
                      </Button>
                    </div>

                    <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                      <div className="flex items-center">
                        <Mail className="h-5 w-5 text-green-600 mr-3" />
                        <div>
                          <h3 className="font-medium text-gray-900">邮箱验证</h3>
                          <p className="text-sm text-gray-500">
                            {user?.emailVerified ? '邮箱已验证' : '验证邮箱以提高账户安全性'}
                          </p>
                        </div>
                      </div>
                      {!user?.emailVerified && (
                        <Button variant="outline" size="sm">
                          验证
                        </Button>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* 右侧：快捷操作 */}
            <div className="space-y-6">
              {/* 账户统计 */}
              <Card>
                <CardContent className="p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">
                    账户统计
                  </h3>
                  
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <span className="text-gray-600">登录次数</span>
                      <span className="font-semibold">{user?.loginCount || 0}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-gray-600">最后登录</span>
                      <span className="text-sm text-gray-500">
                        {user?.lastLoginTime || '未知'}
                      </span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-gray-600">账户状态</span>
                      <span className="text-green-600 font-medium">正常</span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* 快捷操作 */}
              <Card>
                <CardContent className="p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">
                    快捷操作
                  </h3>
                  
                  <div className="space-y-3">
                    <Button
                      variant="outline"
                      fullWidth
                      leftIcon={<CreditCard className="h-4 w-4" />}
                      onClick={() => window.location.href = '/profile/points'}
                    >
                      积分管理
                    </Button>
                    
                    <Button
                      variant="outline"
                      fullWidth
                      leftIcon={<Settings className="h-4 w-4" />}
                      onClick={() => window.location.href = '/settings'}
                    >
                      账户设置
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </MainLayout>
    </ProtectedRoute>
  );
};

export default ProfilePage;
