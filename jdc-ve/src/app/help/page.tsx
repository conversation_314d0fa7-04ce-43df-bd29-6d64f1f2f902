'use client';

import React, { useState } from 'react';
import MainLayout from '@/components/layout/MainLayout';
import { Card, CardContent, Input } from '@/components/ui';
import { 
  Search, 
  Video, 
  CreditCard, 
  User, 
  Settings,
  ChevronDown,
  ChevronRight,
  Mail,
  MessageCircle,
  Phone
} from 'lucide-react';

interface FAQItem {
  id: string;
  category: string;
  question: string;
  answer: string;
  icon: React.ReactNode;
}

const HelpPage: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [expandedItems, setExpandedItems] = useState<string[]>([]);

  const faqData: FAQItem[] = [
    {
      id: '1',
      category: '视频生成',
      question: '如何创建我的第一个AI视频？',
      answer: '1. 登录您的账户\n2. 点击"创建视频"按钮\n3. 输入详细的视频描述\n4. 可选择上传参考图片\n5. 设置生成参数\n6. 点击"开始生成"等待完成',
      icon: <Video className="h-5 w-5 text-purple-600" />
    },
    {
      id: '2',
      category: '视频生成',
      question: '视频生成需要多长时间？',
      answer: '通常情况下，视频生成需要3-5分钟。具体时间取决于：\n• 视频复杂度\n• 是否启用高清增强\n• 当前系统负载\n• 参考图片数量',
      icon: <Video className="h-5 w-5 text-purple-600" />
    },
    {
      id: '3',
      category: '视频生成',
      question: '支持哪些视频格式和分辨率？',
      answer: '我们支持以下格式：\n• 格式：MP4 (H.264编码)\n• 分辨率：1280x720 (标准) / 1920x1080 (高清增强)\n• 帧率：24fps\n• 时长：5-10秒',
      icon: <Video className="h-5 w-5 text-purple-600" />
    },
    {
      id: '4',
      category: '积分系统',
      question: '积分是如何计算的？',
      answer: '积分消耗标准：\n• 基础视频生成：50积分\n• 高清增强：+30积分\n• 使用参考图片：+10积分/张\n• 智能优化描述：+20积分',
      icon: <CreditCard className="h-5 w-5 text-green-600" />
    },
    {
      id: '5',
      category: '积分系统',
      question: '如何获得更多积分？',
      answer: '获得积分的方式：\n• 新用户注册赠送：100积分\n• 每日签到：5-10积分\n• 邀请好友：50积分/人\n• 购买积分包：多种套餐可选',
      icon: <CreditCard className="h-5 w-5 text-green-600" />
    },
    {
      id: '6',
      category: '账户管理',
      question: '如何修改个人信息？',
      answer: '修改个人信息步骤：\n1. 进入"个人中心"\n2. 点击"编辑"按钮\n3. 修改需要更新的信息\n4. 点击"保存"确认更改',
      icon: <User className="h-5 w-5 text-blue-600" />
    },
    {
      id: '7',
      category: '账户管理',
      question: '忘记密码怎么办？',
      answer: '重置密码步骤：\n1. 在登录页点击"忘记密码"\n2. 输入注册邮箱\n3. 查收验证码邮件\n4. 输入验证码和新密码\n5. 完成密码重置',
      icon: <User className="h-5 w-5 text-blue-600" />
    },
    {
      id: '8',
      category: '技术支持',
      question: '视频生成失败怎么办？',
      answer: '如果遇到生成失败：\n• 检查网络连接\n• 确认积分余额充足\n• 简化视频描述\n• 减少参考图片数量\n• 联系客服获得帮助',
      icon: <Settings className="h-5 w-5 text-orange-600" />
    }
  ];

  const categories = Array.from(new Set(faqData.map(item => item.category)));

  const filteredFAQ = faqData.filter(item =>
    item.question.toLowerCase().includes(searchTerm.toLowerCase()) ||
    item.answer.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const toggleExpanded = (id: string) => {
    setExpandedItems(prev =>
      prev.includes(id)
        ? prev.filter(item => item !== id)
        : [...prev, id]
    );
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case '视频生成': return <Video className="h-5 w-5 text-purple-600" />;
      case '积分系统': return <CreditCard className="h-5 w-5 text-green-600" />;
      case '账户管理': return <User className="h-5 w-5 text-blue-600" />;
      case '技术支持': return <Settings className="h-5 w-5 text-orange-600" />;
      default: return <Settings className="h-5 w-5 text-gray-600" />;
    }
  };

  return (
    <MainLayout>
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* 页面标题 */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            帮助中心
          </h1>
          <p className="text-xl text-gray-600 mb-8">
            快速找到您需要的答案和解决方案
          </p>

          {/* 搜索框 */}
          <div className="max-w-md mx-auto">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              <Input
                type="text"
                placeholder="搜索问题或关键词..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 py-3 text-lg"
              />
            </div>
          </div>
        </div>

        {/* 快速导航 */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-12">
          {categories.map((category) => (
            <Card key={category} className="hover:shadow-md transition-shadow cursor-pointer">
              <CardContent className="p-6 text-center">
                <div className="flex justify-center mb-3">
                  {getCategoryIcon(category)}
                </div>
                <h3 className="font-semibold text-gray-900">{category}</h3>
                <p className="text-sm text-gray-500 mt-1">
                  {faqData.filter(item => item.category === category).length} 个问题
                </p>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* FAQ列表 */}
        <div className="space-y-4">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">
            常见问题
          </h2>

          {filteredFAQ.map((item) => (
            <Card key={item.id}>
              <CardContent className="p-0">
                <button
                  onClick={() => toggleExpanded(item.id)}
                  className="w-full p-6 text-left hover:bg-gray-50 transition-colors"
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      {item.icon}
                      <div>
                        <h3 className="font-semibold text-gray-900">
                          {item.question}
                        </h3>
                        <p className="text-sm text-gray-500 mt-1">
                          {item.category}
                        </p>
                      </div>
                    </div>
                    {expandedItems.includes(item.id) ? (
                      <ChevronDown className="h-5 w-5 text-gray-400" />
                    ) : (
                      <ChevronRight className="h-5 w-5 text-gray-400" />
                    )}
                  </div>
                </button>

                {expandedItems.includes(item.id) && (
                  <div className="px-6 pb-6 border-t border-gray-100">
                    <div className="pt-4">
                      <div className="prose prose-sm max-w-none">
                        {item.answer.split('\n').map((line, index) => (
                          <p key={index} className="text-gray-700 mb-2">
                            {line}
                          </p>
                        ))}
                      </div>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          ))}

          {filteredFAQ.length === 0 && (
            <div className="text-center py-12">
              <Search className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                没有找到相关问题
              </h3>
              <p className="text-gray-500">
                尝试使用不同的关键词搜索，或联系我们的客服团队
              </p>
            </div>
          )}
        </div>

        {/* 联系支持 */}
        <Card className="mt-12">
          <CardContent className="p-8 text-center">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">
              还有其他问题？
            </h2>
            <p className="text-gray-600 mb-8">
              我们的客服团队随时为您提供帮助
            </p>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center">
                <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                  <Mail className="h-6 w-6 text-blue-600" />
                </div>
                <h3 className="font-semibold text-gray-900 mb-2">邮件支持</h3>
                <p className="text-sm text-gray-500 mb-3">
                  通常在24小时内回复
                </p>
                <a
                  href="mailto:<EMAIL>"
                  className="text-blue-600 hover:text-blue-700 font-medium"
                >
                  <EMAIL>
                </a>
              </div>

              <div className="text-center">
                <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
                  <MessageCircle className="h-6 w-6 text-green-600" />
                </div>
                <h3 className="font-semibold text-gray-900 mb-2">在线客服</h3>
                <p className="text-sm text-gray-500 mb-3">
                  工作日 9:00-18:00
                </p>
                <button className="text-green-600 hover:text-green-700 font-medium">
                  开始对话
                </button>
              </div>

              <div className="text-center">
                <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-3">
                  <Phone className="h-6 w-6 text-purple-600" />
                </div>
                <h3 className="font-semibold text-gray-900 mb-2">电话支持</h3>
                <p className="text-sm text-gray-500 mb-3">
                  工作日 9:00-18:00
                </p>
                <a
                  href="tel:************"
                  className="text-purple-600 hover:text-purple-700 font-medium"
                >
                  ************
                </a>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  );
};

export default HelpPage;
