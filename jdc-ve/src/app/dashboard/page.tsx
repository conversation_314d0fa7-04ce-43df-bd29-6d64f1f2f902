'use client';

import React from 'react';
import ProtectedRoute from '@/components/auth/ProtectedRoute';
import MainLayout from '@/components/layout/MainLayout';
import { useAuthStore } from '@/store/auth';
import { Card, CardContent, Button } from '@/components/ui';
import { Video, Plus, Clock, CreditCard, TrendingUp } from 'lucide-react';
import { videoApi } from '@/lib/video-api';
import Link from 'next/link';

const DashboardPage: React.FC = () => {
  const { user } = useAuthStore();
  const [stats, setStats] = React.useState({
    totalVideos: 0,
    processingVideos: 0,
    availablePoints: 0,
    usedPoints: 0,
  });
  const [recentTasks, setRecentTasks] = React.useState<any[]>([]);
  const [loading, setLoading] = React.useState(true);

  // 加载仪表板数据
  React.useEffect(() => {
    const loadDashboardData = async () => {
      try {
        // 加载最近任务
        const tasksResult = await videoApi.getTaskList({
          page: 1,
          pageSize: 5
        });

        // 安全地处理API返回的数据
        console.log('Dashboard API返回数据:', tasksResult);
        const taskList = tasksResult?.data?.tasks || tasksResult?.list || tasksResult?.tasks || [];
        const totalCount = tasksResult?.data?.total || tasksResult?.total || 0;
        console.log('处理后的任务列表:', taskList);
        console.log('总数:', totalCount);

        setRecentTasks(taskList);

        // 计算统计数据
        const totalVideos = totalCount;
        const processingVideos = taskList.filter(task => task.status === 1).length;

        setStats({
          totalVideos,
          processingVideos,
          availablePoints: user?.points || 0,
          usedPoints: 0, // 这个需要从用户信息或单独的API获取
        });
      } catch (error) {
        console.error('加载仪表板数据失败:', error);
        // 使用默认值
        setStats({
          totalVideos: 0,
          processingVideos: 0,
          availablePoints: user?.points || 0,
          usedPoints: 0,
        });
        setRecentTasks([]); // 确保recentTasks是空数组而不是undefined
      } finally {
        setLoading(false);
      }
    };

    loadDashboardData();
  }, [user]);

  return (
    <ProtectedRoute>
      <MainLayout>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* 欢迎信息 */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900">
              欢迎回来，{user?.nickname || user?.username || '用户'}！
            </h1>
            <p className="text-gray-600 mt-2">
              在这里管理您的AI视频创作项目
            </p>
          </div>

          {/* 统计卡片 */}
          {loading ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
              {[1,2,3,4].map(i => (
                <Card key={i}>
                  <CardContent className="p-6">
                    <div className="animate-pulse">
                      <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                      <div className="h-8 bg-gray-200 rounded w-1/2"></div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center">
                    <div className="p-2 bg-purple-100 rounded-lg">
                      <Video className="h-6 w-6 text-purple-600" />
                    </div>
                    <div className="ml-4">
                      <p className="text-sm font-medium text-gray-600">总视频数</p>
                      <p className="text-2xl font-bold text-gray-900">{stats.totalVideos}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center">
                    <div className="p-2 bg-blue-100 rounded-lg">
                      <Clock className="h-6 w-6 text-blue-600" />
                    </div>
                    <div className="ml-4">
                      <p className="text-sm font-medium text-gray-600">处理中</p>
                      <p className="text-2xl font-bold text-gray-900">{stats.processingVideos}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center">
                    <div className="p-2 bg-green-100 rounded-lg">
                      <CreditCard className="h-6 w-6 text-green-600" />
                    </div>
                    <div className="ml-4">
                      <p className="text-sm font-medium text-gray-600">可用积分</p>
                      <p className="text-2xl font-bold text-gray-900">{stats.availablePoints}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center">
                    <div className="p-2 bg-orange-100 rounded-lg">
                      <TrendingUp className="h-6 w-6 text-orange-600" />
                    </div>
                    <div className="ml-4">
                      <p className="text-sm font-medium text-gray-600">已消费</p>
                      <p className="text-2xl font-bold text-gray-900">{stats.usedPoints}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}

          {/* 快速操作 */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
            <Link href="/video/generate">
              <Card className="hover:shadow-lg transition-shadow cursor-pointer">
                <CardContent className="p-6 text-center">
                  <div className="p-3 bg-purple-100 rounded-full w-fit mx-auto mb-4">
                    <Plus className="h-8 w-8 text-purple-600" />
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">创建新视频</h3>
                  <p className="text-gray-600">使用AI生成精美视频内容</p>
                </CardContent>
              </Card>
            </Link>

            <Link href="/history">
              <Card className="hover:shadow-lg transition-shadow cursor-pointer">
                <CardContent className="p-6 text-center">
                  <div className="p-3 bg-blue-100 rounded-full w-fit mx-auto mb-4">
                    <Video className="h-8 w-8 text-blue-600" />
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">历史记录</h3>
                  <p className="text-gray-600">查看和管理您的视频项目</p>
                </CardContent>
              </Card>
            </Link>
          </div>

          {/* 最近任务 */}
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-semibold text-gray-900">最近任务</h2>
                <Link href="/history">
                  <Button variant="ghost" size="sm">
                    查看全部
                  </Button>
                </Link>
              </div>

              {loading ? (
                <div className="space-y-4">
                  {[1,2,3].map(i => (
                    <div key={i} className="animate-pulse p-4 bg-gray-50 rounded-lg">
                      <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                      <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                    </div>
                  ))}
                </div>
              ) : (recentTasks && recentTasks.length > 0) ? (
                <div className="space-y-4">
                  {recentTasks.map((task: any) => (
                    <div key={task.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                      <div className="flex items-center space-x-4">
                        <div className="w-12 h-12 bg-gray-200 rounded-lg flex items-center justify-center">
                          <Video className="h-6 w-6 text-gray-500" />
                        </div>
                        <div>
                          <h3 className="font-medium text-gray-900">{task.prompt || '视频任务'}</h3>
                          <p className="text-sm text-gray-500">{task.createTime}</p>
                        </div>
                      </div>

                      <div className="flex items-center space-x-3">
                        {task.status === 1 && task.progress && (
                          <div className="text-sm text-gray-600">
                            {task.progress}%
                          </div>
                        )}
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          task.status === 2 ? 'text-green-600 bg-green-100' :
                          task.status === 1 ? 'text-blue-600 bg-blue-100' :
                          task.status === 3 ? 'text-red-600 bg-red-100' :
                          'text-gray-600 bg-gray-100'
                        }`}>
                          {task.status === 2 ? '已完成' :
                           task.status === 1 ? '处理中' :
                           task.status === 3 ? '失败' : '未知'}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <Video className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500">还没有视频任务</p>
                  <Link href="/video/generate">
                    <Button className="mt-4">
                      创建第一个视频
                    </Button>
                  </Link>
                </div>
              )}
            </CardContent>
          </Card>

          {/* 测试信息 */}
          <Card className="mt-8">
            <CardContent className="p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">系统状态</h2>
              <div className="space-y-2 text-sm">
                <p>✅ 用户认证: 正常</p>
                <p>✅ 页面渲染: 正常</p>
                <p>✅ 路由导航: 正常</p>
                <p className="text-green-600 font-medium">Dashboard 加载成功！</p>
              </div>
            </CardContent>
          </Card>
        </div>
      </MainLayout>
    </ProtectedRoute>
  );
};

export default DashboardPage;
