'use client';

import React, { useState, useEffect } from 'react';
import ProtectedRoute from '@/components/auth/ProtectedRoute';
import MainLayout from '@/components/layout/MainLayout';
import { Card, CardContent, Button, useToast, ToastProvider } from '@/components/ui';
import { CreditCard, TrendingUp, History, Plus } from 'lucide-react';
import { useAuthStore } from '@/store/auth';

// 积分记录类型
interface PointsRecord {
  id: string;
  type: 'consume' | 'recharge' | 'reward';
  amount: number;
  description: string;
  createTime: string;
  balance: number;
}

// 内部组件，在ToastProvider内部使用
const PointsPageContent: React.FC = () => {
  const { success, error: showError } = useToast();
  const { user } = useAuthStore();
  const [records, setRecords] = useState<PointsRecord[]>([]);
  const [loading, setLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  // 模拟积分记录数据
  const mockRecords: PointsRecord[] = [
    {
      id: '1',
      type: 'consume',
      amount: -10,
      description: '视频生成消费',
      createTime: '2025-01-05 14:30:00',
      balance: 990
    },
    {
      id: '2',
      type: 'recharge',
      amount: 100,
      description: '积分充值',
      createTime: '2025-01-05 10:00:00',
      balance: 1000
    },
    {
      id: '3',
      type: 'reward',
      amount: 50,
      description: '新用户奖励',
      createTime: '2025-01-04 16:00:00',
      balance: 900
    }
  ];

  // 加载积分记录
  const loadPointsHistory = async (page = 1) => {
    setLoading(true);
    try {
      // TODO: 替换为真实API调用
      // const result = await pointsApi.getPointsHistory({ page, pageSize: 10 });
      
      // 模拟API延迟
      await new Promise(resolve => setTimeout(resolve, 500));
      
      setRecords(mockRecords);
      setCurrentPage(page);
      setTotalPages(1);
    } catch (error) {
      console.error('加载积分记录失败:', error);
      showError('加载失败', '无法加载积分记录，请重试');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadPointsHistory();
  }, []);

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'consume': return 'text-red-600 bg-red-100';
      case 'recharge': return 'text-green-600 bg-green-100';
      case 'reward': return 'text-blue-600 bg-blue-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getTypeText = (type: string) => {
    switch (type) {
      case 'consume': return '消费';
      case 'recharge': return '充值';
      case 'reward': return '奖励';
      default: return '未知';
    }
  };

  return (
    <ProtectedRoute>
      <MainLayout>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* 页面标题 */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900">积分管理</h1>
            <p className="text-gray-600 mt-2">查看和管理您的积分余额</p>
          </div>

          {/* 积分概览 */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <div className="p-2 bg-blue-100 rounded-lg">
                    <CreditCard className="h-6 w-6 text-blue-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">当前余额</p>
                    <p className="text-2xl font-bold text-gray-900">{user?.points || 0}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <div className="p-2 bg-green-100 rounded-lg">
                    <TrendingUp className="h-6 w-6 text-green-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">本月消费</p>
                    <p className="text-2xl font-bold text-gray-900">50</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <div className="p-2 bg-purple-100 rounded-lg">
                    <Plus className="h-6 w-6 text-purple-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">充值</p>
                    <Button size="sm" className="mt-1">
                      立即充值
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* 积分记录 */}
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-semibold text-gray-900 flex items-center">
                  <History className="h-5 w-5 mr-2" />
                  积分记录
                </h2>
              </div>

              {loading ? (
                <div className="text-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600 mx-auto mb-4"></div>
                  <p className="text-gray-600">加载中...</p>
                </div>
              ) : records.length > 0 ? (
                <div className="space-y-4">
                  {records.map((record) => (
                    <div key={record.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                      <div className="flex items-center space-x-4">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getTypeColor(record.type)}`}>
                          {getTypeText(record.type)}
                        </span>
                        <div>
                          <p className="font-medium text-gray-900">{record.description}</p>
                          <p className="text-sm text-gray-500">{record.createTime}</p>
                        </div>
                      </div>
                      
                      <div className="text-right">
                        <p className={`font-semibold ${record.amount > 0 ? 'text-green-600' : 'text-red-600'}`}>
                          {record.amount > 0 ? '+' : ''}{record.amount}
                        </p>
                        <p className="text-sm text-gray-500">余额: {record.balance}</p>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <History className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500">暂无积分记录</p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </MainLayout>
    </ProtectedRoute>
  );
};

// 主导出组件，包装ToastProvider
const PointsPage: React.FC = () => {
  return (
    <ToastProvider>
      <PointsPageContent />
    </ToastProvider>
  );
};

export default PointsPage;
