'use client';

import React, { useState } from 'react';
import ProtectedRoute from '@/components/auth/ProtectedRoute';
import MainLayout from '@/components/layout/MainLayout';
import { Card, CardContent, Button, Input, useToast, ToastProvider } from '@/components/ui';
import { User, Mail, Lock, Bell, Shield, Save } from 'lucide-react';
import { useAuthStore } from '@/store/auth';

// 内部组件，在ToastProvider内部使用
const SettingsPageContent: React.FC = () => {
  const { success, error: showError } = useToast();
  const { user, updateUser } = useAuthStore();
  const [loading, setLoading] = useState(false);
  
  // 表单数据
  const [formData, setFormData] = useState({
    nickname: user?.nickname || '',
    email: user?.email || '',
    currentPassword: '',
    newPassword: '',
    confirmPassword: '',
    emailNotifications: true,
    systemNotifications: true
  });

  const handleInputChange = (field: string, value: string | boolean) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSaveProfile = async () => {
    setLoading(true);
    try {
      // TODO: 调用真实API
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // 更新用户信息
      updateUser({
        ...user!,
        nickname: formData.nickname,
        email: formData.email
      });
      
      success('保存成功', '个人信息已更新');
    } catch (error) {
      showError('保存失败', '更新个人信息失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  const handleChangePassword = async () => {
    if (!formData.currentPassword || !formData.newPassword) {
      showError('输入错误', '请填写当前密码和新密码');
      return;
    }
    
    if (formData.newPassword !== formData.confirmPassword) {
      showError('输入错误', '新密码和确认密码不一致');
      return;
    }

    setLoading(true);
    try {
      // TODO: 调用真实API
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // 清空密码字段
      setFormData(prev => ({
        ...prev,
        currentPassword: '',
        newPassword: '',
        confirmPassword: ''
      }));
      
      success('修改成功', '密码已更新');
    } catch (error) {
      showError('修改失败', '密码修改失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  return (
    <ProtectedRoute>
      <MainLayout>
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* 页面标题 */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900">账户设置</h1>
            <p className="text-gray-600 mt-2">管理您的账户信息和偏好设置</p>
          </div>

          <div className="space-y-6">
            {/* 个人信息 */}
            <Card>
              <CardContent className="p-6">
                <h2 className="text-xl font-semibold text-gray-900 mb-6 flex items-center">
                  <User className="h-5 w-5 mr-2" />
                  个人信息
                </h2>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      昵称
                    </label>
                    <Input
                      value={formData.nickname}
                      onChange={(e) => handleInputChange('nickname', e.target.value)}
                      placeholder="请输入昵称"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      邮箱
                    </label>
                    <Input
                      type="email"
                      value={formData.email}
                      onChange={(e) => handleInputChange('email', e.target.value)}
                      placeholder="请输入邮箱"
                    />
                  </div>
                </div>
                
                <div className="mt-6">
                  <Button 
                    onClick={handleSaveProfile}
                    disabled={loading}
                    className="flex items-center"
                  >
                    <Save className="h-4 w-4 mr-2" />
                    {loading ? '保存中...' : '保存信息'}
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* 密码修改 */}
            <Card>
              <CardContent className="p-6">
                <h2 className="text-xl font-semibold text-gray-900 mb-6 flex items-center">
                  <Lock className="h-5 w-5 mr-2" />
                  修改密码
                </h2>
                
                <div className="space-y-4 max-w-md">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      当前密码
                    </label>
                    <Input
                      type="password"
                      value={formData.currentPassword}
                      onChange={(e) => handleInputChange('currentPassword', e.target.value)}
                      placeholder="请输入当前密码"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      新密码
                    </label>
                    <Input
                      type="password"
                      value={formData.newPassword}
                      onChange={(e) => handleInputChange('newPassword', e.target.value)}
                      placeholder="请输入新密码"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      确认新密码
                    </label>
                    <Input
                      type="password"
                      value={formData.confirmPassword}
                      onChange={(e) => handleInputChange('confirmPassword', e.target.value)}
                      placeholder="请再次输入新密码"
                    />
                  </div>
                </div>
                
                <div className="mt-6">
                  <Button 
                    onClick={handleChangePassword}
                    disabled={loading}
                    variant="outline"
                    className="flex items-center"
                  >
                    <Shield className="h-4 w-4 mr-2" />
                    {loading ? '修改中...' : '修改密码'}
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* 通知设置 */}
            <Card>
              <CardContent className="p-6">
                <h2 className="text-xl font-semibold text-gray-900 mb-6 flex items-center">
                  <Bell className="h-5 w-5 mr-2" />
                  通知设置
                </h2>
                
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium text-gray-900">邮件通知</p>
                      <p className="text-sm text-gray-500">接收任务完成、积分变动等邮件通知</p>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        checked={formData.emailNotifications}
                        onChange={(e) => handleInputChange('emailNotifications', e.target.checked)}
                        className="sr-only peer"
                      />
                      <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-purple-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-purple-600"></div>
                    </label>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium text-gray-900">系统通知</p>
                      <p className="text-sm text-gray-500">接收系统维护、功能更新等通知</p>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        checked={formData.systemNotifications}
                        onChange={(e) => handleInputChange('systemNotifications', e.target.checked)}
                        className="sr-only peer"
                      />
                      <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-purple-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-purple-600"></div>
                    </label>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </MainLayout>
    </ProtectedRoute>
  );
};

// 主导出组件，包装ToastProvider
const SettingsPage: React.FC = () => {
  return (
    <ToastProvider>
      <SettingsPageContent />
    </ToastProvider>
  );
};

export default SettingsPage;
