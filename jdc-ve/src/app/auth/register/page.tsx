'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Video, ArrowLeft, Gift } from 'lucide-react';
import { Card, CardContent, Button } from '@/components/ui';
import RegisterForm from '@/components/forms/RegisterForm';
import { RegisterData } from '@/types';
import { authApi } from '@/lib/auth-api';

export default function RegisterPage() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [isSendingCode, setIsSendingCode] = useState(false);
  const [error, setError] = useState<string>('');

  const handleRegister = async (data: RegisterData) => {
    setIsLoading(true);
    setError('');

    try {
      // 调用注册API
      const result = await authApi.register(data);
      console.log('注册成功:', result);

      // 注册成功后跳转到登录页面
      router.push('/auth/login?registered=true');
    } catch (err) {
      setError(err instanceof Error ? err.message : '注册失败，请重试');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSendVerificationCode = async (email: string) => {
    setIsSendingCode(true);

    try {
      // 调用发送验证码API
      await authApi.sendVerificationCode(email, 1); // 1表示注册验证
      console.log('验证码已发送到:', email);

    } catch (err) {
      throw new Error(err instanceof Error ? err.message : '发送验证码失败');
    } finally {
      setIsSendingCode(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 to-pink-100 flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        {/* 返回首页按钮 */}
        <div className="mb-6">
          <Button
            variant="ghost"
            onClick={() => router.push('/')}
            leftIcon={<ArrowLeft className="h-4 w-4" />}
          >
            返回首页
          </Button>
        </div>

        {/* 注册卡片 */}
        <Card variant="elevated">
          <CardContent>
            {/* 头部 */}
            <div className="text-center mb-8">
              <div className="flex justify-center mb-4">
                <div className="w-12 h-12 bg-purple-600 rounded-lg flex items-center justify-center">
                  <Video className="h-6 w-6 text-white" />
                </div>
              </div>
              <h1 className="text-2xl font-bold text-gray-900 mb-2">
                创建账户
              </h1>
              <p className="text-gray-600">
                加入我们，开始您的AI视频创作之旅
              </p>
            </div>

            {/* 新用户福利提示 */}
            <div className="bg-gradient-to-r from-purple-100 to-pink-100 border border-purple-200 rounded-lg p-4 mb-6">
              <div className="flex items-center">
                <Gift className="h-5 w-5 text-purple-600 mr-2" />
                <div>
                  <p className="text-sm font-medium text-purple-800">
                    新用户福利
                  </p>
                  <p className="text-xs text-purple-600">
                    注册即送100积分，免费体验Veo3视频生成
                  </p>
                </div>
              </div>
            </div>

            {/* 注册表单 */}
            <RegisterForm
              onSubmit={handleRegister}
              onSendVerificationCode={handleSendVerificationCode}
              isLoading={isLoading}
              isSendingCode={isSendingCode}
              error={error}
            />
          </CardContent>
        </Card>

        {/* 底部信息 */}
        <div className="mt-6 text-center">
          <p className="text-sm text-gray-500">
            基于 Veo3 模型提供专业级AI视频生成服务
          </p>
        </div>
      </div>
    </div>
  );
}