'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { ArrowLeft, Mail, Lock, Shield } from 'lucide-react';
import { Card, CardContent, Button, Input, ToastProvider } from '@/components/ui';
import { authApi } from '@/lib/auth-api';

const resetPasswordSchema = z.object({
  email: z
    .string()
    .min(1, '请输入邮箱地址')
    .email('请输入有效的邮箱地址'),
  verificationCode: z
    .string()
    .min(1, '请输入验证码')
    .regex(/^\d{6}$/, '验证码必须为6位数字'),
  newPassword: z
    .string()
    .min(1, '请输入新密码')
    .min(6, '密码至少需要6位字符'),
  confirmPassword: z
    .string()
    .min(1, '请确认密码'),
}).refine((data) => data.newPassword === data.confirmPassword, {
  message: '两次输入的密码不一致',
  path: ['confirmPassword'],
});

type ResetPasswordFormData = z.infer<typeof resetPasswordSchema>;

const ResetPasswordPage: React.FC = () => {
  const router = useRouter();
  const [step, setStep] = useState<'email' | 'reset'>('email');
  const [isLoading, setIsLoading] = useState(false);
  const [isSendingCode, setIsSendingCode] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [email, setEmail] = useState('');
  const [countdown, setCountdown] = useState(0);

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
  } = useForm<ResetPasswordFormData>({
    resolver: zodResolver(resetPasswordSchema),
    defaultValues: {
      email: '',
      verificationCode: '',
      newPassword: '',
      confirmPassword: '',
    },
  });

  // 发送验证码
  const handleSendCode = async (emailValue: string) => {
    if (!emailValue || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(emailValue)) {
      setError('请输入有效的邮箱地址');
      return;
    }

    setIsSendingCode(true);
    setError('');

    try {
      await authApi.sendVerificationCode(emailValue, 2); // 2表示密码重置
      setEmail(emailValue);
      setValue('email', emailValue);
      setStep('reset');
      setSuccess('验证码已发送到您的邮箱');
      
      // 开始倒计时
      setCountdown(60);
      const timer = setInterval(() => {
        setCountdown((prev) => {
          if (prev <= 1) {
            clearInterval(timer);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);

    } catch (err) {
      setError(err instanceof Error ? err.message : '发送验证码失败');
    } finally {
      setIsSendingCode(false);
    }
  };

  // 重新发送验证码
  const handleResendCode = () => {
    if (countdown > 0) return;
    handleSendCode(email);
  };

  // 重置密码
  const handleResetPassword = async (data: ResetPasswordFormData) => {
    setIsLoading(true);
    setError('');

    try {
      await authApi.resetPassword(data.email, data.verificationCode, data.newPassword);
      setSuccess('密码重置成功！正在跳转到登录页...');
      
      setTimeout(() => {
        router.push('/auth/login?reset=success');
      }, 2000);

    } catch (err) {
      setError(err instanceof Error ? err.message : '密码重置失败，请重试');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <ToastProvider>
      <div className="min-h-screen bg-gradient-to-br from-green-50 to-blue-100 flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        {/* 返回按钮 */}
        <div className="mb-6">
          <Button
            variant="ghost"
            onClick={() => step === 'reset' ? setStep('email') : router.push('/auth/login')}
            leftIcon={<ArrowLeft className="h-4 w-4" />}
          >
            {step === 'reset' ? '返回上一步' : '返回登录'}
          </Button>
        </div>

        {/* 重置密码卡片 */}
        <Card variant="elevated">
          <CardContent>
            {/* 头部 */}
            <div className="text-center mb-8">
              <div className="flex justify-center mb-4">
                <div className="w-12 h-12 bg-green-600 rounded-lg flex items-center justify-center">
                  <Shield className="h-6 w-6 text-white" />
                </div>
              </div>
              <h1 className="text-2xl font-bold text-gray-900 mb-2">
                {step === 'email' ? '重置密码' : '设置新密码'}
              </h1>
              <p className="text-gray-600">
                {step === 'email' 
                  ? '输入您的邮箱地址，我们将发送验证码' 
                  : '输入验证码和新密码完成重置'
                }
              </p>
            </div>

            {/* 成功消息 */}
            {success && (
              <div className="mb-4 p-3 bg-green-100 border border-green-400 text-green-700 rounded-md text-sm">
                {success}
              </div>
            )}

            {/* 错误消息 */}
            {error && (
              <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded-md text-sm">
                {error}
              </div>
            )}

            {step === 'email' ? (
              /* 第一步：输入邮箱 */
              <div className="space-y-4">
                <Input
                  type="email"
                  placeholder="请输入您的邮箱地址"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  leftIcon={<Mail className="h-4 w-4" />}
                  error={!email && error ? '请输入邮箱地址' : ''}
                />

                <Button
                  onClick={() => handleSendCode(email)}
                  disabled={isSendingCode || !email}
                  fullWidth
                  isLoading={isSendingCode}
                >
                  {isSendingCode ? '发送中...' : '发送验证码'}
                </Button>
              </div>
            ) : (
              /* 第二步：重置密码 */
              <form onSubmit={handleSubmit(handleResetPassword)} className="space-y-4">
                {/* 邮箱显示 */}
                <div className="p-3 bg-gray-50 rounded-md">
                  <p className="text-sm text-gray-600">
                    验证码已发送至：<span className="font-medium text-gray-900">{email}</span>
                  </p>
                </div>

                {/* 验证码 */}
                <div>
                  <div className="flex space-x-2">
                    <Input
                      {...register('verificationCode')}
                      type="text"
                      placeholder="6位验证码"
                      maxLength={6}
                      error={errors.verificationCode?.message}
                      className="flex-1"
                    />
                    <Button
                      type="button"
                      variant="outline"
                      onClick={handleResendCode}
                      disabled={countdown > 0}
                      className="whitespace-nowrap"
                    >
                      {countdown > 0 ? `${countdown}s` : '重发'}
                    </Button>
                  </div>
                  {errors.verificationCode && (
                    <p className="mt-1 text-sm text-red-600">{errors.verificationCode.message}</p>
                  )}
                </div>

                {/* 新密码 */}
                <div>
                  <Input
                    {...register('newPassword')}
                    type="password"
                    placeholder="请输入新密码"
                    leftIcon={<Lock className="h-4 w-4" />}
                    error={errors.newPassword?.message}
                  />
                </div>

                {/* 确认密码 */}
                <div>
                  <Input
                    {...register('confirmPassword')}
                    type="password"
                    placeholder="请确认新密码"
                    leftIcon={<Lock className="h-4 w-4" />}
                    error={errors.confirmPassword?.message}
                  />
                </div>

                {/* 提交按钮 */}
                <Button
                  type="submit"
                  disabled={isLoading}
                  fullWidth
                  isLoading={isLoading}
                >
                  {isLoading ? '重置中...' : '重置密码'}
                </Button>
              </form>
            )}

            {/* 底部链接 */}
            <div className="mt-6 text-center">
              <p className="text-sm text-gray-500">
                想起密码了？
                <button
                  onClick={() => router.push('/auth/login')}
                  className="text-green-600 hover:text-green-700 font-medium ml-1"
                >
                  立即登录
                </button>
              </p>
            </div>
          </CardContent>
        </Card>

        {/* 底部信息 */}
        <div className="mt-6 text-center">
          <p className="text-sm text-gray-500">
            如果您没有收到验证码，请检查垃圾邮件文件夹
          </p>
        </div>
      </div>
    </ToastProvider>
  );
};

export default ResetPasswordPage;
