'use client';

import React, { useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Video, ArrowLeft } from 'lucide-react';
import { Card, CardContent, Button } from '@/components/ui';
import LoginForm from '@/components/forms/LoginForm';
import { LoginCredentials } from '@/types';
import { useAuthStore } from '@/store/auth';
import { useToast, ToastProvider } from '@/components/ui';

// 内部登录组件，在ToastProvider内部使用
function LoginPageContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { login } = useAuthStore();
  const { success, error: showError } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string>('');

  const handleLogin = async (data: LoginCredentials) => {
    setIsLoading(true);
    setError('');

    try {
      // 使用状态管理的登录方法
      await login(data.email, data.password, data.rememberMe);

      success('登录成功', '欢迎回来！正在跳转到工作台...');

      // 获取重定向URL或默认跳转到工作台
      const redirectTo = searchParams.get('redirect') || '/dashboard';

      console.log('登录成功，准备跳转到:', redirectTo);

      // 等待一小段时间确保状态更新完成
      setTimeout(() => {
        console.log('执行跳转到:', redirectTo);
        router.push(redirectTo);
      }, 100);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '登录失败，请重试';
      setError(errorMessage);
      showError('登录失败', errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        {/* 返回首页按钮 */}
        <div className="mb-6">
          <Button
            variant="ghost"
            onClick={() => router.push('/')}
            leftIcon={<ArrowLeft className="h-4 w-4" />}
          >
            返回首页
          </Button>
        </div>

        {/* 登录卡片 */}
        <Card variant="elevated">
          <CardContent>
            {/* 头部 */}
            <div className="text-center mb-8">
              <div className="flex justify-center mb-4">
                <div className="w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center">
                  <Video className="h-6 w-6 text-white" />
                </div>
              </div>
              <h1 className="text-2xl font-bold text-gray-900 mb-2">
                欢迎回来
              </h1>
              <p className="text-gray-600">
                登录您的账户，继续您的AI视频创作之旅
              </p>
            </div>

            {/* 登录表单 */}
            <LoginForm
              onSubmit={handleLogin}
              isLoading={isLoading}
              error={error}
            />
          </CardContent>
        </Card>

        {/* 底部信息 */}
        <div className="mt-6 text-center">
          <p className="text-sm text-gray-500">
            基于 Veo3 模型提供专业级AI视频生成服务
          </p>
        </div>
      </div>
    </div>
  );
}

// 主导出组件，包装ToastProvider
export default function LoginPage() {
  return (
    <ToastProvider>
      <LoginPageContent />
    </ToastProvider>
  );
}