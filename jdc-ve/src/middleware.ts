import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

// 需要认证的路由
const protectedRoutes = [
  '/dashboard',
  '/video',
  '/profile',
  '/settings',
  '/history',
];

// 认证相关路由（已登录用户不应访问）
const authRoutes = [
  '/auth/login',
  '/auth/register',
  '/auth/reset-password',
];

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // 暂时禁用中间件认证检查，因为Token存储在localStorage中
  // 认证检查由ProtectedRoute组件处理
  console.log('中间件检查路径:', pathname);

  return NextResponse.next();
}

export const config = {
  matcher: [
    /*
     * 匹配所有路径除了:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    '/((?!api|_next/static|_next/image|favicon.ico|public).*)',
  ],
};
