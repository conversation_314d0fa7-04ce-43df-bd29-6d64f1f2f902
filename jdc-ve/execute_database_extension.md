# JDC视频生成门户网站数据库扩展执行指南

## 📋 执行前准备

### 1. 环境检查
- 确保MySQL 8.x版本正常运行
- 确保InspiRealm系统数据库已存在并正常运行
- 备份现有数据库（重要！）

### 2. 备份命令
```bash
# 备份现有数据库
mysqldump -u [username] -p [database_name] > backup_$(date +%Y%m%d_%H%M%S).sql
```

## 🚀 执行步骤

### 步骤1：连接数据库
```bash
mysql -u [username] -p
USE [database_name];
```

### 步骤2：执行扩展脚本
```bash
# 在MySQL命令行中执行
source jdc_ve_database_extension.sql;
```

或者直接执行：
```bash
mysql -u [username] -p [database_name] < jdc_ve_database_extension.sql
```

### 步骤3：验证执行结果
```sql
-- 检查新增的表是否创建成功
SHOW TABLES LIKE 'web_%';
SHOW TABLES LIKE '%session%';
SHOW TABLES LIKE '%verification%';
SHOW TABLES LIKE 'video_generation_task';

-- 检查表结构
DESCRIBE web_user;
DESCRIBE user_session;
DESCRIBE email_verification;
DESCRIBE video_generation_task;
DESCRIBE web_user_points;

-- 检查系统配置是否插入成功
SELECT * FROM system_config WHERE config_key LIKE 'web.%' OR config_key LIKE 'veo3.%';
```

## 📊 新增表说明

### 1. web_user - Web端用户表
- 存储Web端用户的基础信息
- 与现有的user_info表区分（user_info用于微信小程序用户）
- 支持邮箱注册和密码登录

### 2. user_session - 用户会话表
- 管理Web端用户的登录会话
- 支持JWT Token和刷新Token
- 记录设备信息和登录历史

### 3. email_verification - 邮箱验证表
- 处理邮箱验证码
- 支持注册验证、密码重置等场景
- 防止验证码滥用

### 4. video_generation_task - 视频生成任务表
- 记录Veo3视频生成任务
- 跟踪任务状态和进度
- 存储生成参数和结果

### 5. web_user_points - Web端用户积分表
- 管理Web端用户的积分系统
- 与现有的user_points表并行存在
- 支持积分消费和充值

## ⚠️ 注意事项

### 1. 数据兼容性
- 新增表不会影响现有的微信小程序功能
- Web端和小程序端的用户数据相互独立
- 共享system_config配置表

### 2. 索引优化
- 所有表都已添加必要的索引
- 支持高并发查询和写入
- 考虑了查询性能优化

### 3. 安全考虑
- 密码使用BCrypt加密存储
- 会话Token有过期机制
- 邮箱验证有防刷机制

## 🔧 故障排除

### 常见问题
1. **表已存在错误**：脚本使用了`DROP TABLE IF EXISTS`，正常情况下不会出现
2. **权限不足**：确保数据库用户有CREATE、DROP、INSERT权限
3. **字符集问题**：确保数据库使用utf8mb4字符集

### 回滚方案
如果需要回滚，可以执行以下命令：
```sql
DROP TABLE IF EXISTS web_user;
DROP TABLE IF EXISTS user_session;
DROP TABLE IF EXISTS email_verification;
DROP TABLE IF EXISTS video_generation_task;
DROP TABLE IF EXISTS web_user_points;
DELETE FROM system_config WHERE config_key LIKE 'web.%' OR config_key LIKE 'veo3.%';
```

## ✅ 执行完成检查

执行完成后，应该能看到：
- 5个新增表创建成功
- 13个新的系统配置项插入成功
- 所有表的索引创建正常
- 现有数据不受影响