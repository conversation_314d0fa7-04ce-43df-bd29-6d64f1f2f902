# WeChatAuthController API接口文档

## 接口概述
微信认证控制器，提供微信小程序用户的登录和登出功能。

**基础路径**: `/api/wechat/auth`

## 接口列表

### 1. 微信登录

#### 接口信息
- **接口路径**: `POST /api/wechat/auth/login`
- **接口描述**: 通过微信授权码进行登录，返回用户信息和访问令牌
- **权限要求**: 无需登录（@NoLogin）

#### 请求参数
```json
{
    "code": "0123456789abcdef",
    "userInfo": {
        "nickName": "张三",
        "avatarUrl": "https://wx.qlogo.cn/mmopen/xxx",
        "gender": 1,
        "country": "中国",
        "province": "广东",
        "city": "深圳"
    }
}
```

**参数说明**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| code | String | 是 | 微信授权码 |
| userInfo | Object | 否 | 用户信息对象 |
| userInfo.nickName | String | 否 | 用户昵称 |
| userInfo.avatarUrl | String | 否 | 头像URL |
| userInfo.gender | Integer | 否 | 性别 0:未知 1:男 2:女 |
| userInfo.country | String | 否 | 国家 |
| userInfo.province | String | 否 | 省份 |
| userInfo.city | String | 否 | 城市 |

#### 响应结果
**成功响应**:
```json
{
    "code": 200,
    "message": "操作成功",
    "data": {
        "token": "abc123def456ghi789",
        "userInfo": {
            "id": 1,
            "nickname": "张三",
            "avatarUrl": "https://wx.qlogo.cn/mmopen/xxx",
            "availablePoints": 100
        },
        "isNewUser": true
    },
    "timestamp": 1690617600000
}
```

**失败响应**:
```json
{
    "code": 500,
    "message": "微信登录失败：invalid code",
    "data": null,
    "timestamp": 1690617600000
}
```

**响应参数说明**:
| 参数名 | 类型 | 说明 |
|--------|------|------|
| code | Integer | 响应码，200表示成功 |
| message | String | 响应消息 |
| data | Object | 响应数据 |
| data.token | String | 访问令牌 |
| data.userInfo | Object | 用户信息 |
| data.userInfo.id | Long | 用户ID |
| data.userInfo.nickname | String | 用户昵称 |
| data.userInfo.avatarUrl | String | 头像URL |
| data.userInfo.availablePoints | Integer | 可用积分 |
| data.isNewUser | Boolean | 是否新用户 |
| timestamp | Long | 时间戳 |

#### 业务逻辑
1. 通过微信授权码调用微信API获取用户openId
2. 根据openId查询用户是否已存在
3. 新用户：创建用户记录并初始化积分（默认100积分）
4. 老用户：更新用户信息和最后登录时间
5. 生成访问令牌并存储到Redis
6. 返回用户信息和令牌

#### 错误码说明
| 错误码 | 说明 |
|--------|------|
| 500 | 微信登录失败 |
| 500 | 用户注册失败 |
| 500 | 用户积分初始化失败 |

---

### 2. 用户登出

#### 接口信息
- **接口路径**: `POST /api/wechat/auth/logout`
- **接口描述**: 用户登出，清除服务端Token
- **权限要求**: 需要微信用户登录

#### 请求参数
无需请求体，通过Header传递Token

**请求头**:
```
Authorization: Bearer {token}
或
Token: {token}
```

#### 响应结果
**成功响应**:
```json
{
    "code": 200,
    "message": "操作成功",
    "data": null,
    "timestamp": 1690617600000
}
```

#### 业务逻辑
1. 从请求头获取Token
2. 清除Redis中的Token信息
3. 清除ThreadLocal中的用户上下文
4. 返回成功响应

---

## 使用示例

### JavaScript示例（微信小程序）
```javascript
// 微信登录
wx.login({
    success: function(res) {
        if (res.code) {
            // 获取用户信息
            wx.getUserProfile({
                desc: '用于完善用户资料',
                success: function(userRes) {
                    // 调用登录接口
                    wx.request({
                        url: 'https://api.example.com/api/wechat/auth/login',
                        method: 'POST',
                        data: {
                            code: res.code,
                            userInfo: {
                                nickName: userRes.userInfo.nickName,
                                avatarUrl: userRes.userInfo.avatarUrl,
                                gender: userRes.userInfo.gender,
                                country: userRes.userInfo.country,
                                province: userRes.userInfo.province,
                                city: userRes.userInfo.city
                            }
                        },
                        success: function(loginRes) {
                            if (loginRes.data.code === 200) {
                                // 保存token
                                wx.setStorageSync('token', loginRes.data.data.token);
                                console.log('登录成功', loginRes.data.data);
                            } else {
                                console.error('登录失败', loginRes.data.message);
                            }
                        }
                    });
                }
            });
        }
    }
});

// 用户登出
wx.request({
    url: 'https://api.example.com/api/wechat/auth/logout',
    method: 'POST',
    header: {
        'Token': wx.getStorageSync('token')
    },
    success: function(res) {
        if (res.data.code === 200) {
            // 清除本地token
            wx.removeStorageSync('token');
            console.log('登出成功');
        }
    }
});
```

### cURL示例
```bash
# 微信登录
curl -X POST "https://api.example.com/api/wechat/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "code": "0123456789abcdef",
    "userInfo": {
      "nickName": "张三",
      "avatarUrl": "https://wx.qlogo.cn/mmopen/xxx",
      "gender": 1,
      "country": "中国",
      "province": "广东",
      "city": "深圳"
    }
  }'

# 用户登出
curl -X POST "https://api.example.com/api/wechat/auth/logout" \
  -H "Token: abc123def456ghi789"
```

---

## 注意事项

1. **Token管理**: Token存储在Redis中，有效期为7天，每次使用会自动刷新过期时间
2. **新用户奖励**: 新用户注册会自动获得100积分奖励
3. **用户信息更新**: 每次登录会更新用户的基础信息和最后登录时间
4. **错误处理**: 所有异常都会被捕获并返回友好的错误信息
5. **日志记录**: 关键操作都有详细的日志记录，便于问题排查

---

**文档版本**: v1.0  
**创建时间**: 2024-07-29  
**更新时间**: 2024-07-29
