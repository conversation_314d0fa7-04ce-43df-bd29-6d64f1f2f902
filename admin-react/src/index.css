/* 全局样式重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB',
    'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif, 'Apple Color Emoji',
    'Segoe UI Emoji', 'Segoe UI Symbol';
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f5f7fa;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New', monospace;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 登录页面样式 */
.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.login-box {
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  padding: 40px;
  width: 100%;
  max-width: 400px;
  text-align: center;
}

.login-header {
  margin-bottom: 30px;
}

.login-header .logo {
  font-size: 48px;
  color: #667eea;
  margin-bottom: 16px;
}

.login-header h2 {
  color: #333;
  margin-bottom: 8px;
  font-weight: 600;
}

.login-header p {
  color: #666;
  font-size: 14px;
}

.login-footer {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #e1e5e9;
  color: #666;
  font-size: 14px;
}

/* 主布局样式 */
.main-layout {
  min-height: 100vh;
}

.main-layout .ant-layout-sider {
  background: #001529;
}

.main-layout .ant-layout-header {
  background: #fff;
  padding: 0 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.main-layout .ant-layout-content {
  margin: 24px;
  padding: 24px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  min-height: calc(100vh - 112px);
}

/* 侧边栏样式 */
.sidebar-logo {
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 20px;
  font-weight: 600;
  border-bottom: 1px solid #002140;
}

.sidebar-logo .logo-icon {
  margin-right: 8px;
  font-size: 24px;
  color: #1890ff;
}

/* 头部样式 */
.header-left h1 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #333;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.admin-info {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 6px;
  transition: background-color 0.2s;
}

.admin-info:hover {
  background-color: #f5f5f5;
}

.admin-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
}

/* 统计卡片样式 */
.stats-card {
  background: linear-gradient(135deg, var(--gradient-start) 0%, var(--gradient-end) 100%);
  border: none;
  border-radius: 12px;
  color: white;
  overflow: hidden;
  position: relative;
}

.stats-card::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 100px;
  height: 100px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  transform: translate(30px, -30px);
}

.stats-card .ant-card-body {
  padding: 24px;
}

.stats-card-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.stats-card-info h3 {
  color: white;
  font-size: 28px;
  font-weight: 700;
  margin: 0 0 8px 0;
}

.stats-card-info p {
  color: rgba(255, 255, 255, 0.8);
  margin: 0 0 4px 0;
  font-size: 16px;
}

.stats-card-info .change {
  color: rgba(255, 255, 255, 0.7);
  font-size: 14px;
}

.stats-card-icon {
  font-size: 48px;
  color: rgba(255, 255, 255, 0.3);
}

/* 统计卡片渐变色 */
.stats-card.users {
  --gradient-start: #667eea;
  --gradient-end: #764ba2;
}

.stats-card.images {
  --gradient-start: #f093fb;
  --gradient-end: #f5576c;
}

.stats-card.points {
  --gradient-start: #4facfe;
  --gradient-end: #00f2fe;
}

.stats-card.success {
  --gradient-start: #43e97b;
  --gradient-end: #38f9d7;
}

/* 图片卡片样式 */
.image-card {
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.image-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

.image-preview {
  width: 100%;
  height: 200px;
  object-fit: cover;
  cursor: pointer;
}

.image-info {
  padding: 16px;
}

.image-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.image-user {
  font-weight: 500;
  color: #333;
}

.image-size {
  color: #666;
  font-size: 12px;
}

.image-stats {
  display: flex;
  gap: 16px;
  margin-bottom: 12px;
  color: #666;
  font-size: 14px;
}

.image-stats span {
  display: flex;
  align-items: center;
  gap: 4px;
}

.image-actions {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

/* 系统状态样式 */
.system-status {
  display: grid;
  gap: 16px;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.status-item:last-child {
  border-bottom: none;
}

.status-label {
  font-weight: 500;
  color: #333;
}

/* 资源使用率样式 */
.resource-usage {
  display: grid;
  gap: 20px;
}

.usage-item {
  display: flex;
  align-items: center;
  gap: 16px;
}

.usage-label {
  width: 80px;
  font-weight: 500;
  color: #333;
  font-size: 14px;
}

.usage-progress {
  flex: 1;
}

.usage-value {
  width: 50px;
  text-align: right;
  font-weight: 600;
  color: #333;
  font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .main-layout .ant-layout-content {
    margin: 16px;
    padding: 16px;
  }
  
  .stats-card-content {
    flex-direction: column;
    text-align: center;
    gap: 16px;
  }
  
  .image-actions {
    justify-content: center;
  }
  
  .usage-item {
    flex-direction: column;
    gap: 8px;
  }
  
  .usage-label {
    width: auto;
  }
  
  .usage-value {
    width: auto;
  }
}
