import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  BarChart3,
  TrendingUp,
  Users,
  Image,
  Coins,
  Activity,
  Calendar,
  Download
} from 'lucide-react';
import { <PERSON><PERSON><PERSON>, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, LineChart, Line, PieChart, Pie, Cell } from 'recharts';
import { getSystemStats } from '../../services/api';

const ModernStats = () => {
  const [timeRange, setTimeRange] = useState('7days');
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState(null);
  const [error, setError] = useState(null);

  // 获取统计数据
  const fetchStats = async () => {
    try {
      setLoading(true);
      const response = await getSystemStats();
      setStats(response.data);
      setError(null);
    } catch (err) {
      console.error('获取统计数据失败:', err);
      setError('获取统计数据失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchStats();
  }, [timeRange]);

  // 生成图表数据
  const generateChartData = () => {
    if (!stats) return [];
    
    return [
      { 
        name: '今日', 
        users: stats.userStats?.todayNewUsers || 0, 
        images: stats.imageStats?.todayNewImages || 0,
        points: stats.pointsStats?.todayPointsIssued || 0
      },
      { 
        name: '本周', 
        users: stats.userStats?.weekNewUsers || 0, 
        images: stats.imageStats?.weekNewImages || 0,
        points: stats.pointsStats?.totalPointsIssued || 0
      },
      { 
        name: '本月', 
        users: stats.userStats?.monthNewUsers || 0, 
        images: stats.imageStats?.monthNewImages || 0,
        points: stats.pointsStats?.totalPointsConsumed || 0
      }
    ];
  };

  const generatePieData = () => {
    if (!stats) return [];
    
    return [
      { name: '公开图片', value: stats.imageStats?.publicImages || 0, color: '#3b82f6' },
      { name: '私有图片', value: stats.imageStats?.privateImages || 0, color: '#8b5cf6' },
      { name: '待审核', value: stats.imageStats?.pendingAuditImages || 0, color: '#f59e0b' }
    ];
  };

  const StatCard = ({ title, value, change, icon: Icon, color = 'blue' }) => {
    const colorClasses = {
      blue: 'from-blue-500 to-blue-600',
      green: 'from-green-500 to-green-600',
      yellow: 'from-yellow-500 to-yellow-600',
      purple: 'from-purple-500 to-purple-600'
    };

    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        whileHover={{ y: -4 }}
        className="bg-white rounded-xl shadow-soft border border-gray-200 p-6 relative overflow-hidden"
      >
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-gray-600 mb-1">{title}</p>
            <p className="text-3xl font-bold text-gray-900">{value?.toLocaleString() || 0}</p>
            {change && (
              <div className="flex items-center mt-2">
                <TrendingUp className="w-4 h-4 text-green-500 mr-1" />
                <span className="text-sm font-medium text-green-600">+{change}%</span>
                <span className="text-sm text-gray-500 ml-1">vs 上期</span>
              </div>
            )}
          </div>
          <div className={`w-16 h-16 rounded-2xl bg-gradient-to-br ${colorClasses[color]} flex items-center justify-center`}>
            <Icon className="w-8 h-8 text-white" />
          </div>
        </div>
      </motion.div>
    );
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="loading-spinner" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <p className="text-error-600 mb-4">{error}</p>
          <button onClick={fetchStats} className="btn-primary">
            重新加载
          </button>
        </div>
      </div>
    );
  }

  if (!stats) {
    return null;
  }

  const chartData = generateChartData();
  const pieData = generatePieData();

  return (
    <div className="space-y-8">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">数据统计</h1>
          <p className="text-gray-600 mt-2">系统运营数据分析</p>
        </div>
        <div className="flex items-center space-x-3">
          <select
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value)}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
          >
            <option value="7days">最近7天</option>
            <option value="30days">最近30天</option>
            <option value="90days">最近90天</option>
          </select>
        </div>
      </div>

      {/* 统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard
          title="总用户数"
          value={stats.userStats?.totalUsers}
          change={12.5}
          icon={Users}
          color="blue"
        />
        <StatCard
          title="总图片数"
          value={stats.imageStats?.totalImages}
          change={8.3}
          icon={Image}
          color="green"
        />
        <StatCard
          title="积分总量"
          value={stats.pointsStats?.totalPointsIssued}
          change={15.2}
          icon={Coins}
          color="yellow"
        />
        <StatCard
          title="成功率"
          value={stats.imageStats?.successRate}
          change={2.1}
          icon={TrendingUp}
          color="purple"
        />
      </div>

      {/* 图表区域 */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* 趋势图 */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          className="lg:col-span-2 bg-white rounded-xl shadow-soft border border-gray-200 p-6"
        >
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-gray-900">数据趋势</h3>
            <div className="flex items-center space-x-4 text-sm">
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                <span className="text-gray-600">用户</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                <span className="text-gray-600">图片</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                <span className="text-gray-600">积分</span>
              </div>
            </div>
          </div>
          <ResponsiveContainer width="100%" height={300}>
            <LineChart data={chartData}>
              <CartesianGrid strokeDasharray="3 3" stroke="#f1f5f9" />
              <XAxis dataKey="name" stroke="#64748b" fontSize={12} />
              <YAxis stroke="#64748b" fontSize={12} />
              <Tooltip 
                contentStyle={{ 
                  backgroundColor: 'white', 
                  border: 'none', 
                  borderRadius: '12px', 
                  boxShadow: '0 10px 40px -10px rgba(0, 0, 0, 0.1)' 
                }} 
              />
              <Line type="monotone" dataKey="users" stroke="#3b82f6" strokeWidth={3} dot={{ fill: '#3b82f6', strokeWidth: 2, r: 4 }} />
              <Line type="monotone" dataKey="images" stroke="#10b981" strokeWidth={3} dot={{ fill: '#10b981', strokeWidth: 2, r: 4 }} />
              <Line type="monotone" dataKey="points" stroke="#f59e0b" strokeWidth={3} dot={{ fill: '#f59e0b', strokeWidth: 2, r: 4 }} />
            </LineChart>
          </ResponsiveContainer>
        </motion.div>

        {/* 饼图 */}
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          className="bg-white rounded-xl shadow-soft border border-gray-200 p-6"
        >
          <h3 className="text-lg font-semibold text-gray-900 mb-6">图片分布</h3>
          <ResponsiveContainer width="100%" height={300}>
            <PieChart>
              <Pie
                data={pieData}
                cx="50%"
                cy="50%"
                innerRadius={60}
                outerRadius={100}
                paddingAngle={5}
                dataKey="value"
              >
                {pieData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <Tooltip />
            </PieChart>
          </ResponsiveContainer>
          <div className="grid grid-cols-1 gap-2 mt-4">
            {pieData.map((item, index) => (
              <div key={index} className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 rounded-full" style={{ backgroundColor: item.color }}></div>
                  <span className="text-sm text-gray-600">{item.name}</span>
                </div>
                <span className="text-sm font-medium text-gray-900">{item.value}</span>
              </div>
            ))}
          </div>
        </motion.div>
      </div>

      {/* 详细统计 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white rounded-xl shadow-soft border border-gray-200 p-6"
        >
          <h3 className="text-lg font-semibold text-gray-900 mb-4">用户统计</h3>
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-gray-600">总用户数</span>
              <span className="font-medium">{stats.userStats?.totalUsers || 0}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">今日新增</span>
              <span className="font-medium text-green-600">+{stats.userStats?.todayNewUsers || 0}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">本周新增</span>
              <span className="font-medium text-blue-600">+{stats.userStats?.weekNewUsers || 0}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">活跃用户</span>
              <span className="font-medium">{stats.userStats?.activeUsers || 0}</span>
            </div>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="bg-white rounded-xl shadow-soft border border-gray-200 p-6"
        >
          <h3 className="text-lg font-semibold text-gray-900 mb-4">图片统计</h3>
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-gray-600">总图片数</span>
              <span className="font-medium">{stats.imageStats?.totalImages || 0}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">今日新增</span>
              <span className="font-medium text-green-600">+{stats.imageStats?.todayNewImages || 0}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">公开图片</span>
              <span className="font-medium text-blue-600">{stats.imageStats?.publicImages || 0}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">成功率</span>
              <span className="font-medium">{stats.imageStats?.successRate || 0}%</span>
            </div>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="bg-white rounded-xl shadow-soft border border-gray-200 p-6"
        >
          <h3 className="text-lg font-semibold text-gray-900 mb-4">积分统计</h3>
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-gray-600">总发放量</span>
              <span className="font-medium">{stats.pointsStats?.totalPointsIssued || 0}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">总消费量</span>
              <span className="font-medium text-red-600">{stats.pointsStats?.totalPointsConsumed || 0}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">今日发放</span>
              <span className="font-medium text-green-600">+{stats.pointsStats?.todayPointsIssued || 0}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">平均积分</span>
              <span className="font-medium">{stats.pointsStats?.avgUserPoints || 0}</span>
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default ModernStats;
