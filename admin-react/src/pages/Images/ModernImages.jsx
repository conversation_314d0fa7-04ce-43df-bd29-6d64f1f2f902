import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  Image as ImageIcon,
  Search,
  Filter,
  Eye,
  Heart,
  Download,
  Trash2,
  CheckCircle,
  XCircle,
  Clock,
  User,
  ChevronLeft,
  ChevronRight
} from 'lucide-react';
import clsx from 'clsx';
import { getImageList, auditImage, deleteImage } from '../../services/api';
import ImageDetailModal from '../../components/ImageDetailModal';

const ModernImages = () => {
  const [images, setImages] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 12,
    total: 0
  });
  const [error, setError] = useState(null);
  const [selectedImageId, setSelectedImageId] = useState(null);
  const [isDetailModalOpen, setIsDetailModalOpen] = useState(false);

  // 获取图片列表
  const fetchImages = async () => {
    try {
      setLoading(true);
      const params = {
        page: pagination.current,
        size: pagination.pageSize,
        keyword: searchTerm || undefined,
        auditStatus: statusFilter === 'all' ? undefined : statusFilter
      };
      
      const response = await getImageList(params);
      setImages(response.data.records || []);
      setPagination(prev => ({
        ...prev,
        total: response.data.total || 0
      }));
      setError(null);
    } catch (err) {
      console.error('获取图片列表失败:', err);
      setError('获取图片列表失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchImages();
  }, [pagination.current, pagination.pageSize, searchTerm, statusFilter]);

  // 处理图片审核
  const handleAudit = async (imageId, auditStatus, auditRemark = '') => {
    try {
      // 如果是拒绝审核，必须填写拒绝原因
      if (auditStatus === 2) {
        const reason = auditRemark || prompt('请输入拒绝原因：');
        if (!reason || reason.trim() === '') {
          alert('拒绝审核必须填写原因');
          return;
        }
        await auditImage(imageId, auditStatus, reason.trim());
      } else {
        await auditImage(imageId, auditStatus, auditRemark || '审核通过');
      }
      fetchImages();
    } catch (err) {
      console.error('图片审核失败:', err);
      alert('审核操作失败，请重试');
    }
  };

  // 处理图片删除
  const handleDelete = async (imageId) => {
    if (!confirm('确定要删除这张图片吗？此操作不可恢复。')) {
      return;
    }
    
    try {
      await deleteImage(imageId);
      fetchImages();
    } catch (err) {
      console.error('删除图片失败:', err);
    }
  };

  // 处理搜索
  const handleSearch = (value) => {
    setSearchTerm(value);
    setPagination(prev => ({ ...prev, current: 1 }));
  };

  // 处理状态筛选
  const handleStatusFilter = (status) => {
    setStatusFilter(status);
    setPagination(prev => ({ ...prev, current: 1 }));
  };

  // 处理分页
  const handlePageChange = (page) => {
    setPagination(prev => ({ ...prev, current: page }));
  };

  // 处理图片点击
  const handleImageClick = (imageId) => {
    setSelectedImageId(imageId);
    setIsDetailModalOpen(true);
  };

  // 关闭详情模态框
  const handleCloseDetailModal = () => {
    setIsDetailModalOpen(false);
    setSelectedImageId(null);
  };

  const getStatusBadge = (auditStatus) => {
    const statusMap = {
      0: { text: '待审核', color: 'bg-yellow-100 text-yellow-800', icon: Clock },
      1: { text: '审核通过', color: 'bg-green-100 text-green-800', icon: CheckCircle },
      2: { text: '审核拒绝', color: 'bg-red-100 text-red-800', icon: XCircle }
    };
    
    const status = statusMap[auditStatus] || statusMap[0];
    const Icon = status.icon;
    
    return (
      <span className={clsx('inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium', status.color)}>
        <Icon className="w-3 h-3 mr-1" />
        {status.text}
      </span>
    );
  };

  // 分页组件
  const Pagination = () => {
    const totalPages = Math.ceil(pagination.total / pagination.pageSize);
    const startItem = (pagination.current - 1) * pagination.pageSize + 1;
    const endItem = Math.min(pagination.current * pagination.pageSize, pagination.total);

    return (
      <div className="flex items-center justify-between px-6 py-4 bg-white border-t border-gray-200">
        <div className="flex items-center space-x-4">
          <span className="text-sm text-gray-700">
            显示 {startItem} 到 {endItem} 条，共 {pagination.total} 条
          </span>
        </div>
        
        <div className="flex items-center space-x-2">
          <button
            onClick={() => handlePageChange(pagination.current - 1)}
            disabled={pagination.current <= 1}
            className={clsx(
              'p-2 rounded-lg border',
              pagination.current <= 1
                ? 'border-gray-200 text-gray-400 cursor-not-allowed'
                : 'border-gray-300 text-gray-700 hover:bg-gray-50'
            )}
          >
            <ChevronLeft className="w-4 h-4" />
          </button>
          
          {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
            let pageNum;
            if (totalPages <= 5) {
              pageNum = i + 1;
            } else if (pagination.current <= 3) {
              pageNum = i + 1;
            } else if (pagination.current >= totalPages - 2) {
              pageNum = totalPages - 4 + i;
            } else {
              pageNum = pagination.current - 2 + i;
            }
            
            return (
              <button
                key={pageNum}
                onClick={() => handlePageChange(pageNum)}
                className={clsx(
                  'px-3 py-2 rounded-lg text-sm font-medium',
                  pageNum === pagination.current
                    ? 'bg-primary-600 text-white'
                    : 'text-gray-700 hover:bg-gray-50'
                )}
              >
                {pageNum}
              </button>
            );
          })}
          
          <button
            onClick={() => handlePageChange(pagination.current + 1)}
            disabled={pagination.current >= totalPages}
            className={clsx(
              'p-2 rounded-lg border',
              pagination.current >= totalPages
                ? 'border-gray-200 text-gray-400 cursor-not-allowed'
                : 'border-gray-300 text-gray-700 hover:bg-gray-50'
            )}
          >
            <ChevronRight className="w-4 h-4" />
          </button>
        </div>
      </div>
    );
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="loading-spinner" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <p className="text-error-600 mb-4">{error}</p>
          <button onClick={fetchImages} className="btn-primary">
            重新加载
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">图片管理</h1>
          <p className="text-gray-600 mt-2">管理系统中的所有图片</p>
        </div>
        <div className="flex items-center space-x-3">
          <div className="flex items-center space-x-2 px-4 py-2 bg-purple-50 text-purple-700 rounded-lg">
            <ImageIcon className="w-5 h-5" />
            <span className="text-sm font-medium">总图片: {pagination.total}</span>
          </div>
        </div>
      </div>

      {/* 搜索和筛选 */}
      <div className="bg-white rounded-xl shadow-soft border border-gray-200 p-6">
        <div className="flex flex-col gap-6">
          {/* 搜索框 */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
            <input
              type="text"
              placeholder="搜索图片标题..."
              value={searchTerm}
              onChange={(e) => handleSearch(e.target.value)}
              className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            />
          </div>

          {/* 状态筛选 */}
          <div className="flex flex-col space-y-2">
            <label className="text-sm font-medium text-gray-700 flex items-center">
              <Filter className="w-4 h-4 mr-2 text-gray-500" />
              审核状态
            </label>
            <div className="flex flex-wrap gap-2">
              {[
                { value: 'all', label: '全部图片', icon: '📋' },
                { value: '0', label: '待审核', icon: '⏳' },
                { value: '1', label: '已通过', icon: '✅' },
                { value: '2', label: '已拒绝', icon: '❌' }
              ].map((status) => (
                <button
                  key={status.value}
                  onClick={() => handleStatusFilter(status.value)}
                  className={clsx(
                    'px-4 py-2 text-sm font-medium rounded-full transition-all duration-200 flex items-center space-x-2',
                    statusFilter === status.value
                      ? 'bg-primary-100 text-primary-700 ring-2 ring-primary-200'
                      : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                  )}
                >
                  <span>{status.icon}</span>
                  <span>{status.label}</span>
                </button>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* 图片网格 */}
      <div className="bg-white rounded-xl shadow-soft border border-gray-200 overflow-hidden">
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {images.map((image, index) => (
              <motion.div
                key={image.id}
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: index * 0.05 }}
                className="bg-white rounded-lg border border-gray-200 overflow-hidden hover:shadow-md transition-shadow duration-200"
              >
                <div
                  className="aspect-w-16 aspect-h-12 bg-gray-100 cursor-pointer group relative overflow-hidden"
                  onClick={() => handleImageClick(image.id)}
                >
                  <img
                    src={image.thumbnailUrl || image.imageUrl}
                    alt={image.title}
                    className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                    onError={(e) => {
                      e.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjE1MCIgdmlld0JveD0iMCAwIDIwMCAxNTAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMTUwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik04NyA2NUw5MyA3MUwxMDUgNTlMMTIzIDc3SDE3VjEyM0gxNzNWNzdIODdWNjVaIiBmaWxsPSIjOUNBM0FGIi8+CjxjaXJjbGUgY3g9IjE0NSIgY3k9IjM5IiByPSI5IiBmaWxsPSIjOUNBM0FGIi8+Cjx0ZXh0IHg9IjEwMCIgeT0iOTAiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGZpbGw9IiM5Q0EzQUYiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxMiI+5Zu+54mH5Yqg6L295aSx6LSlPC90ZXh0Pgo8L3N2Zz4K';
                    }}
                  />
                  {/* 悬停遮罩 */}
                  <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300 flex items-center justify-center">
                    <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                      <div className="bg-white bg-opacity-90 rounded-full p-2">
                        <Eye className="w-5 h-5 text-gray-700" />
                      </div>
                    </div>
                  </div>
                </div>
                
                <div className="p-4">
                  <div className="flex items-center justify-between mb-2">
                    <h3 className="text-sm font-medium text-gray-900 truncate flex-1">
                      {image.title || '未命名图片'}
                    </h3>
                    <div className="flex items-center space-x-2">
                      {/* 公开状态标签 */}
                      <span className={clsx(
                        'inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium',
                        image.publicStatusDesc === '公开'
                          ? 'bg-blue-100 text-blue-800'
                          : 'bg-gray-100 text-gray-800'
                      )}>
                        {image.publicStatusDesc === '公开' ? '🌐' : '🔒'} {image.publicStatusDesc || '私有'}
                      </span>
                      {/* 审核状态标签 */}
                      {getStatusBadge(image.auditStatus)}
                    </div>
                  </div>
                  
                  <div className="flex items-center text-xs text-gray-500 mb-3">
                    <User className="w-3 h-3 mr-1" />
                    <span className="truncate">{image.userNickname || '未知用户'}</span>
                  </div>

                  {/* 图片详细信息 */}
                  <div className="space-y-2 mb-3">
                    <div className="flex items-center justify-between text-xs text-gray-500">
                      <div className="flex items-center">
                        <Eye className="w-3 h-3 mr-1" />
                        {image.viewCount || 0}
                      </div>
                      <div className="flex items-center">
                        <Heart className="w-3 h-3 mr-1" />
                        {image.likeCount || 0}
                      </div>
                      <div className="flex items-center">
                        <Download className="w-3 h-3 mr-1" />
                        {image.downloadCount || 0}
                      </div>
                    </div>

                    {/* 图片尺寸和格式信息 */}
                    {(image.imageWidth || image.imageHeight || image.imageFormat) && (
                      <div className="text-xs text-gray-400 flex items-center justify-between">
                        <span>
                          {image.imageWidth && image.imageHeight && `${image.imageWidth}×${image.imageHeight}`}
                        </span>
                        <span className="uppercase">
                          {image.imageFormat}
                        </span>
                      </div>
                    )}

                    {/* 处理类型 */}
                    {image.processType && (
                      <div className="text-xs">
                        <span className="inline-flex items-center px-2 py-0.5 rounded bg-purple-100 text-purple-700">
                          🎨 {image.processType}
                        </span>
                      </div>
                    )}
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    {/* 只有待审核状态(0)且图片申请公开(isPublic=1)时才显示审核按钮 */}
                    {image.auditStatus === 0 && image.isPublic === 1 && (
                      <>
                        <button
                          onClick={() => handleAudit(image.id, 1, '审核通过')}
                          className="flex-1 inline-flex items-center justify-center px-2 py-1.5 text-xs font-medium text-green-700 bg-green-100 rounded hover:bg-green-200 transition-colors duration-200"
                          title="审核通过"
                        >
                          <CheckCircle className="w-3 h-3 mr-1" />
                          通过
                        </button>
                        <button
                          onClick={() => handleAudit(image.id, 2)}
                          className="flex-1 inline-flex items-center justify-center px-2 py-1.5 text-xs font-medium text-red-700 bg-red-100 rounded hover:bg-red-200 transition-colors duration-200"
                          title="审核拒绝"
                        >
                          <XCircle className="w-3 h-3 mr-1" />
                          拒绝
                        </button>
                      </>
                    )}

                    {/* 显示当前状态信息 */}
                    {image.auditStatus !== 0 && (
                      <div className="flex-1 text-xs text-gray-500">
                        {image.auditStatus === 1 ? '已审核通过' : '已审核拒绝'}
                        {image.auditRemark && (
                          <div className="text-xs text-gray-400 mt-1" title={image.auditRemark}>
                            {image.auditRemark.length > 20 ? image.auditRemark.substring(0, 20) + '...' : image.auditRemark}
                          </div>
                        )}
                      </div>
                    )}

                    <button
                      onClick={() => handleDelete(image.id)}
                      className="p-1.5 text-red-600 hover:bg-red-50 rounded transition-colors duration-200"
                      title="删除图片"
                    >
                      <Trash2 className="w-3 h-3" />
                    </button>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>

        {images.length === 0 && !loading && (
          <div className="text-center py-12">
            <ImageIcon className="w-16 h-16 text-gray-300 mx-auto mb-4" />
            <p className="text-gray-500 text-lg">没有找到匹配的图片</p>
          </div>
        )}

        {/* 分页 */}
        {pagination.total > 0 && <Pagination />}
      </div>

      {/* 图片详情模态框 */}
      <ImageDetailModal
        isOpen={isDetailModalOpen}
        onClose={handleCloseDetailModal}
        imageId={selectedImageId}
      />
    </div>
  );
};

export default ModernImages;
