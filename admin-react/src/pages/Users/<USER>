import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  Users,
  Search,
  Filter,
  MoreHorizontal,
  UserCheck,
  UserX,
  Coins,
  Calendar,
  MapPin,
  Phone,
  Mail,
  ChevronLeft,
  ChevronRight
} from 'lucide-react';
import clsx from 'clsx';
import { getUserList, updateUserStatus, manageUserPoints } from '../../services/api';

const ModernUsers = () => {
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0
  });
  const [error, setError] = useState(null);

  // 积分操作相关状态
  const [pointsModal, setPointsModal] = useState({
    visible: false,
    type: '', // 'add' 或 'deduct'
    userId: null,
    userName: '',
    amount: '',
    reason: ''
  });

  // 更多操作菜单状态
  const [activeDropdown, setActiveDropdown] = useState(null);

  // 获取用户列表
  const fetchUsers = async () => {
    try {
      setLoading(true);
      const params = {
        page: pagination.current,
        size: pagination.pageSize,
        keyword: searchTerm || undefined,
        status: statusFilter === 'all' ? undefined : (statusFilter === 'active' ? 1 : 0)
      };
      
      const response = await getUserList(params);
      setUsers(response.data.records || []);
      setPagination(prev => ({
        ...prev,
        total: response.data.total || 0
      }));
      setError(null);
    } catch (err) {
      console.error('获取用户列表失败:', err);
      setError('获取用户列表失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchUsers();
  }, [pagination.current, pagination.pageSize, searchTerm, statusFilter]);

  // 处理用户状态切换
  const handleStatusToggle = async (userId, currentStatus) => {
    try {
      const newStatus = currentStatus === 1 ? 0 : 1;
      const remark = newStatus === 0 ? '管理员禁用' : '管理员启用';
      
      await updateUserStatus(userId, { status: newStatus, remark });
      fetchUsers();
    } catch (err) {
      console.error('更新用户状态失败:', err);
    }
  };

  // 处理搜索
  const handleSearch = (value) => {
    setSearchTerm(value);
    setPagination(prev => ({ ...prev, current: 1 }));
  };

  // 处理状态筛选
  const handleStatusFilter = (status) => {
    setStatusFilter(status);
    setPagination(prev => ({ ...prev, current: 1 }));
  };

  // 处理分页
  const handlePageChange = (page) => {
    setPagination(prev => ({ ...prev, current: page }));
  };

  const handlePageSizeChange = (size) => {
    setPagination(prev => ({ ...prev, pageSize: size, current: 1 }));
  };

  // 打开积分操作弹窗
  const openPointsModal = (type, userId, userName) => {
    setPointsModal({
      visible: true,
      type,
      userId,
      userName,
      amount: '',
      reason: ''
    });
    setActiveDropdown(null); // 关闭下拉菜单
  };

  // 关闭积分操作弹窗
  const closePointsModal = () => {
    setPointsModal({
      visible: false,
      type: '',
      userId: null,
      userName: '',
      amount: '',
      reason: ''
    });
  };

  // 处理积分操作
  const handlePointsOperation = async () => {
    const { type, userId, amount, reason } = pointsModal;

    if (!amount || !reason) {
      alert('请填写积分数量和操作原因');
      return;
    }

    if (isNaN(amount) || Number(amount) <= 0) {
      alert('请输入有效的积分数量');
      return;
    }

    try {
      const operationType = type === 'add' ? 1 : 2; // 1-增加, 2-扣除
      await manageUserPoints(userId, {
        operationType,
        pointsAmount: Number(amount),
        remark: reason
      });

      alert(`积分${type === 'add' ? '增加' : '扣除'}成功`);
      closePointsModal();
      fetchUsers(); // 刷新用户列表
    } catch (err) {
      console.error('积分操作失败:', err);
      alert('积分操作失败，请重试');
    }
  };

  // 切换下拉菜单
  const toggleDropdown = (userId) => {
    setActiveDropdown(activeDropdown === userId ? null : userId);
  };

  // 点击外部关闭下拉菜单
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (activeDropdown && !event.target.closest('.dropdown-container')) {
        setActiveDropdown(null);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [activeDropdown]);

  const StatusBadge = ({ status }) => (
    <span className={clsx(
      'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
      status === 1 
        ? 'bg-green-100 text-green-800' 
        : 'bg-red-100 text-red-800'
    )}>
      {status === 1 ? '正常' : '禁用'}
    </span>
  );

  // 分页组件
  const Pagination = () => {
    const totalPages = Math.ceil(pagination.total / pagination.pageSize);
    const startItem = (pagination.current - 1) * pagination.pageSize + 1;
    const endItem = Math.min(pagination.current * pagination.pageSize, pagination.total);

    return (
      <div className="flex items-center justify-between px-6 py-4 bg-white border-t border-gray-200">
        <div className="flex items-center space-x-4">
          <span className="text-sm text-gray-700">
            显示 {startItem} 到 {endItem} 条，共 {pagination.total} 条
          </span>
          <select
            value={pagination.pageSize}
            onChange={(e) => handlePageSizeChange(Number(e.target.value))}
            className="text-sm border border-gray-300 rounded px-2 py-1"
          >
            <option value={10}>10条/页</option>
            <option value={20}>20条/页</option>
            <option value={50}>50条/页</option>
          </select>
        </div>
        
        <div className="flex items-center space-x-2">
          <button
            onClick={() => handlePageChange(pagination.current - 1)}
            disabled={pagination.current <= 1}
            className={clsx(
              'p-2 rounded-lg border',
              pagination.current <= 1
                ? 'border-gray-200 text-gray-400 cursor-not-allowed'
                : 'border-gray-300 text-gray-700 hover:bg-gray-50'
            )}
          >
            <ChevronLeft className="w-4 h-4" />
          </button>
          
          {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
            let pageNum;
            if (totalPages <= 5) {
              pageNum = i + 1;
            } else if (pagination.current <= 3) {
              pageNum = i + 1;
            } else if (pagination.current >= totalPages - 2) {
              pageNum = totalPages - 4 + i;
            } else {
              pageNum = pagination.current - 2 + i;
            }
            
            return (
              <button
                key={pageNum}
                onClick={() => handlePageChange(pageNum)}
                className={clsx(
                  'px-3 py-2 rounded-lg text-sm font-medium',
                  pageNum === pagination.current
                    ? 'bg-primary-600 text-white'
                    : 'text-gray-700 hover:bg-gray-50'
                )}
              >
                {pageNum}
              </button>
            );
          })}
          
          <button
            onClick={() => handlePageChange(pagination.current + 1)}
            disabled={pagination.current >= totalPages}
            className={clsx(
              'p-2 rounded-lg border',
              pagination.current >= totalPages
                ? 'border-gray-200 text-gray-400 cursor-not-allowed'
                : 'border-gray-300 text-gray-700 hover:bg-gray-50'
            )}
          >
            <ChevronRight className="w-4 h-4" />
          </button>
        </div>
      </div>
    );
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="loading-spinner" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <p className="text-error-600 mb-4">{error}</p>
          <button onClick={fetchUsers} className="btn-primary">
            重新加载
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">用户管理</h1>
          <p className="text-gray-600 mt-2">管理系统中的所有用户</p>
        </div>
        <div className="flex items-center space-x-3">
          <div className="flex items-center space-x-2 px-4 py-2 bg-blue-50 text-blue-700 rounded-lg">
            <Users className="w-5 h-5" />
            <span className="text-sm font-medium">总用户: {pagination.total}</span>
          </div>
        </div>
      </div>

      {/* 搜索和筛选 */}
      <div className="bg-white rounded-xl shadow-soft border border-gray-200 p-6">
        <div className="flex flex-col gap-6">
          {/* 搜索框 */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
            <input
              type="text"
              placeholder="搜索用户昵称或邮箱..."
              value={searchTerm}
              onChange={(e) => handleSearch(e.target.value)}
              className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            />
          </div>

          {/* 状态筛选 */}
          <div className="flex flex-col space-y-2">
            <label className="text-sm font-medium text-gray-700 flex items-center">
              <Filter className="w-4 h-4 mr-2 text-gray-500" />
              用户状态
            </label>
            <div className="flex flex-wrap gap-2">
              {[
                { value: 'all', label: '全部用户', icon: '👥' },
                { value: 'active', label: '正常', icon: '✅' },
                { value: 'inactive', label: '禁用', icon: '🚫' }
              ].map((status) => (
                <button
                  key={status.value}
                  onClick={() => handleStatusFilter(status.value)}
                  className={clsx(
                    'px-4 py-2 text-sm font-medium rounded-full transition-all duration-200 flex items-center space-x-2',
                    statusFilter === status.value
                      ? 'bg-green-100 text-green-700 ring-2 ring-green-200'
                      : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                  )}
                >
                  <span>{status.icon}</span>
                  <span>{status.label}</span>
                </button>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* 用户列表 */}
      <div className="bg-white rounded-xl shadow-soft border border-gray-200 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50 border-b border-gray-200">
              <tr>
                <th className="text-left py-4 px-6 font-semibold text-gray-900">用户信息</th>
                <th className="text-left py-4 px-6 font-semibold text-gray-900">联系方式</th>
                <th className="text-left py-4 px-6 font-semibold text-gray-900">积分信息</th>
                <th className="text-left py-4 px-6 font-semibold text-gray-900">状态</th>
                <th className="text-left py-4 px-6 font-semibold text-gray-900">最后登录</th>
                <th className="text-left py-4 px-6 font-semibold text-gray-900">注册时间</th>
                <th className="text-left py-4 px-6 font-semibold text-gray-900">操作</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200">
              {users.map((user, index) => (
                <motion.tr
                  key={user.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.05 }}
                  className="hover:bg-gray-50 transition-colors duration-200"
                >
                  <td className="py-4 px-6">
                    <div className="flex items-center space-x-4">
                      <img
                        src={user.avatarUrl || `https://ui-avatars.com/api/?name=${encodeURIComponent(user.nickname)}&background=3b82f6&color=fff`}
                        alt={user.nickname}
                        className="w-12 h-12 rounded-full object-cover"
                      />
                      <div>
                        <div className="font-medium text-gray-900">{user.nickname}</div>
                        <div className="text-sm text-gray-500 flex items-center mt-1">
                          <MapPin className="w-4 h-4 mr-1" />
                          {user.city || '未知'}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="py-4 px-6">
                    <div className="space-y-2">
                      <div className="text-sm flex items-center">
                        <Phone className="w-4 h-4 mr-2 text-gray-400" />
                        {user.phone || '未绑定'}
                      </div>
                      <div className="text-sm flex items-center">
                        <Mail className="w-4 h-4 mr-2 text-gray-400" />
                        {user.email || '未绑定'}
                      </div>
                    </div>
                  </td>
                  <td className="py-4 px-6">
                    <div className="space-y-1">
                      <div className="text-sm font-medium flex items-center">
                        <Coins className="w-4 h-4 mr-1 text-yellow-500" />
                        总积分: {user.totalPoints || 0}
                      </div>
                      <div className="text-sm text-gray-500">
                        可用: {user.availablePoints || 0}
                      </div>
                    </div>
                  </td>
                  <td className="py-4 px-6">
                    <StatusBadge status={user.status} />
                  </td>
                  <td className="py-4 px-6">
                    <div className="text-sm text-gray-900 flex items-center">
                      <Calendar className="w-4 h-4 mr-1 text-gray-400" />
                      {user.lastLoginTime || '从未登录'}
                    </div>
                  </td>
                  <td className="py-4 px-6">
                    <div className="text-sm text-gray-500">
                      {user.createTime}
                    </div>
                  </td>
                  <td className="py-4 px-6">
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => handleStatusToggle(user.id, user.status)}
                        className={clsx(
                          'inline-flex items-center px-3 py-1.5 text-xs font-medium rounded-lg transition-colors duration-200',
                          user.status === 1 
                            ? 'bg-red-100 text-red-700 hover:bg-red-200' 
                            : 'bg-green-100 text-green-700 hover:bg-green-200'
                        )}
                      >
                        {user.status === 1 ? (
                          <>
                            <UserX className="w-3 h-3 mr-1" />
                            禁用
                          </>
                        ) : (
                          <>
                            <UserCheck className="w-3 h-3 mr-1" />
                            启用
                          </>
                        )}
                      </button>
                      <div className="relative dropdown-container">
                        <button
                          className="p-1.5 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100"
                          onClick={() => toggleDropdown(user.id)}
                        >
                          <MoreHorizontal className="w-4 h-4" />
                        </button>

                        {/* 下拉菜单 */}
                        {activeDropdown === user.id && (
                          <div className="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 z-10">
                            <div className="py-1">
                              <button
                                className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
                                onClick={() => openPointsModal('add', user.id, user.nickname)}
                              >
                                <Coins className="w-4 h-4 mr-2 text-green-500" />
                                增加积分
                              </button>
                              <button
                                className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
                                onClick={() => openPointsModal('deduct', user.id, user.nickname)}
                              >
                                <Coins className="w-4 h-4 mr-2 text-red-500" />
                                扣除积分
                              </button>
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  </td>
                </motion.tr>
              ))}
            </tbody>
          </table>
        </div>

        {users.length === 0 && !loading && (
          <div className="text-center py-12">
            <Users className="w-16 h-16 text-gray-300 mx-auto mb-4" />
            <p className="text-gray-500 text-lg">没有找到匹配的用户</p>
          </div>
        )}

        {/* 分页 */}
        {pagination.total > 0 && <Pagination />}
      </div>

      {/* 积分操作弹窗 */}
      {pointsModal.visible && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-96 max-w-md mx-4">
            <h3 className="text-lg font-semibold mb-4">
              {pointsModal.type === 'add' ? '增加积分' : '扣除积分'}
            </h3>

            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                用户
              </label>
              <input
                type="text"
                value={pointsModal.userName}
                disabled
                className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50"
              />
            </div>

            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                积分数量
              </label>
              <input
                type="number"
                value={pointsModal.amount}
                onChange={(e) => setPointsModal(prev => ({ ...prev, amount: e.target.value }))}
                placeholder="请输入积分数量"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                操作原因
              </label>
              <textarea
                value={pointsModal.reason}
                onChange={(e) => setPointsModal(prev => ({ ...prev, reason: e.target.value }))}
                placeholder="请输入操作原因"
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            <div className="flex justify-end space-x-3">
              <button
                onClick={closePointsModal}
                className="px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50"
              >
                取消
              </button>
              <button
                onClick={handlePointsOperation}
                className={`px-4 py-2 text-white rounded-md ${
                  pointsModal.type === 'add'
                    ? 'bg-green-600 hover:bg-green-700'
                    : 'bg-red-600 hover:bg-red-700'
                }`}
              >
                确认{pointsModal.type === 'add' ? '增加' : '扣除'}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ModernUsers;
