import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  Eye,
  Heart,
  Download,
  User,
  Filter,
  Search,
  Grid3X3,
  List,
  ChevronLeft,
  ChevronRight,
  Smartphone,
  Monitor
} from 'lucide-react';
import clsx from 'clsx';
import { getSquareImages } from '../../services/api';
import ImageDetailModal from '../../components/ImageDetailModal';

const ModernSquare = () => {
  const [images, setImages] = useState([]);
  const [loading, setLoading] = useState(true);
  const [viewMode, setViewMode] = useState('grid'); // grid | list
  const [processTypeFilter, setProcessTypeFilter] = useState('');
  const [aspectRatioFilter, setAspectRatioFilter] = useState('');
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 12,
    total: 0
  });
  const [error, setError] = useState(null);
  const [selectedImageId, setSelectedImageId] = useState(null);
  const [isDetailModalOpen, setIsDetailModalOpen] = useState(false);

  // 获取广场图片
  const fetchSquareImages = async () => {
    try {
      setLoading(true);
      const params = {
        page: pagination.current,
        size: pagination.pageSize,
        processType: processTypeFilter || undefined,
        aspectRatio: aspectRatioFilter || undefined
      };
      
      const response = await getSquareImages(params);
      setImages(response.data.records || []);
      setPagination(prev => ({
        ...prev,
        total: response.data.total || 0
      }));
      setError(null);
    } catch (err) {
      console.error('获取广场图片失败:', err);
      setError('获取广场图片失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchSquareImages();
  }, [pagination.current, pagination.pageSize, processTypeFilter, aspectRatioFilter]);

  // 处理分页
  const handlePageChange = (page) => {
    setPagination(prev => ({ ...prev, current: page }));
  };

  // 处理图片点击
  const handleImageClick = (imageId) => {
    setSelectedImageId(imageId);
    setIsDetailModalOpen(true);
  };

  // 关闭详情模态框
  const handleCloseDetailModal = () => {
    setIsDetailModalOpen(false);
    setSelectedImageId(null);
  };

  // 分页组件
  const Pagination = () => {
    const totalPages = Math.ceil(pagination.total / pagination.pageSize);
    const startItem = (pagination.current - 1) * pagination.pageSize + 1;
    const endItem = Math.min(pagination.current * pagination.pageSize, pagination.total);

    return (
      <div className="flex items-center justify-between px-6 py-4 bg-white border-t border-gray-200">
        <div className="flex items-center space-x-4">
          <span className="text-sm text-gray-700">
            显示 {startItem} 到 {endItem} 条，共 {pagination.total} 条
          </span>
        </div>
        
        <div className="flex items-center space-x-2">
          <button
            onClick={() => handlePageChange(pagination.current - 1)}
            disabled={pagination.current <= 1}
            className={clsx(
              'p-2 rounded-lg border',
              pagination.current <= 1
                ? 'border-gray-200 text-gray-400 cursor-not-allowed'
                : 'border-gray-300 text-gray-700 hover:bg-gray-50'
            )}
          >
            <ChevronLeft className="w-4 h-4" />
          </button>
          
          {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
            let pageNum;
            if (totalPages <= 5) {
              pageNum = i + 1;
            } else if (pagination.current <= 3) {
              pageNum = i + 1;
            } else if (pagination.current >= totalPages - 2) {
              pageNum = totalPages - 4 + i;
            } else {
              pageNum = pagination.current - 2 + i;
            }
            
            return (
              <button
                key={pageNum}
                onClick={() => handlePageChange(pageNum)}
                className={clsx(
                  'px-3 py-2 rounded-lg text-sm font-medium',
                  pageNum === pagination.current
                    ? 'bg-primary-600 text-white'
                    : 'text-gray-700 hover:bg-gray-50'
                )}
              >
                {pageNum}
              </button>
            );
          })}
          
          <button
            onClick={() => handlePageChange(pagination.current + 1)}
            disabled={pagination.current >= totalPages}
            className={clsx(
              'p-2 rounded-lg border',
              pagination.current >= totalPages
                ? 'border-gray-200 text-gray-400 cursor-not-allowed'
                : 'border-gray-300 text-gray-700 hover:bg-gray-50'
            )}
          >
            <ChevronRight className="w-4 h-4" />
          </button>
        </div>
      </div>
    );
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="loading-spinner" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <p className="text-error-600 mb-4">{error}</p>
          <button onClick={fetchSquareImages} className="btn-primary">
            重新加载
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">广场预览</h1>
          <p className="text-gray-600 mt-2">预览用户在微信端看到的广场效果</p>
        </div>
        <div className="flex items-center space-x-3">
          <div className="flex items-center space-x-2 px-4 py-2 bg-indigo-50 text-indigo-700 rounded-lg">
            <Smartphone className="w-5 h-5" />
            <span className="text-sm font-medium">公开图片: {pagination.total}</span>
          </div>
        </div>
      </div>

      {/* 筛选和视图控制 */}
      <div className="bg-white rounded-xl shadow-soft border border-gray-200 p-6">
        <div className="flex flex-col lg:flex-row gap-6 items-start lg:items-center justify-between">
          <div className="flex flex-col sm:flex-row gap-6 flex-1">
            {/* 处理类型筛选 */}
            <div className="flex flex-col space-y-2">
              <label className="text-sm font-medium text-gray-700 flex items-center">
                <Filter className="w-4 h-4 mr-2 text-gray-500" />
                处理类型
              </label>
              <div className="flex flex-wrap gap-2">
                {[
                  { value: '', label: '全部类型' },
                  { value: 'beautify', label: '美化' },
                  { value: 'style_transfer', label: '风格转换' },
                  { value: 'enhance', label: '增强' },
                  { value: 'cartoon', label: '卡通化' }
                ].map((type) => (
                  <button
                    key={type.value}
                    onClick={() => setProcessTypeFilter(type.value)}
                    className={clsx(
                      'px-3 py-1.5 text-sm font-medium rounded-full transition-all duration-200',
                      processTypeFilter === type.value
                        ? 'bg-blue-100 text-blue-700 ring-2 ring-blue-200'
                        : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                    )}
                  >
                    {type.label}
                  </button>
                ))}
              </div>
            </div>

            {/* 宽高比筛选 */}
            <div className="flex flex-col space-y-2">
              <label className="text-sm font-medium text-gray-700">
                宽高比
              </label>
              <div className="flex flex-wrap gap-2">
                {[
                  { value: '', label: '全部比例', icon: '⚪' },
                  { value: '1:1', label: '1:1', icon: '⬜' },
                  { value: '4:3', label: '4:3', icon: '▭' },
                  { value: '3:4', label: '3:4', icon: '▯' },
                  { value: '16:9', label: '16:9', icon: '▬' }
                ].map((ratio) => (
                  <button
                    key={ratio.value}
                    onClick={() => setAspectRatioFilter(ratio.value)}
                    className={clsx(
                      'px-3 py-1.5 text-sm font-medium rounded-full transition-all duration-200 flex items-center space-x-1',
                      aspectRatioFilter === ratio.value
                        ? 'bg-indigo-100 text-indigo-700 ring-2 ring-indigo-200'
                        : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                    )}
                  >
                    <span>{ratio.icon}</span>
                    <span>{ratio.label}</span>
                  </button>
                ))}
              </div>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            <button
              onClick={() => setViewMode('grid')}
              className={clsx(
                'p-2 rounded-lg transition-colors duration-200',
                viewMode === 'grid'
                  ? 'bg-primary-100 text-primary-600'
                  : 'text-gray-400 hover:text-gray-600 hover:bg-gray-100'
              )}
              title="网格视图"
            >
              <Grid3X3 className="w-5 h-5" />
            </button>
            <button
              onClick={() => setViewMode('list')}
              className={clsx(
                'p-2 rounded-lg transition-colors duration-200',
                viewMode === 'list'
                  ? 'bg-primary-100 text-primary-600'
                  : 'text-gray-400 hover:text-gray-600 hover:bg-gray-100'
              )}
              title="列表视图"
            >
              <List className="w-5 h-5" />
            </button>
          </div>
        </div>
      </div>

      {/* 图片展示区域 */}
      <div className="bg-white rounded-xl shadow-soft border border-gray-200 overflow-hidden">
        <div className="p-6">
          {viewMode === 'grid' ? (
            // 网格视图
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {images.map((image, index) => (
                <motion.div
                  key={image.id}
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: index * 0.05 }}
                  className="bg-white rounded-lg border border-gray-200 overflow-hidden hover:shadow-lg transition-all duration-300 group"
                >
                  <div
                    className="aspect-w-16 aspect-h-12 bg-gray-100 relative overflow-hidden cursor-pointer"
                    onClick={() => handleImageClick(image.id)}
                  >
                    <img
                      src={image.thumbnailUrl || image.imageUrl}
                      alt={`图片 ${image.id}`}
                      className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                      onError={(e) => {
                        e.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjE1MCIgdmlld0JveD0iMCAwIDIwMCAxNTAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMTUwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik04NyA2NUw5MyA3MUwxMDUgNTlMMTIzIDc3SDE3VjEyM0gxNzNWNzdIODdWNjVaIiBmaWxsPSIjOUNBM0FGIi8+CjxjaXJjbGUgY3g9IjE0NSIgY3k9IjM5IiByPSI5IiBmaWxsPSIjOUNBM0FGIi8+Cjx0ZXh0IHg9IjEwMCIgeT0iOTAiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGZpbGw9IiM5Q0EzQUYiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxMiI+5Zu+54mH5Yqg6L295aSx6LSlPC90ZXh0Pgo8L3N2Zz4K';
                      }}
                    />
                    <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300 flex items-center justify-center">
                      <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                        <div className="bg-white bg-opacity-90 rounded-full p-2">
                          <Eye className="w-5 h-5 text-gray-700" />
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <div className="p-4">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center text-xs text-gray-500">
                        <User className="w-3 h-3 mr-1" />
                        <span className="truncate">{image.userInfo?.nickname || '匿名用户'}</span>
                      </div>
                      {/* 公开状态标签 - 广场中的都是公开图片 */}
                      <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        🌐 公开
                      </span>
                    </div>
                    
                    <div className="flex items-center justify-between text-xs text-gray-500 mb-2">
                      <div className="flex items-center space-x-3">
                        <div className="flex items-center">
                          <Eye className="w-3 h-3 mr-1" />
                          {image.viewCount || 0}
                        </div>
                        <div className="flex items-center">
                          <Heart className={clsx("w-3 h-3 mr-1", image.isLiked ? "text-red-500" : "")} />
                          {image.likeCount || 0}
                        </div>
                      </div>
                      <div className="text-xs text-gray-400">
                        {image.aspectRatio}
                      </div>
                    </div>
                    
                    <div className="text-xs text-gray-400">
                      {image.createTime}
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          ) : (
            // 列表视图
            <div className="space-y-4">
              {images.map((image, index) => (
                <motion.div
                  key={image.id}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.05 }}
                  className="flex items-center space-x-4 p-4 border border-gray-200 rounded-lg hover:shadow-md transition-shadow duration-200"
                >
                  <img
                    src={image.thumbnailUrl || image.imageUrl}
                    alt={`图片 ${image.id}`}
                    className="w-20 h-20 object-cover rounded-lg cursor-pointer hover:opacity-80 transition-opacity duration-200"
                    onClick={() => handleImageClick(image.id)}
                    onError={(e) => {
                      e.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAiIGhlaWdodD0iODAiIHZpZXdCb3g9IjAgMCA4MCA4MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjgwIiBoZWlnaHQ9IjgwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0zNSAyNkwzNyAyOEw0MiAyM0w0OSAzMEg3VjQ5SDczVjMwSDM1VjI2WiIgZmlsbD0iIzlDQTNBRiIvPgo8Y2lyY2xlIGN4PSI1OCIgY3k9IjE1IiByPSIzLjUiIGZpbGw9IiM5Q0EzQUYiLz4KPHR5cGUgeD0iNDAiIHk9IjM2IiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjOUNBM0FGIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iNiI+5Zu+54mH5Yqg6L295aSx6LSlPC90ZXh0Pgo8L3N2Zz4K';
                    }}
                  />
                  
                  <div className="flex-1">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center text-sm text-gray-600">
                        <User className="w-4 h-4 mr-1" />
                        {image.userInfo?.nickname || '匿名用户'}
                      </div>
                      <div className="flex items-center space-x-2">
                        <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                          🌐 公开
                        </span>
                        <div className="text-sm text-gray-500">
                          {image.aspectRatio} • {image.processType}
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-4 text-sm text-gray-500">
                        <div className="flex items-center">
                          <Eye className="w-4 h-4 mr-1" />
                          {image.viewCount || 0} 查看
                        </div>
                        <div className="flex items-center">
                          <Heart className={clsx("w-4 h-4 mr-1", image.isLiked ? "text-red-500" : "")} />
                          {image.likeCount || 0} 点赞
                        </div>
                      </div>
                      <div className="text-sm text-gray-400">
                        {image.createTime}
                      </div>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          )}
        </div>

        {images.length === 0 && !loading && (
          <div className="text-center py-12">
            <Monitor className="w-16 h-16 text-gray-300 mx-auto mb-4" />
            <p className="text-gray-500 text-lg">暂无公开图片</p>
            <p className="text-gray-400 text-sm mt-2">用户申请公开并审核通过的图片将在这里显示</p>
          </div>
        )}

        {/* 分页 */}
        {pagination.total > 0 && <Pagination />}
      </div>

      {/* 图片详情模态框 */}
      <ImageDetailModal
        isOpen={isDetailModalOpen}
        onClose={handleCloseDetailModal}
        imageId={selectedImageId}
      />
    </div>
  );
};

export default ModernSquare;
