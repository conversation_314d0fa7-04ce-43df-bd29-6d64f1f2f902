import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  Users,
  Image,
  Coins,
  TrendingUp,
  Activity,
  Server,
  Database,
  HardDrive,
  Cpu,
  MemoryStick,
  Zap
} from 'lucide-react';
import { LineChart, Line, AreaChart, Area, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, PieChart, Pie, Cell } from 'recharts';
import clsx from 'clsx';
import { getSystemStats } from '../../services/api';

const ModernDashboard = () => {
  const [stats, setStats] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // 获取系统统计数据
  useEffect(() => {
    const fetchStats = async () => {
      try {
        setLoading(true);
        const response = await getSystemStats();
        setStats(response.data);
        setError(null);
      } catch (err) {
        console.error('获取系统统计失败:', err);
        setError('获取系统统计失败');
      } finally {
        setLoading(false);
      }
    };

    fetchStats();
  }, []);

  // 生成图表数据
  const chartData = stats ? [
    { name: '本周', users: stats.userStats?.weekNewUsers || 0, images: stats.imageStats?.weekNewImages || 0 },
    { name: '本月', users: stats.userStats?.monthNewUsers || 0, images: stats.imageStats?.monthNewImages || 0 },
    { name: '总计', users: stats.userStats?.totalUsers || 0, images: stats.imageStats?.totalImages || 0 },
  ] : [];

  const pieData = [
    { name: '公开图片', value: stats?.imageStats?.publicImages || 0, color: '#0ea5e9' },
    { name: '私有图片', value: stats?.imageStats?.privateImages || 0, color: '#8b5cf6' },
    { name: '待审核', value: stats?.imageStats?.pendingAuditImages || 0, color: '#f59e0b' },
  ];

  const StatCard = ({ title, value, change, trend, icon: Icon, color = 'primary' }) => {
    const colorClasses = {
      primary: 'from-blue-500 to-blue-600',
      success: 'from-green-500 to-green-600',
      warning: 'from-yellow-500 to-yellow-600',
      purple: 'from-purple-500 to-purple-600'
    };

    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        whileHover={{ y: -4 }}
        className="card-hover relative overflow-hidden"
      >
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-gray-600 mb-1">{title}</p>
            <p className="text-3xl font-bold text-gray-900">{value.toLocaleString()}</p>
            <div className="flex items-center mt-2">
              <span className={clsx(
                'text-sm font-medium',
                trend === 'up' ? 'text-success-600' : 'text-error-600'
              )}>
                {trend === 'up' ? '+' : ''}{change}%
              </span>
              <span className="text-sm text-gray-500 ml-1">vs 上月</span>
            </div>
          </div>
          <div className={clsx(
            'w-16 h-16 rounded-2xl bg-gradient-to-br flex items-center justify-center',
            colorClasses[color]
          )}>
            <Icon className="w-8 h-8 text-white" />
          </div>
        </div>
        
        {/* 装饰性渐变 */}
        <div className={clsx(
          'absolute top-0 right-0 w-32 h-32 bg-gradient-to-br opacity-5 rounded-full transform translate-x-16 -translate-y-16',
          colorClasses[color]
        )} />
      </motion.div>
    );
  };

  const SystemStatusCard = ({ title, status, value, icon: Icon }) => {
    const statusColors = {
      healthy: 'text-success-600 bg-success-50',
      warning: 'text-warning-600 bg-warning-50',
      error: 'text-error-600 bg-error-50'
    };

    return (
      <div className="flex items-center justify-between p-4 bg-gray-50 rounded-xl">
        <div className="flex items-center space-x-3">
          <Icon className="w-5 h-5 text-gray-600" />
          <span className="text-sm font-medium text-gray-900">{title}</span>
        </div>
        <div className="flex items-center space-x-2">
          {value && <span className="text-sm text-gray-600">{value}%</span>}
          <span className={clsx(
            'px-2 py-1 text-xs font-medium rounded-full',
            statusColors[status]
          )}>
            {status === 'healthy' ? '正常' : status === 'warning' ? '警告' : '错误'}
          </span>
        </div>
      </div>
    );
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-center h-64">
          <div className="loading-spinner" />
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <p className="text-error-600 mb-4">{error}</p>
            <button
              onClick={() => window.location.reload()}
              className="btn-primary"
            >
              重新加载
            </button>
          </div>
        </div>
      </div>
    );
  }

  if (!stats) {
    return null;
  }

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">仪表盘</h1>
          <p className="text-gray-600 mt-1">欢迎回来，这里是系统概览</p>
        </div>
        <div className="flex items-center space-x-3">
          <div className="flex items-center space-x-2 px-3 py-2 bg-success-50 text-success-700 rounded-lg">
            <Activity className="w-4 h-4" />
            <span className="text-sm font-medium">
              {stats.systemStatus?.databaseStatus === '正常' ? '系统运行正常' : '系统异常'}
            </span>
          </div>
        </div>
      </div>

      {/* 统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard
          title="总用户数"
          value={stats.userStats?.totalUsers || 0}
          change={((stats.userStats?.weekNewUsers || 0) / Math.max(stats.userStats?.totalUsers - stats.userStats?.weekNewUsers, 1) * 100).toFixed(1)}
          trend="up"
          icon={Users}
          color="primary"
        />
        <StatCard
          title="图片总数"
          value={stats.imageStats?.totalImages || 0}
          change={((stats.imageStats?.weekNewImages || 0) / Math.max(stats.imageStats?.totalImages - stats.imageStats?.weekNewImages, 1) * 100).toFixed(1)}
          trend="up"
          icon={Image}
          color="success"
        />
        <StatCard
          title="积分总量"
          value={stats.pointsStats?.totalPointsIssued || 0}
          change={((stats.pointsStats?.todayPointsIssued || 0) / Math.max(stats.pointsStats?.totalPointsIssued - stats.pointsStats?.todayPointsIssued, 1) * 100).toFixed(1)}
          trend="up"
          icon={Coins}
          color="warning"
        />
        <StatCard
          title="成功率"
          value={stats.imageStats?.successRate || 0}
          change="1.8"
          trend="up"
          icon={TrendingUp}
          color="purple"
        />
      </div>

      {/* 图表区域 */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* 趋势图 */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          className="lg:col-span-2 card"
        >
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-gray-900">数据趋势</h3>
            <div className="flex items-center space-x-4 text-sm">
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-primary-500 rounded-full"></div>
                <span className="text-gray-600">用户</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-success-500 rounded-full"></div>
                <span className="text-gray-600">图片</span>
              </div>
            </div>
          </div>
          <ResponsiveContainer width="100%" height={300}>
            <AreaChart data={chartData}>
              <defs>
                <linearGradient id="colorUsers" x1="0" y1="0" x2="0" y2="1">
                  <stop offset="5%" stopColor="#0ea5e9" stopOpacity={0.3}/>
                  <stop offset="95%" stopColor="#0ea5e9" stopOpacity={0}/>
                </linearGradient>
                <linearGradient id="colorImages" x1="0" y1="0" x2="0" y2="1">
                  <stop offset="5%" stopColor="#22c55e" stopOpacity={0.3}/>
                  <stop offset="95%" stopColor="#22c55e" stopOpacity={0}/>
                </linearGradient>
              </defs>
              <CartesianGrid strokeDasharray="3 3" stroke="#f1f5f9" />
              <XAxis dataKey="name" stroke="#64748b" fontSize={12} />
              <YAxis stroke="#64748b" fontSize={12} />
              <Tooltip 
                contentStyle={{ 
                  backgroundColor: 'white', 
                  border: 'none', 
                  borderRadius: '12px', 
                  boxShadow: '0 10px 40px -10px rgba(0, 0, 0, 0.1)' 
                }} 
              />
              <Area type="monotone" dataKey="users" stroke="#0ea5e9" fillOpacity={1} fill="url(#colorUsers)" strokeWidth={2} />
              <Area type="monotone" dataKey="images" stroke="#22c55e" fillOpacity={1} fill="url(#colorImages)" strokeWidth={2} />
            </AreaChart>
          </ResponsiveContainer>
        </motion.div>

        {/* 饼图 */}
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          className="card"
        >
          <h3 className="text-lg font-semibold text-gray-900 mb-6">图片分类</h3>
          <ResponsiveContainer width="100%" height={300}>
            <PieChart>
              <Pie
                data={pieData}
                cx="50%"
                cy="50%"
                innerRadius={60}
                outerRadius={100}
                paddingAngle={5}
                dataKey="value"
              >
                {pieData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <Tooltip />
            </PieChart>
          </ResponsiveContainer>
          <div className="grid grid-cols-2 gap-2 mt-4">
            {pieData.map((item, index) => (
              <div key={index} className="flex items-center space-x-2">
                <div className="w-3 h-3 rounded-full" style={{ backgroundColor: item.color }}></div>
                <span className="text-sm text-gray-600">{item.name}</span>
              </div>
            ))}
          </div>
        </motion.div>
      </div>

      {/* 系统状态 */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="card"
      >
        <h3 className="text-lg font-semibold text-gray-900 mb-6">系统状态</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <SystemStatusCard
            title="数据库"
            status={stats.systemStatus?.databaseStatus === '正常' ? 'healthy' : 'error'}
            icon={Database}
          />
          <SystemStatusCard
            title="Redis"
            status={stats.systemStatus?.redisStatus === '正常' ? 'healthy' : 'error'}
            icon={Server}
          />
          <SystemStatusCard
            title="MinIO"
            status={stats.systemStatus?.minioStatus === '正常' ? 'healthy' : 'error'}
            icon={HardDrive}
          />
          <SystemStatusCard
            title="CPU使用率"
            status={stats.systemStatus?.cpuUsage > 80 ? 'error' : stats.systemStatus?.cpuUsage > 60 ? 'warning' : 'healthy'}
            value={stats.systemStatus?.cpuUsage}
            icon={Cpu}
          />
          <SystemStatusCard
            title="内存使用率"
            status={stats.systemStatus?.memoryUsage > 80 ? 'error' : stats.systemStatus?.memoryUsage > 60 ? 'warning' : 'healthy'}
            value={stats.systemStatus?.memoryUsage}
            icon={MemoryStick}
          />
          <SystemStatusCard
            title="磁盘使用率"
            status={stats.systemStatus?.diskUsage > 80 ? 'error' : stats.systemStatus?.diskUsage > 60 ? 'warning' : 'healthy'}
            value={stats.systemStatus?.diskUsage}
            icon={HardDrive}
          />
        </div>
      </motion.div>
    </div>
  );
};

export default ModernDashboard;
