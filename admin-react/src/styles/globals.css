/* 导入Inter字体 */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* 全局样式 */
@layer base {
  body {
    font-family: 'Inter', system-ui, -apple-system, sans-serif;
  }
}

/* 自定义组件样式 */
@layer components {
  .card {
    @apply bg-white rounded-xl shadow-sm border border-gray-200 p-6;
  }
  
  .card-hover {
    @apply bg-white rounded-xl shadow-sm border border-gray-200 p-6 transition-all duration-200 hover:shadow-md hover:-translate-y-1;
  }
  
  .btn {
    @apply inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2;
  }
  
  .btn-primary {
    @apply btn bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500 shadow-sm;
  }
  
  .btn-secondary {
    @apply btn bg-gray-100 text-gray-700 hover:bg-gray-200 focus:ring-gray-500;
  }
  
  .input {
    @apply block w-full px-3 py-2 border border-gray-300 rounded-lg text-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors duration-200;
  }
  
  .badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }
  
  .badge-success {
    @apply badge bg-green-50 text-green-600;
  }
  
  .badge-warning {
    @apply badge bg-yellow-50 text-yellow-600;
  }
  
  .badge-error {
    @apply badge bg-red-50 text-red-600;
  }
  
  .badge-gray {
    @apply badge bg-gray-100 text-gray-600;
  }
  
  .loading-spinner {
    @apply animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600;
  }
  
  .gradient-primary {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  }
  
  .text-gradient {
    @apply bg-clip-text text-transparent bg-gradient-to-r from-blue-600 to-blue-800;
  }
  
  .shadow-glow {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
  }
}

/* 滚动条样式 */
@layer utilities {
  ::-webkit-scrollbar {
    @apply w-2;
  }
  
  ::-webkit-scrollbar-track {
    @apply bg-gray-100;
  }
  
  ::-webkit-scrollbar-thumb {
    @apply bg-gray-300 rounded-full;
  }
  
  ::-webkit-scrollbar-thumb:hover {
    @apply bg-gray-400;
  }
}
