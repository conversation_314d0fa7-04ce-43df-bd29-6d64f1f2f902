import React, { useState, useEffect } from 'react'
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom'
import { motion } from 'framer-motion'
import ModernLogin from './pages/Login/ModernLogin'
import ModernLayout from './components/Layout/ModernLayout'
import ModernDashboard from './pages/Dashboard/ModernDashboard'
import ModernUsers from './pages/Users/<USER>'
import ModernImages from './pages/Images/ModernImages'
import ModernStats from './pages/Stats/ModernStats'
import ModernSettings from './pages/Settings/ModernSettings'
import ModernSquare from './pages/Square/ModernSquare'

function App() {
  const [loading, setLoading] = useState(true)
  const [isAuthenticated, setIsAuthenticated] = useState(false)

  useEffect(() => {
    // 检查登录状态
    const token = localStorage.getItem('admin_token')

    if (token) {
      setIsAuthenticated(true)
    }

    setLoading(false)
  }, [])

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          className="text-center"
        >
          <div className="loading-spinner mx-auto mb-4" />
          <p className="text-gray-600">加载中...</p>
        </motion.div>
      </div>
    )
  }

  return (
    <Router>
      <Routes>
        <Route
          path="/login"
          element={
            isAuthenticated ?
            <Navigate to="/dashboard" replace /> :
            <ModernLogin />
          }
        />
        <Route
          path="/"
          element={
            isAuthenticated ? (
              <ModernLayout />
            ) : (
              <Navigate to="/login" replace />
            )
          }
        >
          <Route index element={<Navigate to="/dashboard" replace />} />
          <Route path="dashboard" element={<ModernDashboard />} />
          <Route path="users" element={<ModernUsers />} />
          <Route path="images" element={<ModernImages />} />
          <Route path="stats" element={<ModernStats />} />
          <Route path="settings" element={<ModernSettings />} />
          <Route path="square" element={<ModernSquare />} />
        </Route>
      </Routes>
    </Router>
  )
}

export default App
