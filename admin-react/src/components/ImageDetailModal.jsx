import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  X,
  User,
  Calendar,
  Eye,
  Heart,
  Download,
  Image as ImageIcon,
  FileText,
  Palette,
  Monitor,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  ExternalLink
} from 'lucide-react';
import clsx from 'clsx';
import { getImageDetail } from '../services/api';

const ImageDetailModal = ({ isOpen, onClose, imageId }) => {
  const [imageDetail, setImageDetail] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // 获取图片详情
  const fetchImageDetail = async () => {
    if (!imageId) return;
    
    try {
      setLoading(true);
      setError(null);
      const response = await getImageDetail(imageId);
      setImageDetail(response.data);
    } catch (err) {
      console.error('获取图片详情失败:', err);
      setError('获取图片详情失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (isOpen && imageId) {
      fetchImageDetail();
    }
  }, [isOpen, imageId]);

  // 关闭模态框时清理数据
  const handleClose = () => {
    setImageDetail(null);
    setError(null);
    onClose();
  };

  // 格式化文件大小
  const formatFileSize = (bytes) => {
    if (!bytes) return '未知';
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  };

  // 获取审核状态样式
  const getAuditStatusStyle = (auditStatus) => {
    switch (auditStatus) {
      case 0:
        return { color: 'text-yellow-700', bg: 'bg-yellow-100', icon: AlertCircle, text: '待审核' };
      case 1:
        return { color: 'text-green-700', bg: 'bg-green-100', icon: CheckCircle, text: '审核通过' };
      case 2:
        return { color: 'text-red-700', bg: 'bg-red-100', icon: XCircle, text: '审核拒绝' };
      default:
        return { color: 'text-gray-700', bg: 'bg-gray-100', icon: AlertCircle, text: '未知状态' };
    }
  };

  // 获取公开状态样式
  const getPublicStatusStyle = (publicStatusDesc) => {
    return publicStatusDesc === '公开' 
      ? { color: 'text-blue-700', bg: 'bg-blue-100', icon: '🌐' }
      : { color: 'text-gray-700', bg: 'bg-gray-100', icon: '🔒' };
  };

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <div className="fixed inset-0 z-50 overflow-y-auto">
        {/* 背景遮罩 */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 bg-black bg-opacity-50 transition-opacity"
          onClick={handleClose}
        />

        {/* 模态框内容 */}
        <div className="flex min-h-full items-center justify-center p-4">
          <motion.div
            initial={{ opacity: 0, scale: 0.95, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: 20 }}
            className="relative w-full max-w-4xl bg-white rounded-2xl shadow-2xl overflow-hidden"
            onClick={(e) => e.stopPropagation()}
          >
            {/* 头部 */}
            <div className="flex items-center justify-between p-6 border-b border-gray-200">
              <h2 className="text-2xl font-bold text-gray-900 flex items-center">
                <ImageIcon className="w-6 h-6 mr-2 text-primary-600" />
                图片详情
              </h2>
              <button
                onClick={handleClose}
                className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors duration-200"
              >
                <X className="w-6 h-6" />
              </button>
            </div>

            {/* 内容区域 */}
            <div className="p-6">
              {loading && (
                <div className="flex items-center justify-center h-64">
                  <div className="loading-spinner" />
                </div>
              )}

              {error && (
                <div className="flex items-center justify-center h-64">
                  <div className="text-center">
                    <p className="text-red-600 mb-4">{error}</p>
                    <button onClick={fetchImageDetail} className="btn-primary">
                      重新加载
                    </button>
                  </div>
                </div>
              )}

              {imageDetail && (
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  {/* 左侧：图片展示 */}
                  <div className="space-y-4">
                    <div className="aspect-w-16 aspect-h-12 bg-gray-100 rounded-xl overflow-hidden">
                      <img
                        src={imageDetail.imageUrl}
                        alt={imageDetail.title || '图片'}
                        className="w-full h-full object-contain"
                        onError={(e) => {
                          e.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjMwMCIgdmlld0JveD0iMCAwIDQwMCAzMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSI0MDAiIGhlaWdodD0iMzAwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0xNzQgMTMwTDE4NiAxNDJMMjEwIDExOEwyNDYgMTU0SDM0VjI0NkgzNDZWMTU0SDE3NFYxMzBaIiBmaWxsPSIjOUNBM0FGIi8+CjxjaXJjbGUgY3g9IjI5MCIgY3k9Ijc4IiByPSIxOCIgZmlsbD0iIzlDQTNBRiIvPgo8dGV4dCB4PSIyMDAiIHk9IjE4MCIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZmlsbD0iIzlDQTNBRiIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjE2Ij7lm77niYfliKDovb3lpLHotKU8L3RleHQ+Cjwvc3ZnPgo=';
                        }}
                      />
                    </div>

                    {/* 图片操作按钮 */}
                    <div className="flex items-center justify-center space-x-3">
                      <button
                        onClick={() => window.open(imageDetail.imageUrl, '_blank')}
                        className="flex items-center px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors duration-200"
                      >
                        <ExternalLink className="w-4 h-4 mr-2" />
                        查看原图
                      </button>
                    </div>
                  </div>

                  {/* 右侧：详细信息 */}
                  <div className="space-y-6">
                    {/* 基本信息 */}
                    <div className="bg-gray-50 rounded-xl p-4">
                      <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                        <FileText className="w-5 h-5 mr-2 text-gray-600" />
                        基本信息
                      </h3>
                      <div className="space-y-3">
                        <div className="flex items-center justify-between">
                          <span className="text-gray-600">标题</span>
                          <span className="font-medium">{imageDetail.title || '未命名图片'}</span>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-gray-600">文件名</span>
                          <span className="font-medium text-sm">{imageDetail.fileName || '未知'}</span>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-gray-600">文件大小</span>
                          <span className="font-medium">{formatFileSize(imageDetail.fileSize)}</span>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-gray-600">格式</span>
                          <span className="font-medium uppercase">{imageDetail.imageFormat || '未知'}</span>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-gray-600">尺寸</span>
                          <span className="font-medium">
                            {imageDetail.imageWidth && imageDetail.imageHeight 
                              ? `${imageDetail.imageWidth} × ${imageDetail.imageHeight}` 
                              : '未知'}
                          </span>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-gray-600">宽高比</span>
                          <span className="font-medium">{imageDetail.aspectRatio || '未知'}</span>
                        </div>
                      </div>
                    </div>

                    {/* 状态信息 */}
                    <div className="bg-gray-50 rounded-xl p-4">
                      <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                        <Monitor className="w-5 h-5 mr-2 text-gray-600" />
                        状态信息
                      </h3>
                      <div className="space-y-3">
                        <div className="flex items-center justify-between">
                          <span className="text-gray-600">公开状态</span>
                          <span className={clsx(
                            'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
                            getPublicStatusStyle(imageDetail.publicStatusDesc).bg,
                            getPublicStatusStyle(imageDetail.publicStatusDesc).color
                          )}>
                            {getPublicStatusStyle(imageDetail.publicStatusDesc).icon} {imageDetail.publicStatusDesc || '私有'}
                          </span>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-gray-600">审核状态</span>
                          {(() => {
                            const auditStyle = getAuditStatusStyle(imageDetail.auditStatus);
                            const IconComponent = auditStyle.icon;
                            return (
                              <span className={clsx(
                                'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
                                auditStyle.bg,
                                auditStyle.color
                              )}>
                                <IconComponent className="w-3 h-3 mr-1" />
                                {auditStyle.text}
                              </span>
                            );
                          })()}
                        </div>
                        {imageDetail.auditRemark && (
                          <div className="flex flex-col space-y-1">
                            <span className="text-gray-600">审核备注</span>
                            <span className="text-sm text-gray-800 bg-white p-2 rounded border">
                              {imageDetail.auditRemark}
                            </span>
                          </div>
                        )}
                      </div>
                    </div>

                    {/* 用户信息 */}
                    <div className="bg-gray-50 rounded-xl p-4">
                      <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                        <User className="w-5 h-5 mr-2 text-gray-600" />
                        用户信息
                      </h3>
                      <div className="space-y-3">
                        <div className="flex items-center justify-between">
                          <span className="text-gray-600">用户昵称</span>
                          <span className="font-medium">{imageDetail.userNickname || '未知用户'}</span>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-gray-600">上传时间</span>
                          <span className="font-medium">{imageDetail.createTime || '未知'}</span>
                        </div>
                      </div>
                    </div>

                    {/* 统计信息 */}
                    <div className="bg-gray-50 rounded-xl p-4">
                      <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                        <Palette className="w-5 h-5 mr-2 text-gray-600" />
                        统计信息
                      </h3>
                      <div className="grid grid-cols-3 gap-4">
                        <div className="text-center">
                          <div className="flex items-center justify-center w-10 h-10 bg-blue-100 text-blue-600 rounded-lg mx-auto mb-2">
                            <Eye className="w-5 h-5" />
                          </div>
                          <div className="text-lg font-semibold text-gray-900">{imageDetail.viewCount || 0}</div>
                          <div className="text-sm text-gray-600">查看</div>
                        </div>
                        <div className="text-center">
                          <div className="flex items-center justify-center w-10 h-10 bg-red-100 text-red-600 rounded-lg mx-auto mb-2">
                            <Heart className="w-5 h-5" />
                          </div>
                          <div className="text-lg font-semibold text-gray-900">{imageDetail.likeCount || 0}</div>
                          <div className="text-sm text-gray-600">点赞</div>
                        </div>
                        <div className="text-center">
                          <div className="flex items-center justify-center w-10 h-10 bg-green-100 text-green-600 rounded-lg mx-auto mb-2">
                            <Download className="w-5 h-5" />
                          </div>
                          <div className="text-lg font-semibold text-gray-900">{imageDetail.downloadCount || 0}</div>
                          <div className="text-sm text-gray-600">下载</div>
                        </div>
                      </div>
                    </div>

                    {/* 处理信息 */}
                    {imageDetail.processType && (
                      <div className="bg-gray-50 rounded-xl p-4">
                        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                          <Palette className="w-5 h-5 mr-2 text-gray-600" />
                          处理信息
                        </h3>
                        <div className="space-y-3">
                          <div className="flex items-center justify-between">
                            <span className="text-gray-600">处理类型</span>
                            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-700">
                              🎨 {imageDetail.processType}
                            </span>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          </motion.div>
        </div>
      </div>
    </AnimatePresence>
  );
};

export default ImageDetailModal;
