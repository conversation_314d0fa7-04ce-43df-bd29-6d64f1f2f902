import dayjs from 'dayjs';

/**
 * 格式化数字
 * @param {number} num 数字
 * @returns {string} 格式化后的字符串
 */
export const formatNumber = (num) => {
  if (num >= 10000) {
    return (num / 10000).toFixed(1) + '万';
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'k';
  }
  return num?.toString() || '0';
};

/**
 * 格式化文件大小
 * @param {number} bytes 字节数
 * @returns {string} 格式化后的文件大小
 */
export const formatFileSize = (bytes) => {
  if (!bytes || bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

/**
 * 格式化日期
 * @param {string|Date} date 日期
 * @param {string} format 格式
 * @returns {string} 格式化后的日期
 */
export const formatDate = (date, format = 'YYYY-MM-DD HH:mm:ss') => {
  if (!date) return '-';
  return dayjs(date).format(format);
};

/**
 * 获取状态标签配置
 * @param {number} status 状态值
 * @param {string} type 类型
 * @returns {object} 标签配置
 */
export const getStatusConfig = (status, type = 'user') => {
  const configs = {
    user: {
      1: { color: 'success', text: '正常' },
      0: { color: 'error', text: '禁用' },
    },
    audit: {
      0: { color: 'warning', text: '待审核' },
      1: { color: 'success', text: '已通过' },
      2: { color: 'error', text: '已拒绝' },
    },
    public: {
      1: { color: 'processing', text: '公开' },
      0: { color: 'default', text: '私有' },
    },
    system: {
      '正常': { color: 'success', text: '正常' },
      '异常': { color: 'error', text: '异常' },
      '警告': { color: 'warning', text: '警告' },
    },
  };

  return configs[type]?.[status] || { color: 'default', text: '未知' };
};

/**
 * 获取性别文本
 * @param {number} gender 性别值
 * @returns {string} 性别文本
 */
export const getGenderText = (gender) => {
  const genderMap = {
    0: '未知',
    1: '男',
    2: '女',
  };
  return genderMap[gender] || '未知';
};

/**
 * 获取角色文本
 * @param {number} role 角色值
 * @returns {string} 角色文本
 */
export const getRoleText = (role) => {
  const roleMap = {
    1: '超级管理员',
    2: '普通管理员',
  };
  return roleMap[role] || '未知角色';
};

/**
 * 复制到剪贴板
 * @param {string} text 要复制的文本
 * @returns {Promise<boolean>} 是否成功
 */
export const copyToClipboard = async (text) => {
  try {
    await navigator.clipboard.writeText(text);
    return true;
  } catch (err) {
    // 降级方案
    const textArea = document.createElement('textarea');
    textArea.value = text;
    document.body.appendChild(textArea);
    textArea.select();
    const success = document.execCommand('copy');
    document.body.removeChild(textArea);
    return success;
  }
};

/**
 * 下载文件
 * @param {string} url 文件URL
 * @param {string} filename 文件名
 */
export const downloadFile = (url, filename) => {
  const link = document.createElement('a');
  link.href = url;
  link.download = filename;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};

/**
 * 防抖函数
 * @param {Function} func 要防抖的函数
 * @param {number} wait 等待时间
 * @returns {Function} 防抖后的函数
 */
export const debounce = (func, wait) => {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
};

/**
 * 节流函数
 * @param {Function} func 要节流的函数
 * @param {number} limit 限制时间
 * @returns {Function} 节流后的函数
 */
export const throttle = (func, limit) => {
  let inThrottle;
  return function() {
    const args = arguments;
    const context = this;
    if (!inThrottle) {
      func.apply(context, args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
};

/**
 * 生成随机颜色
 * @returns {string} 十六进制颜色值
 */
export const generateRandomColor = () => {
  return '#' + Math.floor(Math.random() * 16777215).toString(16);
};

/**
 * 获取文件扩展名
 * @param {string} filename 文件名
 * @returns {string} 扩展名
 */
export const getFileExtension = (filename) => {
  return filename.slice((filename.lastIndexOf('.') - 1 >>> 0) + 2);
};

/**
 * 验证邮箱格式
 * @param {string} email 邮箱地址
 * @returns {boolean} 是否有效
 */
export const isValidEmail = (email) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

/**
 * 验证手机号格式
 * @param {string} phone 手机号
 * @returns {boolean} 是否有效
 */
export const isValidPhone = (phone) => {
  const phoneRegex = /^1[3-9]\d{9}$/;
  return phoneRegex.test(phone);
};

/**
 * 获取图片预览URL
 * @param {string} url 原始URL
 * @returns {string} 预览URL
 */
export const getImagePreviewUrl = (url) => {
  if (!url) return '';
  // 如果是缩略图URL，直接返回
  if (url.includes('thumbnail')) {
    return url;
  }
  // 否则返回原图
  return url;
};

/**
 * 计算百分比
 * @param {number} value 当前值
 * @param {number} total 总值
 * @returns {number} 百分比
 */
export const calculatePercentage = (value, total) => {
  if (!total || total === 0) return 0;
  return Math.round((value / total) * 100);
};

/**
 * 获取时间段文本
 * @param {string} date 日期
 * @returns {string} 时间段文本
 */
export const getTimeAgo = (date) => {
  if (!date) return '';
  
  const now = dayjs();
  const target = dayjs(date);
  const diff = now.diff(target, 'minute');
  
  if (diff < 1) return '刚刚';
  if (diff < 60) return `${diff}分钟前`;
  if (diff < 1440) return `${Math.floor(diff / 60)}小时前`;
  if (diff < 43200) return `${Math.floor(diff / 1440)}天前`;
  
  return target.format('YYYY-MM-DD');
};
