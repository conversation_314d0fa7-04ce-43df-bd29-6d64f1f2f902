import axios from 'axios';
import { message } from 'antd';

// 创建axios实例
const api = axios.create({
  baseURL: '/api/admin',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    // 添加Token
    const token = localStorage.getItem('admin_token');
    if (token) {
      config.headers.Token = token;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    const { data } = response;
    
    // 统一处理响应
    if (data.code === 200) {
      return data;
    } else if (data.code === 401) {
      // Token过期，清除本地存储并跳转登录
      localStorage.removeItem('adminToken');
      localStorage.removeItem('adminInfo');
      window.location.href = '/login';
      return Promise.reject(new Error('登录已过期，请重新登录'));
    } else {
      message.error(data.message || '请求失败');
      return Promise.reject(new Error(data.message || '请求失败'));
    }
  },
  (error) => {
    console.error('API请求错误:', error);
    
    if (error.response) {
      const { status, data } = error.response;
      if (status === 401) {
        localStorage.removeItem('admin_token');
        localStorage.removeItem('adminInfo');
        window.location.href = '/login';
        message.error('登录已过期，请重新登录');
      } else {
        message.error(data?.message || `请求失败 (${status})`);
      }
    } else if (error.request) {
      message.error('网络连接失败，请检查网络');
    } else {
      message.error(error.message || '请求失败');
    }
    
    return Promise.reject(error);
  }
);

// ==================== 认证相关 ====================

// 管理员登录
export const login = (data) => {
  return api.post('/auth/login', data);
};

// 修改密码
export const changePassword = (data) => {
  return api.post('/auth/change-password', data);
};

// ==================== 用户管理 ====================

// 获取用户列表
export const getUserList = (params) => {
  return api.get('/users', { params });
};

// 获取用户详情
export const getUserDetail = (userId) => {
  return api.get(`/users/${userId}`);
};

// 更新用户状态
export const updateUserStatus = (userId, data) => {
  return api.put(`/users/${userId}/status`, data);
};

// 积分管理
export const manageUserPoints = (userId, data) => {
  return api.post('/users/points/manage', {
    userId,
    ...data
  });
};

// ==================== 图片管理 ====================

// 获取图片列表
export const getImageList = (params) => {
  return api.get('/images', { params });
};

// 获取图片详情
export const getImageDetail = (imageId) => {
  return api.get(`/images/${imageId}`);
};

// 图片审核
export const auditImage = (imageId, auditStatus, auditRemark = '') => {
  const params = new URLSearchParams({ auditStatus });
  if (auditRemark && auditRemark.trim()) {
    params.append('auditRemark', auditRemark.trim());
  }
  return api.post(`/images/${imageId}/audit?${params.toString()}`);
};

// 删除图片
export const deleteImage = (imageId) => {
  return api.delete(`/images/${imageId}`);
};

// 获取图片统计
export const getImageStats = () => {
  return api.get('/images/stats');
};

// 获取广场公开图片（预览微信端广场效果）
export const getSquareImages = (params = {}) => {
  // 使用现有的图片管理接口，只查询公开且审核通过的图片
  const queryParams = {
    ...params,
    isPublic: 1,      // 只查询公开图片
    auditStatus: 1    // 只查询审核通过的图片
  };
  return api.get('/images', { params: queryParams });
};

// ==================== 系统统计 ====================

// 获取系统统计信息
export const getSystemStats = () => {
  return api.get('/stats/system');
};

// ==================== 图片生成错误处理 ====================

/**
 * 解析图片生成错误信息
 * @param {string} errorMessage - 原始错误信息
 * @returns {string} - 清理后的错误信息
 */
export const parseImageGenerationError = (errorMessage) => {
  if (!errorMessage || typeof errorMessage !== 'string') {
    return '图片生成失败，请重试';
  }

  try {
    // 提取失败原因部分
    const failureReasonMatch = errorMessage.match(/失败原因：([^\n]+)/);
    const reasonMatch = errorMessage.match(/\*\*原因\*\*:\s*([^\n]+)/);
    const suggestionMatch = errorMessage.match(/\*\*建议\*\*:\s*([^\n]+)/);

    let cleanedMessage = '';

    // 处理失败原因
    if (failureReasonMatch) {
      const failureReason = failureReasonMatch[1].trim();
      cleanedMessage += `失败原因：${failureReason}\n`;
    }

    // 处理原因说明，去除"OpenAI"字样
    if (reasonMatch) {
      let reason = reasonMatch[1].trim();
      // 去除"OpenAI"字样
      reason = reason.replace(/OpenAI\s*/gi, '');
      cleanedMessage += `原因: ${reason}\n`;
    }

    // 处理建议，去除markdown格式
    if (suggestionMatch) {
      let suggestion = suggestionMatch[1].trim();
      // 去除"OpenAI"字样
      suggestion = suggestion.replace(/OpenAI\s*/gi, '');
      cleanedMessage += `建议: ${suggestion}`;
    }

    // 如果没有匹配到任何内容，返回简化的错误信息
    if (!cleanedMessage) {
      // 尝试提取基本的错误信息
      if (errorMessage.includes('生成失败')) {
        return '图片生成失败，内容可能违反相关政策，请尝试修改提示词后重试';
      }
      return '图片生成失败，请重试';
    }

    return cleanedMessage.trim();

  } catch (error) {
    console.error('解析图片生成错误信息失败:', error);
    return '图片生成失败，请重试';
  }
};

/**
 * 检查是否为图片生成相关错误
 * @param {string} message - 错误信息
 * @returns {boolean} - 是否为图片生成错误
 */
export const isImageGenerationError = (message) => {
  if (!message || typeof message !== 'string') {
    return false;
  }

  return message.includes('生成失败') ||
         message.includes('output_moderation') ||
         message.includes('排队中') ||
         message.includes('生成中');
};

export default api;
