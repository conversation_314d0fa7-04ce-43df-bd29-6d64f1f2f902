# InspiRealm 管理后台 (React + Vite)

## 项目简介

InspiRealm 管理后台是基于 React + Vite + Ant Design 构建的现代化管理系统，用于管理AI图片处理平台的用户、图片、积分等业务数据。

## 技术栈

- **前端框架**: React 18
- **构建工具**: Vite 5
- **UI组件库**: Ant Design 5
- **路由管理**: React Router 6
- **HTTP客户端**: Axios
- **日期处理**: Day.js
- **图表库**: Recharts
- **开发语言**: JavaScript (ES6+)

## 功能特性

### 🔐 认证系统
- 管理员登录/退出
- Token自动管理
- 权限验证
- 登录状态持久化

### 📊 仪表盘
- 实时系统统计
- 用户数据概览
- 图片处理统计
- 系统资源监控
- 美观的数据可视化

### 👥 用户管理
- 用户列表分页展示
- 用户详情查看
- 用户状态管理（启用/禁用）
- 积分充值/扣除操作
- 搜索和筛选功能

### 🖼️ 图片管理
- 图片网格卡片展示
- 图片详情查看
- 图片审核功能（通过/拒绝）
- 图片删除功能
- 多维度筛选（公开状态、审核状态）

### 📈 统计分析
- 用户统计分析
- 图片统计分析
- 积分统计分析
- 数据可视化展示

## 项目结构

```
admin-react/
├── public/
│   ├── vite.svg
│   └── ...
├── src/
│   ├── components/          # 公共组件
│   │   └── Layout/         # 主布局组件
│   ├── pages/              # 页面组件
│   │   ├── Dashboard/      # 仪表盘
│   │   ├── Users/          # 用户管理
│   │   ├── Images/         # 图片管理
│   │   ├── Stats/          # 统计分析
│   │   └── Login/          # 登录页面
│   ├── services/           # API服务
│   │   └── api.js         # API接口封装
│   ├── utils/              # 工具函数
│   │   └── index.js       # 通用工具函数
│   ├── App.jsx            # 主应用组件
│   ├── main.jsx           # 应用入口
│   └── index.css          # 全局样式
├── index.html             # HTML模板
├── vite.config.js         # Vite配置
├── package.json           # 项目配置
└── README.md              # 项目文档
```

## 快速开始

### 环境要求

- Node.js >= 16.0.0
- npm >= 8.0.0 或 yarn >= 1.22.0

### 安装依赖

```bash
# 使用 npm
npm install

# 或使用 yarn
yarn install
```

### 开发环境

```bash
# 启动开发服务器
npm run dev

# 或使用 yarn
yarn dev
```

访问 http://localhost:3000

### 生产构建

```bash
# 构建生产版本
npm run build

# 或使用 yarn
yarn build
```

### 预览构建结果

```bash
# 预览生产构建
npm run preview

# 或使用 yarn
yarn preview
```

## 配置说明

### API配置

在 `src/services/api.js` 中配置后端API地址：

```javascript
const api = axios.create({
  baseURL: '/api/admin',  // 开发环境通过Vite代理
  timeout: 10000,
});
```

### 代理配置

在 `vite.config.js` 中配置开发环境代理：

```javascript
export default defineConfig({
  server: {
    proxy: {
      '/api': {
        target: 'http://localhost:9808',
        changeOrigin: true,
        secure: false,
      },
    },
  },
});
```

## 默认登录信息

- **用户名**: admin
- **密码**: 123456

## 主要功能说明

### 仪表盘
- 显示系统核心指标统计
- 实时监控系统状态（数据库、Redis、MinIO）
- 资源使用率可视化（CPU、内存、磁盘）
- 用户、图片、积分详细统计

### 用户管理
- **列表查看**: 分页展示所有用户信息
- **搜索功能**: 支持按昵称、手机号、邮箱搜索
- **状态筛选**: 按用户状态筛选
- **用户详情**: 查看用户完整信息和积分详情
- **状态管理**: 启用/禁用用户账号
- **积分管理**: 为用户充值/扣除积分

### 图片管理
- **网格展示**: 以卡片形式展示图片
- **多维筛选**: 按公开状态、审核状态筛选
- **图片审核**: 审核通过/拒绝图片，支持填写拒绝原因
- **图片删除**: 删除不当图片（需确认）
- **详情查看**: 查看图片完整信息和元数据

### 统计分析
- **用户统计**: 总数、新增、活跃用户等多维度统计
- **图片统计**: 总数、处理成功率等统计
- **积分统计**: 发放、消费、利用率等统计
- **数据可视化**: 使用Ant Design的统计组件展示

## 开发指南

### 添加新页面

1. 在 `src/pages/` 下创建新的页面组件
2. 在 `src/App.jsx` 中添加路由配置
3. 在 `src/components/Layout/index.jsx` 中添加菜单项

### 添加新API

在 `src/services/api.js` 中添加新的API方法：

```javascript
// 示例：添加新的API方法
export const newApiMethod = (params) => {
  return api.get('/new-endpoint', { params });
};
```

### 自定义样式

在 `src/index.css` 中添加或修改全局样式，或在组件中使用内联样式。

## 性能优化

### Vite优势
- **快速启动**: 使用原生ES模块，无需打包即可启动
- **热更新**: 快速的热模块替换（HMR）
- **按需编译**: 只编译当前页面相关的模块
- **优化构建**: 基于Rollup的生产构建优化

### 代码分割
在 `vite.config.js` 中配置了代码分割：

```javascript
build: {
  rollupOptions: {
    output: {
      manualChunks: {
        vendor: ['react', 'react-dom'],
        antd: ['antd', '@ant-design/icons'],
        router: ['react-router-dom'],
        utils: ['axios', 'dayjs'],
      },
    },
  },
},
```

## 部署说明

### 构建部署

```bash
# 1. 构建项目
npm run build

# 2. 将 dist 目录部署到Web服务器
# 例如：nginx、apache等
```

### 环境变量

可以创建 `.env` 文件配置环境变量：

```env
VITE_API_BASE_URL=http://your-api-server.com
VITE_APP_TITLE=InspiRealm 管理后台
```

## 浏览器兼容性

- Chrome >= 87
- Firefox >= 78
- Safari >= 14
- Edge >= 88

## 开发工具

推荐使用以下开发工具：

- **IDE**: VS Code
- **插件**: 
  - ES7+ React/Redux/React-Native snippets
  - Prettier - Code formatter
  - ESLint
  - Auto Rename Tag
  - Bracket Pair Colorizer

## 故障排除

### 常见问题

1. **端口占用**: 修改 `vite.config.js` 中的端口配置
2. **API连接失败**: 检查代理配置和后端服务状态
3. **依赖安装失败**: 清除缓存后重新安装
   ```bash
   rm -rf node_modules package-lock.json
   npm install
   ```

### 调试技巧

- 使用浏览器开发者工具
- 查看控制台日志和网络请求
- 使用React Developer Tools扩展

## 更新日志

### v1.0.0 (2024-07-29)
- 🎉 项目初始化
- ✨ 完整的管理后台功能
- 🚀 使用Vite构建工具
- 🎨 现代化UI设计
- 📱 响应式布局支持

## 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

---

**InspiRealm Team**  
*让AI图片处理管理更简单* 🚀
