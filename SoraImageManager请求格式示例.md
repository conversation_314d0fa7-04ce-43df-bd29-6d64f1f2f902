# SoraImageManager 修复后的请求格式示例

## 修复前的问题
之前的请求格式是简单的键值对形式：
```json
{
    "prompt": "美化一下这种图片,加上 我爱中国 四个字 尺寸[4:3]",
    "imageUrl": "https://example.com/image.png",
    "aspectRatio": "4:3",
    "processType": "beautify",
    "textContent": "我爱中国",
    "style": "realistic"
}
```

## 修复后的正确格式
现在按照您要求的OpenAI消息格式：

### 使用图片URL的请求
```json
{
    "model": "sora_image",
    "messages": [
        {
            "role": "user",
            "content": [
                {
                    "type": "text",
                    "text": "美化一下这种图片,加上 我爱中国 四个字 尺寸[4:3] 风格：realistic 处理类型：beautify"
                },
                {
                    "type": "image_url",
                    "image_url": {
                        "url": "https://example.com/image.png"
                    }
                }
            ]
        }
    ]
}
```

### 使用Base64图片的请求
```json
{
    "model": "sora_image",
    "messages": [
        {
            "role": "user",
            "content": [
                {
                    "type": "text",
                    "text": "美化一下这种图片,加上 我爱中国 四个字 尺寸[4:3] 风格：realistic 处理类型：beautify"
                },
                {
                    "type": "image_url",
                    "image_url": {
                        "url": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA..."
                    }
                }
            ]
        }
    ]
}
```

### 只有文本的请求（无图片）
```json
{
    "model": "sora_image",
    "messages": [
        {
            "role": "user",
            "content": [
                {
                    "type": "text",
                    "text": "生成一张图片，内容：我爱中国 尺寸[4:3] 风格：realistic"
                }
            ]
        }
    ]
}
```

## 代码实现说明

### buildRequestBody方法
1. **设置模型**：固定为 "sora_image"
2. **构建消息数组**：包含一个用户消息
3. **构建内容数组**：
   - 文本部分：合并所有文本信息（prompt + textContent + aspectRatio + style + processType）
   - 图片部分：如果有图片URL或Base64，添加image_url类型的内容

### buildTextContent方法
智能合并所有文本信息：
- 基础prompt
- 文字内容（如果prompt中没有包含）
- 尺寸要求（如果prompt中没有包含）
- 风格要求
- 处理类型

### 兼容性
- 支持图片URL和Base64两种格式
- 自动检测prompt中是否已包含某些信息，避免重复
- 保持向后兼容，原有的SoraImageRequest参数结构不变

## 测试示例

### Java代码调用示例
```java
SoraImageManager.SoraImageRequest request = new SoraImageManager.SoraImageRequest();
request.setPrompt("美化一下这种图片,加上 我爱中国 四个字 尺寸[4:3]");
request.setImageUrl("https://example.com/image.png");
request.setAspectRatio("4:3");
request.setProcessType("beautify");
request.setTextContent("我爱中国");
request.setStyle("realistic");

// 调用处理方法
SoraImageManager.SoraImageResult result = soraImageManager.processImage(request);
```

### 生成的实际请求
上述Java代码会生成如下HTTP请求：
```json
{
    "model": "sora_image",
    "messages": [
        {
            "role": "user",
            "content": [
                {
                    "type": "text",
                    "text": "美化一下这种图片,加上 我爱中国 四个字 尺寸[4:3] 风格：realistic 处理类型：beautify"
                },
                {
                    "type": "image_url",
                    "image_url": {
                        "url": "https://example.com/image.png"
                    }
                }
            ]
        }
    ]
}
```

## 修复验证
✅ 请求格式符合OpenAI标准  
✅ 支持图片URL和Base64  
✅ 智能文本内容合并  
✅ 保持原有接口兼容性  
✅ 避免重复信息  

修复完成！现在SoraImageManager会按照正确的格式发送请求到sora_image模型。
