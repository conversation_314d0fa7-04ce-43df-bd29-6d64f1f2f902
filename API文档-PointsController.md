# PointsController API接口文档

## 接口概述
积分管理控制器，提供微信小程序用户的积分管理功能，包括积分余额查询、流水记录、每日签到等。

**基础路径**: `/api/wechat/points`  
**权限要求**: 需要微信用户登录（@WeChatAuth）

## 接口列表

### 1. 获取积分余额

#### 接口信息
- **接口路径**: `GET /api/wechat/points/balance`
- **接口描述**: 获取当前登录用户的积分余额信息
- **权限要求**: 微信用户登录

#### 请求参数
无需请求参数，通过Header传递Token

#### 响应结果
**成功响应**:
```json
{
    "code": 200,
    "message": "操作成功",
    "data": {
        "userId": 1,
        "totalPoints": 1000,
        "availablePoints": 800,
        "lockedPoints": 50,
        "consumedPoints": 150
    },
    "timestamp": 1690617600000
}
```

**响应参数说明**:
| 参数名 | 类型 | 说明 |
|--------|------|------|
| data.userId | Long | 用户ID |
| data.totalPoints | Integer | 总积分 |
| data.availablePoints | Integer | 可用积分 |
| data.lockedPoints | Integer | 锁定积分 |
| data.consumedPoints | Integer | 已消费积分 |

---

### 2. 分页查询积分流水

#### 接口信息
- **接口路径**: `GET /api/wechat/points/transactions`
- **接口描述**: 分页查询当前登录用户的积分流水记录
- **权限要求**: 微信用户登录

#### 请求参数
| 参数名 | 类型 | 必填 | 说明 | 默认值 |
|--------|------|------|------|--------|
| page | Integer | 否 | 页码 | 1 |
| size | Integer | 否 | 每页大小 | 10 |
| transactionType | Integer | 否 | 交易类型 1:获得 2:消费 3:锁定 4:解锁 5:回退 | 无 |

#### 响应结果
**成功响应**:
```json
{
    "code": 200,
    "message": "操作成功",
    "data": {
        "records": [
            {
                "id": 1,
                "transactionType": 1,
                "transactionTypeDesc": "获得积分",
                "pointsAmount": 10,
                "beforePoints": 90,
                "afterPoints": 100,
                "businessType": "DAILY_CHECKIN",
                "businessTypeDesc": "每日签到",
                "description": "每日签到奖励",
                "createTime": "2024-07-29 10:30:00"
            }
        ],
        "total": 50,
        "size": 10,
        "current": 1,
        "pages": 5
    },
    "timestamp": 1690617600000
}
```

**响应参数说明**:
| 参数名 | 类型 | 说明 |
|--------|------|------|
| data.records | Array | 流水记录列表 |
| data.records[].id | Long | 流水ID |
| data.records[].transactionType | Integer | 交易类型 |
| data.records[].transactionTypeDesc | String | 交易类型描述 |
| data.records[].pointsAmount | Integer | 积分数量 |
| data.records[].beforePoints | Integer | 变更前积分 |
| data.records[].afterPoints | Integer | 变更后积分 |
| data.records[].businessType | String | 业务类型 |
| data.records[].businessTypeDesc | String | 业务类型描述 |
| data.records[].description | String | 描述 |
| data.records[].createTime | String | 创建时间 |
| data.total | Long | 总记录数 |
| data.size | Long | 每页大小 |
| data.current | Long | 当前页码 |
| data.pages | Long | 总页数 |

---

### 3. 每日签到

#### 接口信息
- **接口路径**: `POST /api/wechat/points/daily-checkin`
- **接口描述**: 用户每日签到获取积分奖励
- **权限要求**: 微信用户登录

#### 请求参数
无需请求参数

#### 响应结果
**首次签到成功响应**:
```json
{
    "code": 200,
    "message": "签到成功",
    "data": {
        "success": true,
        "earnedPoints": 10,
        "totalPoints": 110,
        "consecutiveDays": 1,
        "alreadyCheckedIn": false,
        "checkinTime": "2024-07-29 10:30:00"
    },
    "timestamp": 1690617600000
}
```

**已签到响应**:
```json
{
    "code": 200,
    "message": "今日已签到",
    "data": {
        "success": false,
        "earnedPoints": 0,
        "totalPoints": 110,
        "consecutiveDays": 0,
        "alreadyCheckedIn": true,
        "checkinTime": null
    },
    "timestamp": 1690617600000
}
```

**响应参数说明**:
| 参数名 | 类型 | 说明 |
|--------|------|------|
| data.success | Boolean | 是否签到成功 |
| data.earnedPoints | Integer | 获得积分 |
| data.totalPoints | Integer | 签到后总积分 |
| data.consecutiveDays | Integer | 连续签到天数 |
| data.alreadyCheckedIn | Boolean | 是否已签到 |
| data.checkinTime | String | 签到时间 |

#### 业务逻辑
1. 检查用户今日是否已签到（通过Redis缓存）
2. 如果已签到，返回已签到状态
3. 如果未签到，增加用户积分并记录签到状态
4. 签到状态在Redis中缓存24小时

---

### 4. 获取签到状态

#### 接口信息
- **接口路径**: `GET /api/wechat/points/checkin-status`
- **接口描述**: 获取当前用户今日的签到状态
- **权限要求**: 微信用户登录

#### 请求参数
无需请求参数

#### 响应结果
**成功响应**:
```json
{
    "code": 200,
    "message": "操作成功",
    "data": {
        "hasCheckedIn": true,
        "checkinDate": "2024-07-29"
    },
    "timestamp": 1690617600000
}
```

**响应参数说明**:
| 参数名 | 类型 | 说明 |
|--------|------|------|
| data.hasCheckedIn | Boolean | 今日是否已签到 |
| data.checkinDate | String | 签到日期 |

---

## 积分系统业务规则

### 交易类型说明
| 类型码 | 类型名称 | 说明 |
|--------|----------|------|
| 1 | 获得积分 | 用户获得积分（签到、奖励等） |
| 2 | 消费积分 | 用户消费积分（图片处理等） |
| 3 | 锁定积分 | 积分被锁定（处理中） |
| 4 | 解锁积分 | 积分解锁（处理失败） |
| 5 | 回退积分 | 积分回退（系统退款） |

### 业务类型说明
| 业务码 | 业务名称 | 说明 |
|--------|----------|------|
| USER_REGISTER | 用户注册 | 新用户注册奖励 |
| DAILY_CHECKIN | 每日签到 | 每日签到奖励 |
| IMAGE_PROCESS | 图片处理 | 图片处理消费 |
| ADMIN_RECHARGE | 管理员充值 | 管理员手动充值 |
| ADMIN_DEDUCT | 管理员扣除 | 管理员手动扣除 |
| SYSTEM_REWARD | 系统奖励 | 系统自动奖励 |
| SYSTEM_REFUND | 系统回退 | 系统自动回退 |

### 积分安全机制
1. **预扣除机制**：图片处理前先锁定积分，防止重复消费
2. **事务保护**：所有积分操作都在数据库事务中执行
3. **并发控制**：使用数据库锁防止并发问题
4. **流水记录**：所有积分变动都有详细的流水记录

---

## 使用示例

### JavaScript示例（微信小程序）
```javascript
// 获取积分余额
wx.request({
    url: 'https://api.example.com/api/wechat/points/balance',
    method: 'GET',
    header: {
        'Token': wx.getStorageSync('token')
    },
    success: function(res) {
        if (res.data.code === 200) {
            console.log('积分余额', res.data.data);
        }
    }
});

// 每日签到
wx.request({
    url: 'https://api.example.com/api/wechat/points/daily-checkin',
    method: 'POST',
    header: {
        'Token': wx.getStorageSync('token')
    },
    success: function(res) {
        if (res.data.code === 200) {
            if (res.data.data.success) {
                wx.showToast({
                    title: `签到成功，获得${res.data.data.earnedPoints}积分`,
                    icon: 'success'
                });
            } else if (res.data.data.alreadyCheckedIn) {
                wx.showToast({
                    title: '今日已签到',
                    icon: 'none'
                });
            }
        }
    }
});

// 查询积分流水
wx.request({
    url: 'https://api.example.com/api/wechat/points/transactions',
    method: 'GET',
    data: {
        page: 1,
        size: 10,
        transactionType: 1 // 只查询获得积分的记录
    },
    header: {
        'Token': wx.getStorageSync('token')
    },
    success: function(res) {
        if (res.data.code === 200) {
            console.log('积分流水', res.data.data);
        }
    }
});
```

### cURL示例
```bash
# 获取积分余额
curl -X GET "https://api.example.com/api/wechat/points/balance" \
  -H "Token: abc123def456ghi789"

# 每日签到
curl -X POST "https://api.example.com/api/wechat/points/daily-checkin" \
  -H "Token: abc123def456ghi789"

# 查询积分流水
curl -X GET "https://api.example.com/api/wechat/points/transactions?page=1&size=10&transactionType=1" \
  -H "Token: abc123def456ghi789"
```

---

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 401 | 未登录或Token无效 |
| 500 | 用户积分信息不存在 |
| 500 | 积分不足 |
| 500 | 锁定积分不足 |
| 500 | 更新用户积分失败 |
| 500 | 记录积分流水失败 |

---

## 注意事项

1. **权限验证**: 所有接口都需要微信用户登录
2. **签到限制**: 每日只能签到一次，通过Redis缓存控制
3. **积分安全**: 所有积分操作都有完整的事务保护和流水记录
4. **分页限制**: 流水查询每页最多100条记录
5. **缓存策略**: 签到状态缓存24小时自动过期
6. **并发控制**: 使用数据库锁防止积分并发问题

---

**文档版本**: v1.0  
**创建时间**: 2024-07-29  
**更新时间**: 2024-07-29
