# UserController API接口文档

## 接口概述
用户管理控制器，提供微信小程序用户的个人信息管理功能。

**基础路径**: `/api/wechat/user`  
**权限要求**: 需要微信用户登录（@WeChatAuth）

## 接口列表

### 1. 获取用户资料

#### 接口信息
- **接口路径**: `GET /api/wechat/user/profile`
- **接口描述**: 获取当前登录用户的详细资料信息
- **权限要求**: 微信用户登录

#### 请求参数
无需请求参数，通过Header传递Token

**请求头**:
```
Authorization: Bearer {token}
或
Token: {token}
```

#### 响应结果
**成功响应**:
```json
{
    "code": 200,
    "message": "操作成功",
    "data": {
        "id": 1,
        "openId": "oABC123DEF456",
        "nickname": "张三",
        "avatarUrl": "https://wx.qlogo.cn/mmopen/xxx",
        "gender": 1,
        "country": "中国",
        "province": "广东",
        "city": "深圳",
        "phone": "13800138000",
        "email": "<EMAIL>",
        "status": 1,
        "pointsInfo": {
            "totalPoints": 1000,
            "availablePoints": 800,
            "lockedPoints": 50,
            "consumedPoints": 150
        },
        "createTime": "2024-07-29 10:30:00",
        "lastLoginTime": "2024-07-29 15:30:00"
    },
    "timestamp": 1690617600000
}
```

**响应参数说明**:
| 参数名 | 类型 | 说明 |
|--------|------|------|
| data.id | Long | 用户ID |
| data.openId | String | 微信OpenID |
| data.nickname | String | 用户昵称 |
| data.avatarUrl | String | 头像URL |
| data.gender | Integer | 性别 0:未知 1:男 2:女 |
| data.country | String | 国家 |
| data.province | String | 省份 |
| data.city | String | 城市 |
| data.phone | String | 手机号 |
| data.email | String | 邮箱 |
| data.status | Integer | 用户状态 0:禁用 1:正常 |
| data.pointsInfo | Object | 积分信息 |
| data.pointsInfo.totalPoints | Integer | 总积分 |
| data.pointsInfo.availablePoints | Integer | 可用积分 |
| data.pointsInfo.lockedPoints | Integer | 锁定积分 |
| data.pointsInfo.consumedPoints | Integer | 已消费积分 |
| data.createTime | String | 注册时间 |
| data.lastLoginTime | String | 最后登录时间 |

---

### 2. 更新用户信息

#### 接口信息
- **接口路径**: `PUT /api/wechat/user/info`
- **接口描述**: 更新当前登录用户的基本信息
- **权限要求**: 微信用户登录

#### 请求参数
```json
{
    "nickname": "李四",
    "avatarUrl": "https://wx.qlogo.cn/mmopen/yyy",
    "gender": 2,
    "phone": "13900139000",
    "email": "<EMAIL>"
}
```

**参数说明**:
| 参数名 | 类型 | 必填 | 说明 | 验证规则 |
|--------|------|------|------|----------|
| nickname | String | 否 | 用户昵称 | 最大50个字符 |
| avatarUrl | String | 否 | 头像URL | 最大500个字符 |
| gender | Integer | 否 | 性别 | 0:未知 1:男 2:女 |
| phone | String | 否 | 手机号 | 11位手机号格式 |
| email | String | 否 | 邮箱 | 邮箱格式，最大100个字符 |

#### 响应结果
**成功响应**:
```json
{
    "code": 200,
    "message": "更新成功",
    "data": {
        "id": 1,
        "openId": "oABC123DEF456",
        "nickname": "李四",
        "avatarUrl": "https://wx.qlogo.cn/mmopen/yyy",
        "gender": 2,
        "country": "中国",
        "province": "广东",
        "city": "深圳",
        "phone": "13900139000",
        "email": "<EMAIL>",
        "status": 1,
        "pointsInfo": {
            "totalPoints": 1000,
            "availablePoints": 800,
            "lockedPoints": 50,
            "consumedPoints": 150
        },
        "createTime": "2024-07-29 10:30:00",
        "lastLoginTime": "2024-07-29 15:30:00"
    },
    "timestamp": 1690617600000
}
```

**失败响应**:
```json
{
    "code": 500,
    "message": "手机号已被其他用户使用",
    "data": null,
    "timestamp": 1690617600000
}
```

#### 业务逻辑
1. 验证用户是否存在且状态正常
2. 检查手机号和邮箱是否被其他用户使用
3. 更新用户信息（只更新有变更的字段）
4. 返回更新后的完整用户资料

---

### 3. 获取用户基本信息

#### 接口信息
- **接口路径**: `GET /api/wechat/user/info`
- **接口描述**: 获取当前登录用户的基本信息（简化版）
- **权限要求**: 微信用户登录

#### 请求参数
无需请求参数，通过Header传递Token

#### 响应结果
**成功响应**:
```json
{
    "code": 200,
    "message": "操作成功",
    "data": {
        "id": 1,
        "nickname": "张三",
        "avatarUrl": "https://wx.qlogo.cn/mmopen/xxx",
        "gender": 1,
        "availablePoints": 800
    },
    "timestamp": 1690617600000
}
```

**响应参数说明**:
| 参数名 | 类型 | 说明 |
|--------|------|------|
| data.id | Long | 用户ID |
| data.nickname | String | 用户昵称 |
| data.avatarUrl | String | 头像URL |
| data.gender | Integer | 性别 0:未知 1:男 2:女 |
| data.availablePoints | Integer | 可用积分 |

---

## 使用示例

### JavaScript示例（微信小程序）
```javascript
// 获取用户资料
wx.request({
    url: 'https://api.example.com/api/wechat/user/profile',
    method: 'GET',
    header: {
        'Token': wx.getStorageSync('token')
    },
    success: function(res) {
        if (res.data.code === 200) {
            console.log('用户资料', res.data.data);
        } else {
            console.error('获取失败', res.data.message);
        }
    }
});

// 更新用户信息
wx.request({
    url: 'https://api.example.com/api/wechat/user/info',
    method: 'PUT',
    header: {
        'Token': wx.getStorageSync('token'),
        'Content-Type': 'application/json'
    },
    data: {
        nickname: '新昵称',
        phone: '13900139000',
        email: '<EMAIL>'
    },
    success: function(res) {
        if (res.data.code === 200) {
            console.log('更新成功', res.data.data);
        } else {
            console.error('更新失败', res.data.message);
        }
    }
});

// 获取用户基本信息
wx.request({
    url: 'https://api.example.com/api/wechat/user/info',
    method: 'GET',
    header: {
        'Token': wx.getStorageSync('token')
    },
    success: function(res) {
        if (res.data.code === 200) {
            console.log('用户基本信息', res.data.data);
        }
    }
});
```

### cURL示例
```bash
# 获取用户资料
curl -X GET "https://api.example.com/api/wechat/user/profile" \
  -H "Token: abc123def456ghi789"

# 更新用户信息
curl -X PUT "https://api.example.com/api/wechat/user/info" \
  -H "Token: abc123def456ghi789" \
  -H "Content-Type: application/json" \
  -d '{
    "nickname": "新昵称",
    "phone": "13900139000",
    "email": "<EMAIL>"
  }'

# 获取用户基本信息
curl -X GET "https://api.example.com/api/wechat/user/info" \
  -H "Token: abc123def456ghi789"
```

---

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 401 | 未登录或Token无效 |
| 500 | 用户不存在 |
| 500 | 用户状态异常，无法更新信息 |
| 500 | 手机号已被其他用户使用 |
| 500 | 邮箱已被其他用户使用 |
| 500 | 更新用户信息失败 |

---

## 注意事项

1. **权限验证**: 所有接口都需要微信用户登录，通过@WeChatAuth注解验证
2. **数据验证**: 更新接口有完整的参数验证，包括格式和长度限制
3. **唯一性检查**: 手机号和邮箱会检查是否被其他用户使用
4. **增量更新**: 只更新有变更的字段，提高性能
5. **数据安全**: 敏感信息如OpenID只在详细资料接口中返回
6. **日志记录**: 所有操作都有详细的日志记录

---

**文档版本**: v1.0  
**创建时间**: 2024-07-29  
**更新时间**: 2024-07-29
